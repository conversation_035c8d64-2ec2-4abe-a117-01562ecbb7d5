<template>
  <view class="order-item" @tap.stop="toPay">
    <view class="top">
      <view class="order-id">
        <text>订单号：{{ order.orderGroupId }}</text>
      </view>
      <view class="order-status" :style="{ color: onOrderColor(order.orderStatus) }">{{ orderStatus[order.orderStatus] }}</view>
    </view>
    <view class="middle">
      <view class="order-title">
        <y-font-weight>{{ order.productSkuName || order.productName }}</y-font-weight>
        &nbsp;
        <text>{{ ticketEnum[order.productType] }}</text>
      </view>
      <text class="order-num">X {{ order.num }}</text>
    </view>
    <view class="bottom">
      <text class="unit"
        >¥ <text class="money">{{ order.payAmount }}</text></text
      >
      <view
        class="commentBtn go-comment"
        v-if="order.ticketStatus == '3' && order.commentType == '0' && (order.orderStatus == 30 || order.orderStatus == 21 || order.orderStatus == 31 || order.orderStatus == 34)"
        @tap.stop="toComment"
        >去点评</view
      >
      <view
        class="commentBtn view-comment"
        v-else-if="
          order.ticketStatus == '3' && order.commentType == '1' && (order.orderStatus == 30 || order.orderStatus == 21 || order.orderStatus == 31 || order.orderStatus == 34)
        "
        @tap.stop="toCommentDetail"
        >查看点评</view
      >

      <text v-if="order.orderStatus == '20'" class="go-pay">去支付</text>
    </view>
  </view>
</template>

<script setup>
import { toRefs, reactive, ref } from "vue";
import { orderStatus, ticketType, goodsType } from "@/utils/constant.js";

const props = defineProps({
  order: {
    type: Object,
    default: () => ({}),
    required: true,
  },
});
const onOrderColor = (status) => {
  if (status == 20) {
    return "var(--theme-color)";
  } else if (status == 52) {
    return "#F43636";
  } else {
    return "#050505";
  }
};
const toComment = () => {
  Tool.goPage.push(
    `/pages/comment/postEvaluation?type=1&orderId=${props.order.orderGroupId}&goodName=${props.order.productSkuName}&scenicId=${props.order.scenicId}&scenicName=${props.order.scenicName}`
  );
};

const toCommentDetail = () => {
  Tool.goPage.push(
    `/pages/scenic/commentDetail?commentId=${props.order.commentId}&scenicName=${props.order.scenicName}&scenicId=${props.order.scenicId}&storeId=${props.order.storeId}`
  );
};

const toPay = () => {
  if (props.order.refundAmount) {
    Tool.goPage.push(`/pages/orderDetail/orderDetail?orderId=${props.order.orderGroupId}&refundStatus=${props.order.orderStatus}&refundId=${props.order.refundId}`);
  } else {
    Tool.goPage.push(`/pages/orderDetail/orderDetail?orderId=${props.order.orderGroupId}`);
  }
};

const ticketEnum = ref({
  ...ticketType,
  10: "组合票",
  11: "权益卡",
});
</script>

<style lang="scss" scoped>
.order-item {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 18rpx;
  background-color: #fff;

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    font-size: 26rpx;
    border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);

    .order-id {
      display: flex;
      flex: 1;
      width: 0;
      color: #14131f;

      text {
        @include text-truncate;
      }
    }

    .order-status {
      flex: none;
      margin-left: 20rpx;
      color: #050505;
    }
  }

  .middle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 25rpx 0 20rpx;

    .order-title {
      width: 450rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #131313;
    }

    .order-num {
      width: 150rpx;
      text-align: right;
      flex: none;
      align-self: flex-start;
      font-size: 26rpx;
      line-height: 45rpx;
      color: #050505;
    }
  }

  .bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .unit {
      display: inline-block;
      font-size: 28rpx;
      margin-right: 6rpx;
      color: #f43636;
      .money {
        font-weight: 600;
        font-size: 48rpx;
      }
    }
    .commentBtn {
      width: 150rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      font-size: 26rpx;
      border-radius: 28rpx;
    }
    .go-comment {
      background: #ff9201;
      color: #fff;
    }
    .view-comment {
      border: 2rpx solid #b9b9b9;
      color: #050505;
    }

    .go-pay {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ff9201;
      border-radius: 30rpx;
      padding: 10rpx 33rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #fff;
    }
  }
}
</style>
