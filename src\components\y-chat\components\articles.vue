<style lang="scss" scoped>
.goods {
	position: relative;
	flex: none;
	width: 274rpx;
	margin-right: 20rpx;
	overflow: hidden;
	background: #ffffff;
	border-radius: 12rpx;
	.label {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 81rpx;
		height: 32rpx;
		margin-bottom: 20rpx;
		color: #8e8e8e;
		color: #65300f;
		font-weight: 500;
		font-size: 28rpx;
		font-size: 22rpx;
		background: linear-gradient(270deg, #f1b896 0%, #f9ddc7 100%);
		border-radius: 3px;
	}
	.goods__img {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 158rpx;
		background: rgba(0, 0, 0, 0.1);
		.img {
				width: 100%;
				height: 158rpx;
			}
		
			.default-img {
				width: 100rpx;
			}
		}
		
		.bottom {
			padding: 20rpx;
		
			.title {
				margin-bottom: 10rpx;
				color: #14131f;
				font-weight: 500;
				font-size: 30rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		
			.price {
				color: #f43636;
				font-weight: 500;
				font-size: 26rpx;
			}
		
			.read {
				display: flex;
				align-items: center;
				color: #14131f;
				font-size: 24rpx;
				opacity: 0.5;
		
				image {
					width: 30rpx;
					height: 30rpx;
				}
			}
		}
		}
		
		.goods-title {
			margin: 40rpx 0 25rpx;
			color: #14131f;
			font-weight: 500;
			font-size: 32rpx;
		}
</style>
<template>
	<div style="margin-bottom: 40rpx;">
		<div v-if="data.goodsList && data.goodsList.length > 0">
			<div class="goods-title">商品推荐</div>
			<scroll-view scroll-x="true" style="width: 100%">
				<div style="display: flex">
					<div class="goods" v-for="item in data.goodsList" @click="goGoods(item)">
						<div class="label">{{ lableType[item.journeyGoodsSuggestType] }}</div>
						<!-- <div class="label">{{ item.journeyGoodsSuggestType }}</div> -->
						<div class="goods__img">
							<image class="img" v-if="item.pictureUrl" :src="item.pictureUrl" mode="aspectFill" />
							<image v-else style="width: 100rpx" src="@/static/image/default-img.png" mode="widthFix" />
						</div>
						<div class="bottom">
							<div class="title">{{ item.keyName }}</div>
							<div class="price">¥{{ item.lowestPrice }} 起</div>
						</div>
					</div>
				</div>
			</scroll-view>
		</div>
		<div v-if="data.articleList && data.articleList.length > 0">
			<div class="goods-title">文章推荐</div>
			<scroll-view scroll-x="true" style="width: 100%">
				<div style="display: flex">
					<div v-for="item in data.articleList" @click="goArticle(item)" class="goods">
						<div class="label">{{ lableType[item.articleType] }}</div>
						<div class="goods__img">
							<image class="img" v-if="item.pictureUrl" :src="item.pictureUrl" mode="aspectFill" />
							<image v-else style="width: 100rpx" src="@/static/image/default-img.png" mode="widthFix" />
						</div>
						<div class="bottom">
							<div class="title">{{ item.articleTitle }}</div>
							<div class="read">
								<image src="@/static/image/ai/eye-icon.png" mode="widthFix" /> {{ item.readCount }}
							</div>
						</div>
					</div>
				</div>
			</scroll-view>
		</div>
	</div>

</template>

<script setup>
const props = defineProps({
	type: {
		type: String,
		default: "planTip"
	},
	data: {
		type: Object,
		default: () => ({})
	}
})
const tipList = ref([
	"深圳一人游，有哪些必去景点？",
	"对于深圳 3 日游，有哪些必去的景点和推荐行程安排？",
	"深圳 2 日游，有什么必去的路线？"
])
const peopleList = ref([
	{
		name: "亲子",
		active: false
	},
	{
		name: "带爸妈",
		active: false
	},
	{
		name: "好友结伴",
		active: false
	},
	{
		name: "情侣/夫妻",
		active: false
	},
	{
		name: "蜜月",
		active: false
	}
])
const journeyList = ref([
	{
		name: "行程紧凑",
		active: false
	},
	{
		name: "行程宽松",
		active: false
	},
	{
		name: "地标打卡",
		active: false
	},
	{
		name: "地道美食",
		active: false
	}
])

const lableType = ref({
	COMBO_TICKET: "组合票",
	SCENIC: "景点",
	TRAVEL_CARD: "权益卡",
	1: "攻略",
	2: "资讯",
	3: "活动",
	4: "游记"
})

const goArticle = item => {
	if (item.externalUrl) {
		window.location.href = item.externalUrl.includes("https")
			? item.externalUrl.trim()
			: `https://${item.externalUrl.trim()}`
	} else {
		Tool.goPage.push(`/pages/articleDetail/articleDetail?id=${item.articleId}`)
	}

}
const goGoods = item => {
	const { journeyGoodsSuggestType } = item
	if (journeyGoodsSuggestType === "COMBO_TICKET") {
		Tool.goPage.push(`/pages/scenic/scenic?storeGoodsId=${item.keyId}`)
	}
	if (journeyGoodsSuggestType === "SCENIC") {
		Tool.goPage.push(`/pages/scenic/scenic?scenicId=${item.keyId}`)
	}
	if (journeyGoodsSuggestType === "TRAVEL_CARD") {
		Tool.goPage.push(
			`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.keyId}`
		)
	}
}

const onSelect = (type, index) => {
	if (type === "journeyList") {
		journeyList.value[index].active = !journeyList.value[index].active
		return
	}
	peopleList.value[index].active = !peopleList.value[index].active
}

const emits = defineEmits(["onSubmit"])

const onSubmit = isSubmit => {
	if (isSubmit) {
		const peopleStr = peopleList.value
			.filter(item => item.active)
			.map(item => item.name)
			.join("、")
		const journeyStr = journeyList.value
			.filter(item => item.active)
			.map(item => item.name)
			.join("、")
		const text = `出游天数：${params.value.day}，出游人数：${params.value.num}，人群类型：${peopleStr}，行程期待：${journeyStr}`
		emits("onSubmit", text)
	} else {
		emits("onSubmit", "")
	}
}

const params = ref({
	day: 1,
	num: 1
})
const onAdd = key => {
	params.value[key]++
}
const onsub = key => {
	if (params.value[key] > 1) {
		params.value[key]--
	}
}
</script>
