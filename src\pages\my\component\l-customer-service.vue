<template>
	<view v-if="btnList.length" class="service-box">
		<image @click="onShowInfo(e)" :class="{ 'btn-hide': hideBtn }" v-for="(e, i) in btnList" :key="i" class="btn"
			:style="{ right: 150 + i * 120 + 'rpx' }" :src="imgViewHost + e.label" mode="aspectFill"></image>
		<image @click="hideBtn = !hideBtn" class="service-btn" src="@/static/image/my/service-icon.png" mode=""></image>
	</view>
	<view v-if="boxInfo !== undefined" class="service-bg">
		<view class="service-pop">
			<view class="service-title">{{ boxInfo.type }}</view>
			<image v-if="boxInfo.attachment" class="service-qrcode" :src="imgViewHost + boxInfo.attachment" mode=""></image>
			<view v-if="boxInfo.contractWay" class="service-name">
				{{ boxInfo.contractWay }}
				<!-- <image class="copy" @click="copyData(boxInfo.contractWay)" src="@/static/image/my/copy-icon.png" mode=""></image> -->
			</view>
			<!-- <view class="service-tip">（请长按保存）</view> -->
			<uni-icons @click="boxInfo = undefined" type="close" class="close" color="#fff" size="40"></uni-icons>
		</view>
	</view>
</template>

<script setup>
import { toRefs, reactive, ref, onMounted } from "vue";
import { orderStatus } from "@/utils/constant.js";
import { copyText, getRoute } from "@/utils/tool.js";
import request from "@/utils/request.js";
import { getEnv } from "@/utils/getEnv";


const props = defineProps({
	order: {
		type: Object,
		default: () => { },
		required: true,
	},
});
const imgViewHost = ref(getEnv().VITE_IMG_HOST);

const btnList = ref([]);
const getServiceInfo = async () => {
	const storeId = getRoute.params().storeId;
	const { code, data } = await request.get(`/contractStaff/list/${storeId}`);
	btnList.value = data;
};

const hideBtn = ref(true);

const boxInfo = ref(undefined);

const onShowInfo = (info) => {
	boxInfo.value = info;
};

//一键复制
const copyData = (str) => {
	copyText(str);
};

onMounted(() => {
	getServiceInfo();
});
</script>

<style lang="scss" scoped>
.service-box {
	position: fixed;
	bottom: 236rpx;
	right: 0;
	z-index: 10;

	image {
		width: 90rpx;
		height: 90rpx;
	}

	.service-btn {
		width: 126rpx !important;
		height: 126rpx !important;
	}

	.btn {
		position: absolute;
		top: 50%;
		right: 100rpx;
		transform: translateY(-50%);
		border-radius: 50%;
		transition: 0.2s all;
	}

	.btn-hide {
		right: 0 !important;
		opacity: 0;
	}
}

.service-bg {
	position: fixed;
	top: 0%;
	left: 0%;
	right: 0%;
	bottom: 0%;
	background-color: rgba(1, 1, 1, 0.3);
	z-index: 10;

	.service-pop {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		justify-content: center;
		flex-direction: column;
		width: 500rpx;
		background: #ffffff;
		border-radius: 21rpx;
		padding: 36rpx 88rpx;
		text-align: center;

		.service-title {
			font-size: 42rpx;
			font-weight: 600;
			color: #14131f;
		}

		.service-qrcode {
			// margin-top: 36rpx;
			width: 324rpx;
			height: 324rpx;
		}

		.service-name {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 22rpx;
			font-size: 30rpx;
			font-weight: 400;
			color: #14131f;

			.copy {
				margin-left: 24rpx;
				width: 30rpx;
				height: 28rpx;
			}
		}

		.service-tip {
			margin-top: 6rpx;
			font-size: 26rpx;
			font-weight: 400;
			color: #14131f;
		}
	}

	.close {
		position: absolute;
		bottom: -150rpx;
		left: 50%;
		transform: translate(-50%, -50%);
	}
}
</style>
