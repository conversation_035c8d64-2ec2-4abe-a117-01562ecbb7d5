<template>
  <view class="comment-list-page">
    <l-comment-list :isPage="true" :scenicName="scenicName" :scrollTop="scrollTop" :initialTag="initialTag"></l-comment-list>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import lCommentList from './component/l-comment-list.vue';

// 获取路由参数
const scenicId = ref('');
const scenicName = ref('');
const initialTag = ref(null);

onLoad((option) => {
  scenicId.value = option.scenicId || '';
  scenicName.value = option.scenicName || '';
  // 设置页面标题
  if (scenicName.value) {
    uni.setNavigationBarTitle({
      title: `${scenicName.value}的点评`
    });
  }
  // 检查并读取本地缓存中的标签信息
  try {
    const selectedTag = uni.getStorageSync('selectedCommentTag');
    if (selectedTag) {
      initialTag.value = JSON.parse(selectedTag);
      // 读取后立即删除，防止重复使用
      uni.removeStorageSync('selectedCommentTag');
    }
  } catch(e) {
    console.error('读取评论标签缓存失败', e);
    uni.removeStorageSync('selectedCommentTag');
  }
});

const scrollTop = ref(0);
onPageScroll(e => {
  scrollTop.value = e.scrollTop
})
</script>

<style lang="scss" scoped>
.comment-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style>
