<template>
  <view class="tour-line-list">
    <view
      class="tour-line-list--item"
      v-for="(item, index) in lineList"
      :key="index"
      @click="lineClick(item)"
    >
      <image :src="imgSrc(item.publicizeUrl)" mode="aspectFill" />
      <view>
        <view class="title">{{ item.lineName }}</view>
        <view class="tips">
          <text>{{ item.pointNumber }}个点位</text>
          <text>{{ item.estimateHour }}小时</text>
          <text>{{ item.estimateDistance }}公里</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue';
import { getRoute, imgSrc } from "@/utils/tool.js";
  import request from "@/utils/request.js";

  const { guideId } = getRoute.params()
  const lineList = ref()
  const lineClick = (item) => {
    Tool.goPage.push(`/pages/tourLineDetail/tourLineDetail?guideId=${guideId}&id=${item.id}`)
  }
  request.get('/navigation/line/list', { scenicId: guideId }).then(({data}) => {
    lineList.value = data
  })
</script>

<style lang="scss" scoped>
  .tour-line-list {
    min-height: 100vh;
    background: #F9F9F9;
    overflow: auto;
    .tour-line-list--item {
      position: relative;
      margin: 30rpx auto;
      width: 690rpx;
      height: 388rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158,158,158,0.5);
      >image {
        width: 100%;
        height: 100%;
      }
      >view {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 108rpx;
        background: rgba(255,255,255,0.65);
        backdrop-filter: blur(3px);
        padding: 15rpx 30rpx;
        .title {
          font-size: 36rpx;
          font-weight: bold;
          color: #090909;
          line-height: 36rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .tips {
          margin-top: 10rpx;
          font-size: 24rpx;
          color: #535353;
          line-height: 33rpx;
          >text:not(:first-child) {
            margin-left: 20rpx;
          }
        }
      }
    }
  }
</style>