// import config from "./config.js";
// import localConfig from "./config.local.js";
// import devConfig from "./config.dev.js";
// import testConfig from "./config.test.js";
// import canaryConfig from "./config.canary.js";
// import prodConfig from "./config.prod.js";

// let newConfig = {};
// // 合并配置
// switch (process.env.MODE) {
// 	case "dev":
// 		newConfig = { ...config, ...devConfig };
// 		break;
// 	case "test":
// 		newConfig = { ...config, ...testConfig };
// 		break;
// 	case "canary":
// 		newConfig = { ...config, ...canaryConfig };
// 		break;
// 	case "prod":
// 		newConfig = { ...config, ...prodConfig };
// 		break;
// }

// //本地开发环境下，优先使用 config.local.js 配置
// if (process.env.NODE_ENV === "development") {
// 	newConfig = { ...newConfig, ...localConfig };
// }

// export default {
// 	...newConfig,
// };
