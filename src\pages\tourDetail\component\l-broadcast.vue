<template>
  <view @click="openPopup" class="b_avatar" :style="{ 'background-image': `url(${activeAvatar})` }" />

  <view :style="{ opacity: showBubble ? 1 : 0 }" class="b_bubble">可切换人物讲解</view>
  <uni-popup ref="popup" type="top">
    <view class="b_popup">
      <view class="b_title">人物</view>
      <view class="b_people">
        <view v-for="(item, index) in peopleList" :key="item.name" @click="setPeople(index)" class="b_people_item"
          :class="{ active: item.active }">
          <view class="b_people_avatar" :style="{
            'background-image': `url(${voiceTypeEnum[item.name]?.img})`,
          }" />
          <view class="b_people_name">{{ voiceTypeEnum[item.name]?.name }}</view>
        </view>
      </view>
      <view class="b_title">讲解</view>
      <view class="b_word-type">
        <view v-for="(item, index) in wordTypeList" :key="item.name" @click="setWordType(index)"
          class="b_word-type_item" :class="{ active: item.active }">{{ item.name }}</view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { wordTypeEnum } from "@/utils/constant";

import ai_voice_children from "@/static/image/ai/ai_voice_children.png";
import ai_voice_man1 from "@/static/image/ai/ai_voice_man1.png";
import ai_voice_man2 from "@/static/image/ai/ai_voice_man2.png";
import ai_voice_woman1 from "@/static/image/ai/ai_voice_woman1.png";
import ai_voice_woman2 from "@/static/image/ai/ai_voice_woman2.png";
import ai_voice_woman3 from "@/static/image/ai/ai_voice_woman3.png";
import ai_voice_woman4 from "@/static/image/ai/ai_voice_woman4.png";
const popup = ref();
const showBubble = ref(true);

const peopleList = ref([]);
const wordTypeList = ref([]);
const props = defineProps({
  detail: {
    type: Object,
    default: () => { },
  },
});
const emit = defineEmits(["setAiVoice"]);

const voiceTypeEnum = {
  白术: {
    name: "男声 1",
    img: ai_voice_man1,
  },
  神里绫人: {
    name: "男声 2",
    img: ai_voice_man2,
  },
  凯瑟琳: {
    name: "女声 1",
    img: ai_voice_woman1,
  },
  阿贝多2lanlan: {
    name: "女声 2",
    img: ai_voice_woman2,
  },
  八重神子2xingxing: {
    name: "女声 3",
    img: ai_voice_woman3,
  },
  琴: {
    name: "女声 4",
    img: ai_voice_woman4,
  },
  可莉: {
    name: "童声",
    img: ai_voice_children,
  },
  english_woman: {
    name: "女声",
    img: ai_voice_woman1,
  },
  english_man: {
    name: "男声",
    img: ai_voice_man1,
  },
};

// 设置人物
const setPeople = (index) => {
  const aiVoice = Tool.getAiVoice({ ...props.detail });

  const isEnglish = aiVoice.wordType == '2' // 英文
  // 设置人物列表
  if (isEnglish) {
    peopleList.value = [
      {
        name: 'english_woman',
        active: true
      },
      {
        name: 'english_man',
        active: false
      },
    ]
  } else {
    peopleList.value = props.detail.aiVoiceType.split(",").map((item) => {
      return {
        name: item,
        active: item === aiVoice.aiVoiceType,
      };
    });
  }

  // 手动切换
  if (index !== undefined) {
    const allAiVoice = JSON.parse(localStorage.getItem("aiVoiceType") || "{}");
    peopleList.value.forEach((item, i) => {
      if (i === index) {
        item.active = true;
        if (!allAiVoice[props.detail.id]) {
          allAiVoice[props.detail.id] = {};
        }
        allAiVoice[props.detail.id].aiVoiceType = item.name;

        localStorage.setItem("aiVoiceType", JSON.stringify(allAiVoice));
      } else {
        item.active = false;
      }
    });
  }

  setTimeout(() => {
    emit("setAiVoice");
  }, 100);

};
// 设置文案
const setWordType = (index) => {
  const aiVoice = Tool.getAiVoice({ ...props.detail });

  // 初始化
  if (!wordTypeList.value.length) {
    wordTypeList.value = props.detail.wordType.split(",").map((item) => {
      return {
        name: wordTypeEnum[item],
        key: item,
        active: item === aiVoice.wordType,  // 上次选中的
      };
    });
  }

  // 手动切换
  if (index !== undefined) {
    const allAiVoice = JSON.parse(localStorage.getItem("aiVoiceType") || "{}");
    wordTypeList.value.forEach((item, i) => {
      if (i === index) {
        item.active = true;
        if (!allAiVoice[props.detail.id]) {
          allAiVoice[props.detail.id] = {};
        }
        allAiVoice[props.detail.id].wordType = item.key;
        localStorage.setItem("aiVoiceType", JSON.stringify(allAiVoice));
      } else {
        item.active = false;
      }
    });
  }

  setPeople() // 设置人物

  // setTimeout(() => {
  //   emit("setAiVoice");
  // }, 100);
}

const setVoice = (fields, index) => {
  const allAiVoice = JSON.parse(localStorage.getItem("aiVoiceType") || "{}");

  if (fields === "aiVoiceType") {
    // 人物
    setPeople(false)
    peopleList.value.forEach((item, i) => {
      if (i === index) {
        item.active = true;
        if (!allAiVoice[props.detail.id]) {
          allAiVoice[props.detail.id] = {};
        }
        allAiVoice[props.detail.id][fields] = item.name;
        localStorage.setItem("aiVoiceType", JSON.stringify(allAiVoice));
      } else {
        item.active = false;
      }
    });
  } else {
    wordTypeList.value.forEach((item, i) => {
      if (i === index) {
        item.active = true;
        if (!allAiVoice[props.detail.id]) {
          allAiVoice[props.detail.id] = {};
        }
        allAiVoice[props.detail.id][fields] = item.key;
        localStorage.setItem("aiVoiceType", JSON.stringify(allAiVoice));
      } else {
        item.active = false;
      }
    });
  }

  // peopleList.value.forEach((item, i) => {
  //     if (i === index) {
  //         item.active = true
  //         fields
  //         if (allAiVoice[props.detail.id]) {
  //             allAiVoice[props.detail.id][fields] = item.name
  //         }
  //         localStorage.setItem('aiVoiceType', JSON.stringify(allAiVoice))
  //     } else {
  //         item.active = false
  //     }
  // })
};



const activeAvatar = computed(() => {
  const key = peopleList.value.find((item) => item.active)?.name || "白术";
  return voiceTypeEnum[key].img;
});

watch(
  () => props.detail,
  (val) => {
    if (val) {
      const aiVoice = Tool.getAiVoice({ ...val });
      setWordType()
      // setPeople() // 设置人物
      // let isEnglish = false
      // wordTypeList.value = val.wordType.split(",").map((item) => {
      //   if (aiVoice.wordType == '2') {
      //     isEnglish = true
      //   }
      //   return {
      //     name: wordTypeEnum[item],
      //     key: item,
      //     active: item === aiVoice.wordType,
      //   };
      // });
      // setPeople(isEnglish)

    }
  }
);

onMounted(() => {
  setTimeout(() => {
    showBubble.value = false;
  }, 8000);
});

const openPopup = () => {
  popup.value.open();
};
</script>

<style lang="scss" scoped>
.b_avatar {
  width: 72rpx;
  height: 72rpx;
  background: url("https://img.yzcdn.cn/vant/cat.jpeg") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.4rem;
  color: #fff;
  font-family: "YouSheBiaoTiHei";
  box-shadow: 0px 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);
  position: fixed;
  top: 30rpx;
  right: 30rpx;
}

.b_bubble {
  background-color: white;
  padding: 5rpx 12rpx;
  font-size: 24rpx;
  position: fixed;
  top: 122rpx;
  right: 18rpx;
  border-radius: 6rpx;

  transition: opacity 0.5s ease-in-out;
}

.b_popup {
  background: white;
  padding: 25rpx 34rpx;

  .b_title {
    font-size: 30rpx;
    margin-bottom: 28rpx;
    font-weight: bold;
    color: #0d0d0d;
  }

  .b_people {
    display: flex;
    flex-wrap: wrap;
    gap: 40rpx 70.5rpx;
    margin-bottom: 50rpx;

    .b_people_item {
      flex: none;
      text-align: center;
      display: flex;
      align-items: center;
      flex-direction: column;
      width: 80rpx;
      overflow: hidden;
      white-space: nowrap;

      &.active {
        .b_people_avatar {
          border: 1px solid #349fff;
        }

        color: #349fff;
      }

      .b_people_avatar {
        width: 80rpx;
        height: 80rpx;
        background: url("https://img.yzcdn.cn/vant/cat.jpeg") no-repeat;
        background-size: 100% 100%;
        background-position: center;
        border-radius: 50%;
      }

      .b_people_name {
        font-size: 28rpx;
        margin-top: 6rpx;
        width: 100%;
        white-space: nowrap;
      }
    }
  }

  .b_word-type {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx 26rpx;

    .b_word-type_item {
      width: 150rpx;
      height: 70rpx;
      font-size: 30rpx;
      color: #0d0d0d;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f4f4f4;
      border-radius: 4rpx;

      &.active {
        background: #edf7ff;
        border: 1px solid #349fff;
      }
    }
  }
}
</style>
