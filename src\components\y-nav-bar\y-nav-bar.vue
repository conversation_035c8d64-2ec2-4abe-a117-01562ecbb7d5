<template>
	<!-- 此导航栏只在微信端生效 -->
	<!-- #ifdef MP-WEIXIN -->
	<view class="nav-space" :style="{backgroundColor: backgroundColor, color: fontColor}">
		<view class="nav-bar" :style="navBarStyle">
			<view class="back" v-if="backBtn" @click="Tool.goPage.back()">
				<uni-icons type="back" :color="fontColor" size="20"></uni-icons>
			</view>
			<view class="title">
				<slot></slot>
			</view>
		</view>
	</view>
	<!-- 占位 -->
	<view :style="navBarStyle" v-if="props.solid"></view>
	<!-- #endif -->

	<view></view>
</template>
<script setup>
import { reactive, ref } from 'vue'

	const props = defineProps({
		//是否占空间
		solid: {
			type: Boolean,
			default: false
		},
		//背景颜色
		backgroundColor: {
			type: String,
			default: 'transparent'
		},
		fontColor: {
			type: String,
			default: '#fff'
		},
		//返回键
		backBtn: {
			type: Boolean,
			default: false
		}
	})
	// #ifdef MP-WEIXIN
	const menuButton = uni.getMenuButtonBoundingClientRect()
	const navStyle = `width: 100%;margin-top:${menuButton.top}px;height: ${menuButton.height}px;margin-bottom: 14rpx;`
	const navBarStyle = ref(navStyle)
	// #endif
	
	
</script>
<style lang="scss" scoped>
	.status-height{
		height: var(--status-bar-height);
	}
	.nav-space{
		// position: fixed;
		top: 0%;
		left: 0%;
		right: 0%;
		height: 3rem;
		background-color: transparent;
		overflow: hidden;
		z-index: 1000;
	}
	.nav-bar{
		position: relative;
		display: flex;
		height: 100%;
		justify-content: center;
		align-items: center;
		.back{
			position: absolute;
			left: 41rpx;
			width: 44rpx;
		}
	}
</style>