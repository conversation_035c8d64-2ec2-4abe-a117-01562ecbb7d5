import request from "@/utils/request.js";
import default_img from "@/static/image/tour/tour_default.svg";
import { marked } from "marked";
import "jweixin-module";
import { getEnv } from "@/utils/getEnv";

const { VITE_APPID, VITE_IMG_HOST, VITE_MAP_SDK } = getEnv();
const renderer = new marked.Renderer();

// 覆盖标题渲染方法
renderer.heading = function (text, level) {
  return `<h${level}>${text}</h${level}>`;
};

// 设置 marked 使用自定义渲染器
marked.setOptions({
  renderer: renderer,
});

// 系统页面
const designerConf = {
  home: "/pages/home/<USER>",
  tour: "/pages/tourList/tourList",
  order: "/pages/order/order",
  my: "/pages/my/my",
  ticketList: "/pages/ticketList/ticketList",
  travelCardList: "/pages/travelCardList/travelCardList?index=2",
  information: "/pages/information/information",
  travelNotes: "/pages/travelNotes/travelNotes",
};

// 跳转链接
export const goLink = ({ linkType, link, rightsId }) =>
  linkType == 8
    ? window.open(link)
    : goPage.push(
        {
          0: link,
          1: `/pages/home/<USER>
          2: `/pages/scenic/scenic?scenicId=${link}`,
          3: `/pages/book/book?storeGoodsId=${link}&orderType=single`,
          4: `/pages/scenic/scenic?storeGoodsId=${link}`,
          5: `/pages/travelCardDetail/travelCardDetail?storeGoodsId=${link}&rightId=${rightsId}`,
          6: `/pages/articleDetail/articleDetail?id=${link}`,
          7: `/pages/tour/tour?guideId=${link}`,
        }[linkType]
      );

// 图片显示
export const imgSrc = (v) => (v ? VITE_IMG_HOST + v : default_img);

// 直线距离
export const pointDistance = (p2) => {
  try {
    if (!(window.navFrom && p2?.[0] && p2?.[1])) return 0;
    const distance = TMap.geometry.computeDistance([
      new TMap.LatLng(...window.navFrom),
      new TMap.LatLng(...p2),
    ]);
    return Math.round(distance / 100) / 10 || 0;
  } catch (error) {
    formatPath;
    console.log(error);
    return 0;
  }
};

// 格式化时间
export const transformTime = (v) => {
  return (
    String(parseInt(v / 60)).padStart(2, "0") +
    ":" +
    String(Math.ceil(v % 60)).padStart(2, "0")
  );
};

// 注入微信权限验证配置
export const setJWeixin = (func) => {
  if (window.isJWeixin != undefined) return func && func();
  window.isJWeixin = false;
  request
    .get("/navigation/line/wx/sign", { ylbUrl: location.href.split("#")[0] })
    .then(({ data }) => {
      jWeixin.config({
        debug: false,
        appId: data.appId,
        timestamp: data.timestamp,
        nonceStr: data.noncestr,
        signature: data.sign,
        jsApiList: ["openLocation"], // JS 接口列表
      });
    });
  // 注入成功
  jWeixin.ready(() => {
    window.isJWeixin = true;
    func && func();
  });
  // 注入失败
  jWeixin.error((err) => {});
};

const formatPath = (params) => {
  let newParams = {};
  let storeId = "";
  // #ifdef H5
  storeId = getApp().globalData.storeId;
  // #endif
  // #ifdef MP-WEIXIN
  storeId = this.$scope.globalData.storeId;
  // #endif
  // if (!storeId) {
  // 	storeId = uni.getStorageSync("storeId")
  // }

  // 设计器枚举值
  if (designerConf[params]) {
    params = designerConf[params];
  }
  if (typeof params === "string") {
    if (params.indexOf("?") === -1) {
      newParams.url = `${params}?storeId=${storeId}`;
    } else {
      const p = params.split("?")[1];
      if (p.indexOf("storeId") === -1) {
        // 没有则添加
        newParams.url = `${params}&storeId=${storeId}`;
      } else {
        // 已有 storeId 就不用添加了
        newParams.url = `${params}`;
      }
    }
  } else {
    const path = params.url;
    newParams = { ...params };
    if (path.indexOf("?") === -1) {
      newParams.url = `${path}?storeId=${storeId}`;
    } else {
      const p = params.split("?")[1];
      if (p.indexOf("storeId") === -1) {
        // 没有则添加
        newParams.url = `${params}&storeId=${storeId}`;
      } else {
        // 已有 storeId 就不用添加了
        newParams.url = `${params}`;
      }
    }
  }

  // 添加失败回调
  newParams.fail = (err) => {
    goPage.replace("/pages/error/404");
  };
  return newParams;
};
const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : "0" + n;
};

/**
 * 时间戳转化为年 月 日 时 分 秒
 * number: 传入时间戳
 * format：返回格式，支持自定义，但参数必须与 formateArr 里保持一致
 */
export const formatTime = (number, format) => {
  let formateArr = ["Y", "M", "D", "h", "m", "s"];
  let returnArr = [];

  let date = new Date(number);
  returnArr.push(date.getFullYear());
  returnArr.push(formatNumber(date.getMonth() + 1));
  returnArr.push(formatNumber(date.getDate()));

  returnArr.push(formatNumber(date.getHours()));
  returnArr.push(formatNumber(date.getMinutes()));
  returnArr.push(formatNumber(date.getSeconds()));

  for (let i in returnArr) {
    format = format.replace(formateArr[i], returnArr[i]);
  }
  return format;
};

// 页面跳转
// 参数跟 uni 路由跳转一样，另外可以直接传 string path
export const goPage = {
  push(params) {
    // 保留当前页面，跳转到应用内的某个页面
    const obj = formatPath(params);
    uni.navigateTo(obj);
  },
  tab(params) {
    // 跳转到 tabBar 页面
    const obj = formatPath(`/pages/index/index?curTab=${params}`);
    uni.navigateTo(obj);
  },
  back(params) {
    // 关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPages() 获取当前的页面栈，决定需要返回几层。
    const obj = {
      delta: 1,
    };
    getCurrentPages()?.length > 1 ? uni.navigateBack(obj) : history.back();
  },
  replace(params) {
    // 关闭当前页面，跳转到应用内的某个页面
    const obj = formatPath(params);
    uni.redirectTo(obj);
  },
  reLaunch(params) {
    // 关闭所有页面，打开到应用内的某个页面。
    const obj = formatPath(params);
    uni.reLaunch(obj);
  },
};

//对象转 url 参数
export const objToUrlPath = (data) => {
  var _result = [];
  for (var key in data) {
    var value = data[key];
    if (value.constructor == Array) {
      value.forEach(function (_value) {
        _result.push(key + "=" + encodeURIComponent(_value));
      });
    } else {
      _result.push(key + "=" + encodeURIComponent(value));
    }
  }
  return _result.join("&");
};

export const urlParamsToObject = (urlString) => {
  console.log(
    "🚀 ~ file: tool.js:133 ~ urlParamsToObject ~ urlString:",
    urlString
  );

  if (!urlString.includes("?")) {
    urlString = `?${urlString}`;
  }

  const query = urlString.split("?")[1];
  const paramsArr = query.split("&");
  const paramsObj = {};

  for (const param of paramsArr) {
    const [key, value] = param.split("=");
    paramsObj[key] = value;
  }

  return paramsObj;
};

//获取当前路由、参数
export const getRoute = {
  params() {
    const url = window.location.href;
    const [_, paramsStr] = url.split("?");
    if (paramsStr) {
      const urlParams = {};
      paramsStr.split("&").forEach((item) => {
        const [key, value] = item.split("=");
        urlParams[key] = decodeURIComponent(value);
      });
      return urlParams;
    } else {
      return {};
    }
  },
  path() {
    const url = window.location.href;
    const [path, _] = url.split("#")[1].split("?");
    return path;
  },
};

// 退出登录 redirect 是否携带重定向参数
export const logOut = async (type) => {
  // 移除失效的 cookie
  Tool.removeCookie("Authorization");
  uni.showLoading({
    title: "易旅宝",
    mask: true,
  });
  const storeId = getRoute.params().storeId;
  let path = `/pages/login/login?storeId=${storeId}`;

  if (type === "redirect") {
    const [_, rePath] = location.href.split("#");
    console.log(location.href, rePath);
    if (rePath.indexOf("/loading") === -1)
      sessionStorage.setItem("redirectLogin", rePath);
  }
  uni.hideLoading();
  uni.clearStorage();
  console.log("path", path);

  goPage.push(path);
};

// 获取用户信息
export const getUserInfo = async () => {
  const { userInfo, realNameInfo } = getApp().globalData;
  if (Object.keys(userInfo).length > 0) {
    return { userInfo, realNameInfo };
  } else {
    const { data: userInfo } = await request.get(`/user/userSocial`);
    getApp().globalData.userInfo = userInfo;
    //获取实名
    const { data } = await request.get(
      `/orgStructure/realNameInfo/${userInfo.userId}`
    );
    getApp().globalData.realNameInfo = data;
  }

  if (data.cardNumber != "" && data.idNumber != "") {
    uni.setStorageSync("autonym", data);
  }
  return true;
};

//节流
export const throttle = (func, delay) => {
  let startTime = 0;
  return (...test) => {
    const endTime = new Date();
    if (endTime - startTime > delay) {
      startTime = endTime;
      return func(...test);
    }
  };
};

//防抖
export const debounce = (func, delay = 500) => {
  let timeout;
  return (...args) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
};

//获取随机好看的颜色，用于图片加载不出来时占位
export const getRandomColor = () => {
  const colorList = [
    "#df89b5",
    "#bdc2e8",
    "#e6b980",
    "#ffc3a0",
    "#dfe9f3",
    "#ff9a9e",
    "#fbc2eb",
    "#fad0c4",
    "#fda085",
    "#a1c4fd",
    "#fef9d7",
  ];
  const sub = Math.round(Math.random() * colorList.length - 1);
  return colorList[sub];
};

//是否 IOS 平台
export const isIos = () => {
  if (uni.getSystemInfoSync().platform == "ios") {
    return true;
  }
  return false;
};

// 通过经纬度获取地址
export const getAddressByTx = ({ lat, lng }) => {
  return new Promise((resolve, reject) => {
    request
      .get(`/comment/getAddress/${lat}/${lng}`)
      .then(({ data }) => {
        console.log("res.data.通过经纬度获取地址---");
        console.log(data);
        if (data.status === 0) {
          console.log(data.result);
          resolve(data.result);
        } else {
          console.log("调用失败");
          reject(data.message);
        }
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });

    // const baseUrl = import.meta.env.DEV
    // 	? "/fire/yms/api-map"
    // 	: "https://apis.map.qq.com"
    // const query = objToUrlPath({
    // 	location: lat + "," + lng,
    // 	key: VITE_MAP_SDK
    // })
    // uni.request({
    // 	url: baseUrl + "/ws/geocoder/v1/?" + query,
    // 	method: "GET",
    // 	success(res) {
    // 		console.log("res.data.通过经纬度获取地址")
    // 		console.log(res)
    // 		console.log(res.data)
    // 		if (res.statusCode === 200 && res.data.status === 0) {
    // 			console.log(res.data.result)
    // 			resolve(res.data.result)
    // 		} else {
    // 			console.log("调用失败")
    // 			reject(res.data.message)
    // 		}
    // 	},
    // 	fail(err) {
    // 		console.log("调用失败")
    // 		console.log(err)
    // 		reject(err)
    // 	}
    // })
  });
};

// 获取当前星期几
export const getWeekDate = (date) => {
  const now = new Date(date);
  const day = now.getDay();
  const weeks = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const week = weeks[day];
  return week;
};

//复制到剪切板
export const copyText = (data) => {
  const input = document.createElement("input");
  input.setAttribute("readonly", "readonly");
  input.setAttribute("value", data);
  document.body.appendChild(input);
  input.select();
  input.setSelectionRange(0, 9999);

  if (document.execCommand("copy")) {
    // true
    document.execCommand("copy");
    uni.showToast({
      title: "复制成功",
      icon: "none",
    });
  }
  document.body.removeChild(input);
};

//微信授权登录
export const wxAuthorize = (route) => {
  let state = getRoute.params().storeId;
  if (route) state += `,${route}`;
  state = encodeURIComponent(state);
  const redirect_uri = encodeURIComponent(location.origin + location.pathname);
  const appid = VITE_APPID;
  const callback_url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`;
  window.location.href = callback_url;
};

// 修改主题
const setCssVar = (prop, val, dom = document.documentElement) => {
  dom.style.setProperty(prop, val);
};

//  "#349fff"
// setCssVar("--theme-color", `#f87054`)

/**
 * 金额处理：保留几位小数，不四舍五入 (关于金额数值的处理用这个方法，以防金额计算出错)
 * @param num   金额
 * @param decimal   保留位数
 * @returns {string}
 */
export const moneyFormatDecimal = (num, decimal = 2) => {
  let number = num.toString();
  let index = number.indexOf(".");
  if (index !== -1) {
    number = number.substring(0, decimal + index + 1);
  } else {
    number = number.substring(0);
  }
  return parseFloat(num).toFixed(decimal);
};

/**
 * markdonw 转 html
 */
export const markdownToHtml = (content = "") => {
  let html = marked(content);
  html = html.replace(/<img/g, '<img style="max-width:100%;height:auto"');
  return html;
};
export const toolMarkdownToHtml = (content = "") => {
  let html = marked(content);
  html = html.replace(/<img/g, '<img style="max-width:100%;height:auto"');
  return html;
};
/**
 * 判断元素是否在可视区域
 * @param {*} element
 */
export function isInViewport(element) {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

// 单位换算
export function pxToRpx(num) {
  return num * 2 + "rpx";
}

/**
 * 脱敏处理
 * @param {*} str 要脱敏的数据
 * @param {*} type 类型：phone 手机号，idCard 身份证号，ticket 票号，name 姓名
 * @returns 脱敏过后的数据
 */
export function desensitization(str, type) {
  if (!str) return "";
  // 转字符串
  str = String(str);
  // 手机号
  if (type === "phone") return str.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
  // 身份证号
  if (type === "idCard")
    return str.replace(/(\d{3})\d{11}(\d{4})/, "$1*************$2");
  // 票号，取前 10 位跟后 9 位，中间用 ... 代替
  if (type === "ticket")
    return str.length < 20 ? `${str.slice(0, 10)}...${str.slice(-9)}` : str;
  // 姓名，只显示姓，其他用*代替
  if (type === "name") {
    if (str.length === 2) return str.substring(0, 1) + "*";
    if (str.length > 2) return str.substring(0, 1) + "*".repeat(str.length - 1);
    return str;
  }
}

export const hexToRgb = (hex) => {
  let r = 0,
    g = 0,
    b = 0;
  // 3 位十六进制颜色
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  }
  // 6 位十六进制颜色
  else if (hex.length === 7) {
    r = parseInt(hex[1] + hex[2], 16);
    g = parseInt(hex[3] + hex[4], 16);
    b = parseInt(hex[5] + hex[6], 16);
  }
  return `${r}, ${g}, ${b}`;
};

export const setCookie = async (tk) => {
  const res = await request.put(`/user/noAuth/setCookie/${tk}`);
  // 微信小程序不能携带 cookie，只能存在本地
  console.log("ressssssssssss");
  console.log(res);
  // #ifdef MP-WEIXIN
  const cookie = res.cookies[0];
  uni.setStorageSync("ylb-cookie", cookie);
  // #endif
};

/**
 *
 * @param time1 时间 "5:00"
 * @param time2 时间 "15:00"
 * @returns time1 < time2 返回 -1，否则 1
 */
export const compareTimes = (time1, time2) => {
  if (!time1 || !time2) return -1;
  const hours1 = parseInt(time1.split(":")[0], 10);
  const minutes1 = parseInt(time1.split(":")[1], 10);
  const hours2 = parseInt(time2.split(":")[0], 10);
  const minutes2 = parseInt(time2.split(":")[1], 10);

  if (hours1 === hours2) {
    return minutes1 === minutes2 ? 0 : minutes1 < minutes2 ? -1 : 1;
  }
  return hours1 < hours2 ? -1 : 1;
};
