<template>
	<y-nav-bar backgroundColor="#349FFF" backBtn solid>个人中心</y-nav-bar>
	<!-- 设置密码页面 -->
	<template v-if="currentPage === 'passwordPage'">
		<view class="personal-center">
			<view class="y-list" v-if="hasPwd">
				<view class="y-list-item">
					<input class="y-list-item-input" v-model="resetPwdParams.oldPassword" type="password" placeholder="请输入旧密码"
						:placeholder-style="placeholderColor" />
				</view>
			</view>
			<view class="y-list">
				<view class="y-list-item">
					<input class="y-list-item-input" v-model="resetPwdParams.newPassword" type="password" placeholder="请输入新密码"
						:placeholder-style="placeholderColor" />
				</view>
				<view class="y-list-item">
					<input class="y-list-item-input" v-model="resetPwdParams.reNewPassword" type="password" placeholder="确认新密码"
						:placeholder-style="placeholderColor" />
				</view>
			</view>
			<view class="y-fixed-bottom">
				<y-button @tap="onResetPwd" :disable="!canSetPwd">确定</y-button>
			</view>
		</view>
	</template>
	<!-- 设置昵称页面 -->
	<template v-else-if="currentPage === 'setNicknamePage'">
		<view class="personal-center">
			<view class="y-list">
				<view class="y-list-item">
					<input class="y-list-item-input" v-model="stase.userInfo.nickname" placeholder="请输入昵称"
						:placeholder-style="placeholderColor" />
				</view>
			</view>
			<view class="nichen">以英文字母或汉字开头，限 2-8 字符</view>
			<view class="y-fixed-bottom">
				<y-button @tap="setNickName" :disable="!stase.userInfo.nickname">确定</y-button>
			</view>
		</view>
	</template>
	<!-- 绑定手机页面 -->
	<template v-else-if="currentPage === 'bindPhonePage'">
		<l-bind-phone></l-bind-phone>
	</template>

	<!-- 结果页 -->
	<template v-else-if="currentPage === 'resultPage'">
		<view class="result-page">
			<image class="result-icon" :src="`@/static/image/my/set-suceess-icon${linkSuffix}.png`" mode="widthFix"></image>
			<view class="status">设置成功</view>
			<view class="tip">
				<text>{{ countDownTime }}</text> 秒自动返回登录页，
				<text @click="loginOut">立即返回</text>
			</view>
		</view>
	</template>
	<!-- 个人中心页面 -->
	<template v-else>
		<view class="personal-center">
			<view class="y-list">
				<view class="y-list-item">
					<view class="y-list-item-left">头&emsp;像</view>
					<view class="y-list-item-right" @click="chooseImage">
						<image class="y-list-item-avatar" v-if="stase.userInfo.avatar" :src="imgHost + stase.userInfo.avatar"
							mode="aspectFill"></image>
						<image class="y-list-item-avatar" v-else src="@/static/image/default-avatar.png" mode="aspectFill"></image>
						<uni-icons class="y-list-item-forward" type="forward"></uni-icons>
					</view>
				</view>

				<view class="y-list-item">
					<view class="y-list-item-left">昵&emsp;称</view>
					<view class="y-list-item-right" @click="
						toPage(
							'/pages/personalCenter/personalCenter?currentPage=setNicknamePage'
						)
						">
						<text>{{ stase.userInfo.nickname }}</text>
						<uni-icons type="forward"></uni-icons>
					</view>
				</view>

				<view class="y-list-item" @tap="
					toPage(
						'/pages/personalCenter/personalCenter?currentPage=bindPhonePage'
					)
					">
					<view class="y-list-item-left">手机号</view>
					<view class="y-list-item-right">
						<text>{{ stase.userInfo.phone || "" }}</text>
						<uni-icons type="forward"></uni-icons>
					</view>
				</view>

				<view class="y-list-item" @tap="
					toPage(
						'/pages/personalCenter/personalCenter?currentPage=passwordPage'
					)
					">
					<view class="y-list-item-left">设置密码</view>
					<view class="y-list-item-right">
						<uni-icons type="forward"></uni-icons>
					</view>
				</view>
				<view class="y-list-item" @click="bindWx">
					<view class="y-list-item-left">绑定微信</view>
					<view class="y-list-item-right">
						<text>{{ wxUnionId ? "解绑微信" : "未绑定" }}</text>
						<uni-icons type="forward"></uni-icons>
					</view>
				</view>
			</view>

			<view class="y-fixed-bottom">
				<y-button v-show="showLoginBtn" :disable="false" @tap="loginOut">退出登录</y-button>
			</view>
		</view>
	</template>
	<y-confirm-modal :modelVisit="wxBingVisit.isShow" @confirm="wxBingConfirm" @cancel="wxBingVisit.isShow = false">
		{{ wxBingVisit.content }}
	</y-confirm-modal>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import { logOut, getRoute, wxAuthorize } from "@/utils/tool.js"
import request from "@/utils/request.js"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"
import { onShow, onLoad } from "@dcloudio/uni-app"
import { getEnv } from "@/utils/getEnv";

const { VITE_IMG_HOST, VITE_UPLOAD_HOST } = getEnv();
const imgHost = ref(VITE_IMG_HOST)
const stase = reactive({
	userInfo: {},
	isAutonym: false
})

const hasPwd = ref(false)

const wxBingVisit = reactive({
	isShow: false,
	content: "",
	type: ""
})

const currentPage = ref("")

const showLoginBtn = ref(true)

//临时用于换绑的 unionId
const unionIdByBing = ref("")

const placeholderColor = computed(() => {
	return { color: "#9397A8" }
})

const linkSuffix = computed(() => {
	return ""
})

const toPage = url => {
	Tool.goPage.push(url)
}
//手机号绑定微信
const onBindWechat = ({ unionId, code }) => {
	uni.showLoading({
		mask: true,
		title: "绑定中"
	})
	request
		.post("/user/bindWechat", {
			unionId,
			code
		})
		.then(res => {
			wxUnionId.value = unionId
			uni.setStorage("unionId", unionId)
			console.log('===unionId===', unionId)
			wxBingVisit.isShow = false
			setTimeout(() => {
				uni.hideLoading()
				uni.showToast({
					mask: true,
					icon: "none",
					title: "绑定成功"
				})
				location.reload()
			}, 500);
			Tool.goPage.replace('/pages/personalCenter/personalCenter?currentPage=accountPage')
		})
}
//确认
const wxBingConfirm = () => {
	switch (wxBingVisit.type) {
		case "bindWx":
			onBindWechat({
				unionId: unionIdByBing.value,
				code: getRoute.params().code
			})
			break
		case "unBindWx":
			uni.showLoading({
				mask: true,
				title: "解绑中"
			})
			request.put("/user/unbindWechat").then(() => {
				uni.showToast({
					mask: true,
					icon: "none",
					title: "解绑成功"
				})
				wxUnionId.value = ""
				uni.removeStorage({
					key: "unionId"
				})
				wxBingVisit.isShow = false
			})
			break
	}
}
//查询微信是否已绑定手机号
const isBingMobile = async unionId => {
	const params = {
		credential: unionId
	}
	const { credential } = await request.casGet("/checkCredential", params)
	if (credential === unionId) {
		return true
	} else {
		return false
	}
}

// 微信绑定状态
const wxUnionId = ref("")
//绑定解绑微信
const bindWx = () => {
	if (wxUnionId.value) {
		//已绑定
		wxBingVisit.isShow = true
		wxBingVisit.content = "当前账号已经绑定微信，是否确定解绑微信？"
		wxBingVisit.type = "unBindWx"
	} else {
		//未绑定
		wxAuthorize("/pages/personalCenter/personalCenter?currentPage=accountPage")
	}
}

//退出登陆
const loginOut = async () => {
	uni.showLoading({ mask: true })
	try {
		await request.put(`/user/logout`, {
			appId: uni.getStorageSync("appId")
		})
		logOut()
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

//倒计时
const countDownTime = ref(3)
const countDown = () => {
	const timer = setTimeout(() => {
		if (countDownTime.value === 0) {
			loginOut()
		} else {
			countDownTime.value = countDownTime.value - 1
			countDown()
		}
	}, 1000)
}

const resetPwdParams = reactive({
	newPassword: "",
	oldPassword: "",
	reNewPassword: ""
})
const canSetPwd = computed(() => {
	if (hasPwd.value) {
		return (
			resetPwdParams.newPassword &&
			resetPwdParams.oldPassword &&
			resetPwdParams.reNewPassword
		)
	} else {
		return resetPwdParams.newPassword && resetPwdParams.reNewPassword
	}
})
//重置密码
const onResetPwd = async () => {
	if (!canSetPwd.value) return
	if (resetPwdParams.reNewPassword !== resetPwdParams.newPassword) {
		uni.showToast({
			icon: "none",
			title: "两次密码不一致"
		})
		return
	}
	const params = {
		newPassword: w_md5.hex_md5_32(resetPwdParams.newPassword)
	}
	// 如果有旧密码
	if (hasPwd.value) params.oldPassword = w_md5.hex_md5_32(resetPwdParams.oldPassword)

	const { code, data, message } = await request.casPut(
		"/password/update",
		params,
		{ intercept: false }
	)
	console.log(code, data, message)
	if (code === 20000) {
		Tool.goPage.push("/pages/personalCenter/personalCenter?currentPage=resultPage")
		countDown()
	} else {
		uni.showToast({
			icon: "none",
			title: message
		})
	}
}

//选择图片上传
const chooseImage = () => {
	uni.chooseImage({
		count: 1, //默认 9
		sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
		sourceType: ["album", "camera"], //从相册选择
		success: function (res) {
			const imgObj = res.tempFiles[0]
			const imgSize = imgObj.size / 1024 / 1024
			const oSize = 0.5 //图片大小限制 (MB)
			const ocale = oSize / imgSize
			const tempFilePaths = res.tempFilePaths[0]
			uni.showLoading({ mask: true, title: "图片压缩" })
			compressImage(tempFilePaths, { quality: ocale }).then(res => {
				uni.showLoading({ mask: true, title: "图片上传" })
				uni.uploadFile({
					url: VITE_UPLOAD_HOST,
					filePath: res, // 使用压缩后的图片的本地临时路径
					name: "file",
					success: async uploadFileRes => {
						console.log(uploadFileRes)
						const imgObj = JSON.parse(uploadFileRes.data)[0]
						const avatar = imgObj.path
						const { code, data } = await request.casPost("/user/editBasic", {
							avatar
						})
						stase.userInfo.avatar = avatar
						uni.hideLoading()
					},
					fail: err => {
						console.log(err)
					}
				})
			})

			// uni.showLoading({ mask: true })
			// const uploadTask = uni.uploadFile({
			// 	url: VITE_UPLOAD_HOST,
			// 	filePath: tempFilePaths[0],
			// 	name: "file",
			// 	success: async uploadFileRes => {
			// 		const imgObj = JSON.parse(uploadFileRes.data)[0]
			// 		const avatar = imgObj.path
			// 		const { code, data } = await request.casPost("/user/editBasic", {
			// 			avatar
			// 		})
			// 		stase.userInfo.avatar = avatar
			// 		uni.hideLoading()
			// 	}
			// })
		},
		fail: function (err) {
			console.log("fail====")
			console.log(err)
		}
	})
}
// 图片分辨率压缩
const calcImageSize = (res, pixels) => {
	let imgW, imgH
	imgW = res.width
	imgH = res.height

	let ratio
	if ((ratio = (imgW * imgH) / pixels) > 1) {
		ratio = Math.sqrt(ratio)
		imgW = parseInt(imgW / ratio)
		imgH = parseInt(imgH / ratio)
	} else {
		ratio = 1
	}

	return { imgW, imgH }
}
// 压缩图片
const compressImage = (imgUrl, options = {}) => {
	/*************** 参数默认值 ***************/
	const MAX_PIXELS = 2000000 // 最大分辨率，宽 * 高 的值
	const MAX_QUALITY = 0.8 // 压缩质量
	const IMG_TYPE = "jpg"
	const CANVAS_ID = "compress_canvas"
	const BASE_64 = false

	return new Promise((resolve, reject) => {
		uni.getImageInfo({
			src: imgUrl,
			success: res => {
				console.log("getImageInfo")
				console.log(res)
				let pixels = options.pixels || MAX_PIXELS
				let quality = options.quality || MAX_QUALITY
				let type = options.type || IMG_TYPE
				let canvasId = options.canvasId || CANVAS_ID
				let isBase64 = options.base64 || BASE_64

				let { imgW, imgH } = calcImageSize(res, pixels)
				let w = options.width || imgW
				let h = options.height || imgH
					// #ifdef H5
					; (type = type == "jpg" ? "jpeg" : type),
						// #endif

						console.log(
							`%c 宽：${w} %c 高：${h} %c 分辨率：${w * h
							} %c 质量：${quality} %c 类型：${type}`,
							"color:#f00",
							"background-color:#f60;color:#fff",
							"color:#F00",
							"background-color:#f60;color:#fff",
							"color:#F00"
						)

				// #ifdef H5
				let img = new Image()
				img.src = res.path
				img.onload = () => {
					const canvas = document.createElement("canvas")
					const ctx = canvas.getContext("2d")
					canvas.width = w
					canvas.height = h
					let drawW = w,
						drawH = h

					ctx.drawImage(img, 0, 0, drawW, drawH)

					let base64 = canvas.toDataURL(`image/${type}`, quality)
					resolve(base64)
				}
				// #endif
			}
		})
	})
}

// 设置昵称
const setNickName = async () => {
	const regex = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5a-zA-Z]{1,7}$/
	if (!regex.test(stase.userInfo.nickname)) {
		uni.showToast({
			icon: "none",
			title: "昵称不符合要求"
		})
		return
	}
	await request.casPost("/user/editBasic", {
		nickname: stase.userInfo.nickname
	})
	uni.navigateBack({
		delta: 1
	})
}

onLoad(({ hidden_btn }) => {
	if (hidden_btn === "1") {
		showLoginBtn.value = false
	}
})
let userData = {}
const init = async (isReload) => {
	userData = await Tool.getUserInfo(isReload)
	const { userInfo, realNameInfo } = userData
	stase.userInfo = userInfo
	hasPwd.value = !!userInfo.password
	console.log('000', userInfo.unionId);

	wxUnionId.value = userInfo.unionId
	if (!Tool.isEmpty(realNameInfo)) {
		//已实名
		stase.isAutonym = true
	}

	const { code } = getRoute.params()
	currentPage.value = getRoute.params().currentPage
	if (currentPage.value === "resultPage") {
		countDown()
	}
	if (code) {
		//微信登录
		uni.showLoading({
			mask: true,
			title: "登录中"
		})
		const params = {
			code,
			type: 1
		}
		const {
			data: { union_id }
		} = await request.casGet("/wechat/login", params)
		request
			.casGet(
				"/checkCredential",
				{
					credential: union_id
				},
				{ intercept: false }
			)
			.then(res => {
				uni.hideLoading()
				if (res.code === 20000) {
					//绑定了手机号，弹窗提示是否换绑
					wxBingVisit.isShow = true
					wxBingVisit.content =
						"该微信已绑定其他账号，是否需要解绑之后再绑定当前账号？"
					wxBingVisit.type = "bindWx"
					unionIdByBing.value = union_id
				} else if (res.code === 50002) {
					//此微信未绑定手机号
					onBindWechat({
						code,
						unionId: union_id
					})
				} else {
					uni.showToast({
						mask: true,
						icon: "none",
						title: res.message
					})
				}
			})
	}
}
onShow(async () => {
	await init()
})
</script>
<style lang="scss" scoped>
.personal-center {
	overflow: hidden;
	height: 100%;
	background-color: #f6f6f6;

	.group {
		margin-bottom: 20rpx;
		background-color: #fff;

		>.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 00 40rpx;
			height: 100rpx;
			font-size: 34rpx;
			font-weight: 400;

			&.avatar-h {
				height: 120rpx;
			}

			>.left {
				color: #040404;
			}

			>.right {
				display: flex;
				align-items: center;
				color: #050505;

				.avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
				}

				.more {
					margin-left: 15rpx;
				}

				.un-real-name {
					color: #f43636;
				}

				.real-name {
					width: 60rpx;
					height: 60rpx;
				}
			}
		}

		>.line {
			display: block;
			margin-left: 40rpx;
			height: 1rpx;
			width: 100%;
			background-color: rgba(177, 177, 177, 0.39);
		}
	}
}

.result-page {
	background-color: #fff;
	min-height: 100vh;
	text-align: center;

	.result-icon {
		margin-top: 245rpx;
		margin-bottom: 12rpx;
		width: 238rpx;
		height: 238rpx;
	}

	.status {
		margin-bottom: 20rpx;
		font-size: 48rpx;
		font-weight: 600;
		color: #060131;
		line-height: 48rpx;
	}

	.tip {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
		line-height: 26rpx;

		text {
			color: #3c93ff;
		}
	}
}

.nichen {
	margin-left: 40rpx;
	font-size: 24rpx;
	font-weight: 400;
	color: #d86666;
}
</style>
