# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- 添加店铺信息支持，更新登录页面展示逻辑和工具函数
- 在订单合并数据中添加系统来源支持
- 提交分支名,提交信息，提交信息管理议题id功能 
- 更新 Vite 配置文件中的端口号为 1998，并修改提交信息脚本中的项目路径为 scenic/3-yilvtong。  
- 添加全局悬浮按钮及拖拽功能，优化用户交互体验 
- 替换全局悬浮按钮为返回原系统按钮，优化样式和交互体验 
- 重构 App.vue，优化全局返回按钮的创建逻辑，动态加载 vConsole，提升用户体验 
- 添加微信登录处理逻辑，优化 App.vue 中的初始化流程 
- 优化 App.vue 中的系统来源参数处理，改进返回按钮逻辑，使用解码后的 source_url 进行跳转 
- 更新 scenic.vue 以支持不同来源类型的展示逻辑，添加 scenicMap 组件并调整 sourceType 处理 
- 更新 scenicMap 组件，添加地图加载逻辑和边界处理，优化 scenic.vue 中的样式和布局 
- 调整 scenic.vue 和 scenicMap.vue 中的高度计算，优化布局以适应不同屏幕 
- 在 pages.json 中添加 scenicByTravelai 页面配置，更新导航栏标题 
- 新增分支名和提交信息检查功能，优化提交信息管理，添加 husky 钩子脚本以验证提交信息和分支命名规范。 
- 新增分支名和提交信息检查功能，优化提交信息管理，支持合并提交信息解析和议题ID提取 
- 在 scenicMap 组件中添加点位弹窗功能，优化地图点位聚合和距离更新逻辑，增强用户交互体验 
- 修改登录页自动登录逻辑，使用 expire 替代 isAutoLogin 变量 
- 添加 AI 页面并更新聊天组件逻辑 
- 升级新版ai聊天界面 
- 更新环境配置，调整 AI 基础 URL 以简化路径并修复组件导入路径 
- 更新 AI 页面逻辑，添加天气信息获取功能并优化 API 路径 
- 在 AI 聊天组件中添加 sceincName 参数支持，优化页面跳转逻辑并删除不必要的样式文件 
- 移除 scenicMap 组件中点位点击事件处理逻辑，点击点位时不再执行任何操作 
- 优化 scenic.vue 和 scenicByTravelai.vue 组件，移除条件渲染和调整样式 
- 在多个组件中添加 scenicId 属性，优化页面跳转逻辑并引入自定义点位列表功能 
- 移除 y-chat 组件中的过渡效果，简化结构 
- 在 scenicMap 组件中添加页面显示时地图重新初始化的逻辑，优化地图管理 
- 优化地图边界计算逻辑，增加边界扩展功能以提升用户体验 
- 优化 scenicMap 组件中的地图和聚合点管理逻辑，添加销毁实例和清理全局变量的功能，以提升性能和用户体验 
- 优化 App.vue 中的系统来源参数获取逻辑，支持从路由参数和本地缓存读取 
- 在 createBackButton 函数中添加延迟隐藏文本和调整样式的功能，以提升用户体验 
- 修改 dialogue.vue 组件，调整 flagList 属性类型为数组并更新默认值，同时在内容处理逻辑中添加换行符转换为 HTML 标签的功能 
- 所有环境的天气接口使用同一个 
- 在 .env.canary 文件中添加 AI 相关的 URL 配置，包括基础 URL、聊天、语音识别、文本转语音和路线规划服务的 WebSocket 地址 
- 更新 AI 组件中的头像图片和样式，调整设置组件的边框样式，优化 scenicByTravelai.vue 中的间距和布局 #281 #279 #278 
- 修改个人中心页面的导航逻辑，使用 Tool.goPage.push 替代 uni.navigateTo，并修复手机号绑定逻辑中的条件判断，确保正确性 
- 在日历组件中添加价格列表支持，更新价格显示逻辑，确保选择日期时能正确获取对应价格 
- 添加导览讲解功能，支持语音播放和状态管理 
- 更新语音合成功能，使用新的 TTS API 实现语音播放，并修改提示语内容 
- 更新l-tab组件，修改价格显示格式，移除不必要的类型定义，简化代码逻辑 
- 在导览功能中添加调试日志，优化停止讲解的逻辑 
- 重构导览页面音频播放组件，替换为新的音频播放器并优化样式 
- 优化导览讲解功能，重构音频播放逻辑，添加文本更新事件处理 #374 
- 在音频播放组件中添加错误处理，播放失败时显示提示信息 
- 禁用气泡点击事件以优化地图交互体验 
- 用远程husky替换本地husky 
- 静态开发增加tab 
- 评价静态开发 
- 笑脸点击逻辑开发 
- 笑脸和星星静态逻辑开发 
- 静态开发完成 
- 添加景点评分和评论功能的静态展示 
- 将景区票列表功能抽离成组件 
- 添加用户评价静态界面 
- 优化用户点评组件，添加标签展开功能和评论内容展开收起逻辑 
- 添加全部用户点评页面 
- 更新用户点评组件，修改图片路径，优化评论展示逻辑，添加底部固定按钮 
- 添加获取评论的接口 
- 开发遮罩组件以及假如动画 
- 对接新增评论 
- 增加新增评论校验 
- 点评规则跳转增加 
- 完成点评对接
- 联调优化 
- 传景区id联调 
- 添加文件上传接口配置及景区评分概览功能 
- 优化标签筛选 
- 添加点评详情页面 
- 景区页面添加tab吸顶效果，评论列表标签添加收起展开功能 #372 
- Travalai 景区界面添加用户点评功能 
- 添加吸顶筛选栏和滚动位置控制，优化评论列表展示 
- 添加图片预览功能 
- 完成点评成功后跳转逻辑 
- 添加图片查看详情弹窗组件，添加用户点评列表页标签吸顶功能 #378 
- 在评论列表中添加初始标签支持，优化标签选择逻辑 
- 优化景区页面的票务和评论列表布局，添加动态滚动位置控制和图片预览功能 
- 评论新增视频上传功能和视频展示功能 
- 视频功能修改优化 
- 视频上传优化 
- 添加景区打卡相关页面 
- 优化城市搜索功能，添加高亮显示搜索关键词，调整城市列表展示逻辑 
- 调整城市搜索页面样式，优化输入框布局和清除图标显示逻辑 
- 添加首字母锚点功能 
- 优化评论详情和评论列表组件，调整游览时长显示和标签筛选逻辑 #392 #394 
- 隐藏打卡功能 
- 更新线上支付收银台地址 
- 新增搜索热词自定义配置和展示 
- 替换支付tradeNo 
- 新增搜索历史页面 
- 新增历史展示等接口对接 
- 新增搜索历史和删除等对接 
- 视频上传功能完成
- 开发全局搜索 
- 配置全局搜索的项跳转 
- 全局搜索联调 
- 增加自动热词搜索对接 
- 子码掩码逻辑开发 
- 添加打卡成功页面和打卡失败弹窗组件，优化打卡流程和用户体验 
- 优化页面跳转和URL参数获取规则;重构图片上传逻辑，简化代码结构 #463 
- 重构城市搜索页面，优化搜索框布局和打卡功能，新增定位信息获取逻辑 
- 更新腾讯地图SDK，对接景区打卡接口 
- 移除未使用的定位逻辑，优化代码结构 
- 更新打卡地图页面，新增用户信息展示和添加打卡按钮，优化地图加载逻辑和样式 
- 新增我的打卡UI界面，新增用户信息展示和打卡记录时间轴 
- 手动打卡对接接口 
- 更新打卡页面，优化用户信息展示 和打卡统计逻辑，新增打卡数据获取功能 
- 更新评论列表和打卡成功页面，优化接口调用和代码格式 
- 实名认证子码掩码 
- 优化字码掩码逻辑 
- 更新打卡页面图标，优化图片资源路径 
- 增强打卡地图功能，添加当前位置标记和聚合点处理逻辑 
- 新增退出评论时弹出挽留弹窗 
- 更新打卡成功页面，添加图片删除功能及评分显示 
- 在打卡页面添加空状态显示 
- 5分钟内连续打卡，提示打卡频繁 
- Ui问题优化 
- 打卡地图标记点添加地址显示 
- 易旅宝登录页面逻辑理顺开发 
- 旧搜索框兼容新全局搜索接口 
- 实名认证提示弹窗开发 
- 添加分享功能及弹窗，优化打卡成功页面交互体验 
- 在分享弹窗中添加保存图片功能，优化用户体验并引入html2canvas库 
- 在打卡成功页面和分享弹窗中优化分享数据结构，增强用户信息展示 
- 在打卡成功页面中添加地图截图功能，并优化分享数据结构以支持截图 
- 在打卡成功页面中移除TMap相关功能，新增静态地图 
- 更新打卡成功页面中的静态地图标记图标，优化地图展示效果 
- 在分享弹窗中添加图片转换为base64功能，优化用户头像和分享图片展示 
- 封装图片转base64的组件 
- 添加打卡弹窗 
- 完成打卡地图分享功能开发 
- 更新pnpm锁定文件，添加html2canvas和其他依赖项 
- 添加权益卡tab功能 
- 添加权益票UI样式的修改优化 
- 更新权益票页面，添加权益景区和权益说明功能 
- 新增权益景区详情页，对接接口 #521 
- 页面数据刷新 
- 权益说明升级富文本 
- 信息提交审批页优化 
- Debug 
- 更新打卡功能，新增打卡名称和图片更新逻辑 
- 优化分享弹窗，简化图片处理逻辑并改善保存提示 
- 优化打卡列表展示，调整样式并增加图片预览功能 
- 更新打卡功能，增加地址拼接逻辑以优化打卡请求 
- 优化分享弹窗，增加图片转换和保存提示逻辑 
- 移除CI/CD相关脚本和配置文件 
- 移除CI/CD相关配置文件，清理src目录下gitlab-ci部署脚本和Docker配置 
- 升级Vite版本从4.1.4到6.3.5，更新相关依赖包 
- 降级Vite版本从6.3.5到4.5.14，修复sass版本依赖冲突 
- 重构环境配置，移除.env文件，新增动态环境配置工具函数 
- 添加Prettier配置，统一代码格式化标准并更新环境变量引用 
- 启用husky pre-commit钩子并统一代码格式化为单引号风格 
- 我的打卡-年份titile添加吸顶功能 
- 更新我的打卡页面样式和图标，优化背景渐变效果 
- 优化图片处理逻辑，添加弹窗状态管理和图片准备状态监听 
- 公用枚举配合联调 
- 获取订单类型枚举开发 
- 获取接口枚举优化 
- 添加 ESLint 配置和缓存文件 
- 更新Prettier配置，添加VSCode设置和cspell配置 
- 更新ESLint配置包 
- 优化联系人组件代码结构和样式 
- 优化y-magiccube组件代码结构和样式 
- 优化y-number组件props定义和默认值 
- 优化y-provinces组件代码结构和样式 
- 优化y-segmentation组件代码结构和样式 
- 优化y-step组件代码结构和样式 
- 优化y-swiper组件代码结构和样式 
- 优化y-tab-control组件代码结构和样式 
- 优化y-titletext组件代码结构和样式 
- 优化y-verification-code组件代码结构和样式 
- 优化articleDetail组件代码结构和样式 
- 优化l-calendar组件代码结构和样式 
- 优化l-tourist-info组件代码结构和样式 
- 优化linkman组件代码结构和样式 
- 优化custom-waterfalls-flow组件代码结构和样式 
- 优化y-travel-card组件代码结构和样式 
- 优化ticketPackageInfo组件代码结构和样式 
- 优化certification页面代码结构和样式 
- 优化l-banner组件代码结构和样式 
- 优化l-information组件代码结构和样式 
- 优化l-menu组件代码结构和样式 
- 修改文件的类型定义 
- 优化l-tab组件代码结构和样式 
- 优化ETicket页面代码结构和样式 
- 优化忘记密码输入组件代码结构和样式 
- 优化l-information组件条件渲染顺序 
- 修改shop项目中判断条件写法 
- 优化l-single-ticket组件导入路径 
- 添加定时器判空逻辑 
- 更新pnpm-lock文件 
- 添加定时器变量优化定位逻辑 

### 🐛 Bug Fixes

- 测试
- 在下单页添加对权益卡的支持
- 测试 
- 调整 scenicByTravelai.vue 中的 marginTop 样式，简化条件逻辑以提升代码可读性 
- 修复ESLint错误
- 解决日历列表没有回显价格的bug 
- 在音频播放组件中添加隐藏气泡事件的发送逻辑 
- 更新价格获取逻辑，处理价格列表为空的情况并修改默认价格返回值 
- 优化评论图片的展示 
- 联调修改字段 
- Config
- 联调字段变更 
- 图片问题优化以及判断新增评论
- 优化 
- 优化评分显示和评论标签字段，调整空内容提示样式 
- 添加评分字段显示 
- 景区id传过来 
- 优化评分和评论数量显示逻辑，添加空内容提示 
- 优化查看更多评论逻辑 
- 点评校验修改 
- 解决组合票页面，预订日历空白问题 
- 修复评分系统 
- 优化评论标签显示逻辑，添加条件判断以避免显示零值标签 
- 优化评论标签样式，增加间距和溢出处理 
- 添加商家回复多条消息的展示，优化展开全文按钮 
- 增加评论列表的触摸事件处理，优化排序功能和底部按钮显示逻辑 
- 查看点评修改 
- 改为全局枚举，修改问题 
- 优化出游信息显示逻辑，使用全局枚举替代硬编码，移除冗余函数 
- 优化评分和点评显示逻辑，增加条件判断以处理无点评情况 
- Icon图样式修复 
- 评论成功后跳回订单页
- 点评弹框已完成开发 
- 修改评论列表显示逻辑，优化跳转链接以包含类型参数 
- 景区门票-评论数量调整为4个 
- 优化日历组件和订单信息组件，增加调试日志，重构常用联系人获取逻辑 #398  
- 浏览时长问题修复 
- 出游类型问题修复 
- 浏览时长弹框逻辑修复 
- 点评页，标签问题汇总修复 
- 修复问题 
- 点评成功后跳转逻辑 
- 评价页面景区名称未回显 
- 点评问题修复 
- 修复旅行卡片列表为空时的错误提示逻辑 
- 优化获取景区数据的请求参数格式 
- 修改页面跳转逻辑，使用返回功能替代直接跳转 
- 类型判断配合解决 
- 更新默认头像路径并优化用户姓名脱敏处理逻辑 
- 添加评论详情和评论列表的文本自动换行样式 
- 标签问题优化 
- 点击弹窗问题优化 
- 优化权益卡列表UI 
- 格式化代码
- 版本景区点评内容样式修改 
- 优化评论列表中的箭头图标切换逻辑，更新SVG图标 
- 解决权益卡审核页面请求参数报错问题，添加申请理由输入框字数显示 
- 上传图片修改样式 
- 视频上传以及抽屉优化 
- 修正评论详情中游览类型文本的索引逻辑 
- 视频展示调整 
- Debug  
- 视频拿首帧优化 
- 合并并修改 
- 信息掩码问题修复 
- 优化定时器倒计时 
- 搜索栏X按钮添加 
- 视频问题优化以及层级修复 
- 优化展示层级 
- 将地图点位去重 
- 优化，阻止默认行为 
- 为用户信息添加默认昵称处理逻辑 
- 解决定位404问题 
- 解决我的打卡里景点超出隐藏问题 
- 优化搜索参数构建逻辑，确保城市名称正确传递 
- 修正打卡类型判断逻辑 
- 优化打卡异常流程 
- 修改打卡成功后的页面跳转逻辑，优化评论检查条件 
- 调整文案 
- 更新删除打卡记录的提示文案，增加确认和取消按钮文本 
- 优化评论列表组件，调整箭头图标和展开逻辑 
- 将隐藏底部按钮交互改成判断滚动条 
- 添加通过经纬度获取地址的请求逻辑，处理成功与失败的响应 
- 用户取消订单状态不应该显示：去点评按钮 
- 解决icon丢失问题 
- 弹框优化 
- 优化定位逻辑，解决一直定位中的问题；处理1km弹窗问题 #455  
- 修复定位信息输出，确保成功回调中包含位置信息  
- 优化定位功能，增加超时处理逻辑，确保在定位失败时使用景区默认地址；调整打卡提示文案 
- 更新定位功能，增加城市名称和城市编码的处理逻辑，优化UI显示，确保在定位中状态下的用户体验 
- 手动打卡不展示点评模块 
- 挽留弹窗没有关闭的问题修复 
- 用户点评时没有上传视频，带视频评价却显示为1 
- 优化获取景区列表的价格处理逻辑，确保价格为0时返回正确值 
- 景区打卡UI修复 
- 用户点评UI优化 
- 回归验证0627版本景区点评内容修复 
- 隐藏弹框 
- 优化退票功能，新增退票资格检验逻辑及UI状态处理 
- 更新退票按钮显示逻辑，增加退票可行性检查 
- 点评二次跳转问题修复 
- 热词配置为空时，易旅宝应该屏蔽为空的背景框 
- 背景登录颜色跟原型不一致 523
- 高价底价排序调整 
- 搜索框文本调整 
- 优化搜索框搜索 
- 修正组件路径错误并删除未使用的分享弹窗组件 
- 调整标记大小，调整透明度 
- Test 
- 合并 
- 更新包 
- 修复票列表渲染逻辑，确保仅在有票时显示票项 
- 修正二维码链接格式 
- 修正静态地图 URL 生成逻辑，调整缩放级别并添加调试信息 
- 解决分享地图初始化完成后空白的问题 
- 搜索框样式和首页保持一致 
- UI 
- 添加初始化loading 
- 修正打卡点数据中的城市名称为点位名称 #534 
- 优化代码格式，调整模板和逻辑结构，添加空状态提示 
- 调整打卡数据展示格式，增加城市和地点计数的空格 
- 添加旅游卡禁用逻辑，优化评论和票务组件的交互 
- 权益景区详情内点评内容有数据但是没展示出来 
- 修改旅游卡组件和详情页中的商品名称字段，优化错误日志输出格式 
- 优化定位超时处理逻辑，确保定时器正确清理 
- 添加页面显示时重置滚动位置的逻辑，优化用户体验 
- 搜索框样式和首页保持一致优化 
- 优化页面滚动位置计算 
- 优化页面加载和显示时的滚动位置处理，增加状态重置逻辑 
- 修改y-list组件ticket默认值为函数返回空对象 
- 优化approvalDetail图片列表渲染逻辑 
- 修复articleDetail图片路径和背景色问题 
- 修复忘记密码页面条件渲染和路径问题 

### 🚜 Refactor

- 移除当前位置标记相关逻辑，简化地图功能 

### 📚 Documentation

- Auto-generate changelog [ci-skip] 

### ⚙️ Miscellaneous Tasks

- 优化 Jenkinsfile 配置并修复环境变量引用 

## [release-v4.2.0] - 2025-04-03

### 🚜 Refactor

- *(y-magiccube)* 避免直接修改原始对象，使用新对象进行操作
- 统一网关，更新API基础路径并移除无用配置  #XINFANSHI-836

## [release-v4.1.1] - 2025-03-21

### 🐛 Bug Fixes

- #CSZ-341 修复tabBar的问题

## [release-v4.1.0] - 2025-03-11

### 🚀 Features

- 优化初始化逻辑
- Add tke
- 没有店铺自动跳转默认店铺
- 金额计算优化
- 添加生产默认店铺ID
- 权益卡审核跳转优化
- 权益卡列表添加关键字搜索
- Debug
- 仅限本人购买的票下单时自动回填
- 人脸设别添加轮训验证
- 票列表有效日期长存
- 今日不可入园的票不可选今天日期
- 删除无用的登录页
- 添加  vscode 插件建议
- 添加 unocss
- 添加缺省页
- New feat
- 将拖拽组件封装成hook
- Refactor dialogue component styles
- 首页
- 活动资讯
- 攻略游记
- 意见反馈
- 购买验证该实名是否已下单
- 添加 debug 功能
- 添加请求唯一标识符
- 修复票号过长不换行的问题
- 添加票二维码组件
- 更新二维码组件
- 开发人脸补录功能
- 添加人脸提示弹窗功能
- 修复人脸提示弹窗显示问题
- 人脸录入提示弹窗改成阻塞
- 订单详情页，实名信息有人脸显示人脸
- 优化联系人列表页面加载性能
- 移除订单号显示
- 优化初始化数据获取逻辑
- 全局替换用户信息跟实名信息获取方式
- Ai 咨询聊天页
- Ai咨询页文章推荐跟商品推荐
- Ai域名更改
- Ai咨询页优化
- 添加默认店铺跳转
- 设计器商品列表改动
- 优化初始化加载
- 行程偏好优化，前文提及的内容不在出现在偏好中
- 易旅宝-门票预定-景区列表-商品列表兼容分时开发
- 景区门票购买兼容分时时段
- 门票列表--组合票兼容分时
- Ai
- Ai导览升级优化
- Ai 导览跟ai行程兼容
- 请求函数添加ignoreStatusCode参数
- 登录页、忘记密码主题功能
- 优化渲染高亮逻辑
- 行程规划新增展开收起，tutu的气泡样式优化
- 修复登录失效时，没有正确跳转到登陆页的问题
- 替换 GitLab CI 配置为 Jenkinsfile 以支持新的构建和部署流程
- 添加区块链证书页面及订单详情跳转功能
- 添加证书下载功能及动画效果
- 优化证书下载功能，使用新方法打开下载链接
- 优化联系人和票务信息组件，简化代码结构和提升可读性
- 添加票务数量选择组件，优化订单信息页面的交互体验
- 优化套票信息组件，添加实名制支持及集合票处理逻辑
- 优化套票信息组件，简化代码结构并提升可读性
- 优化票务日期处理逻辑，根据集合时间开关动态获取游玩日期

### 🐛 Bug Fixes

- 区块链交易展示
- Test
- Bug
- 添加浏览器导航栏
- Test 订单列表票数
- *(订单列表)* 显示数量
- 修复同一订单中的两张门票的二维码一样
- 修复退单问题
- 修复头像不显示问题
- 订单详情多个供应商bug
- 票详情票号展示 bug
- 订单列表页的组合票添加票名称
- 微信端去除导航
- 支付回调慢，增加等待状态
- 去除接收方区块链账号
- 分时下单 bug
- 修复非分时没库存bug
- 下单添加 isComposeSimpleGood 字段
- 下单接口删减字段
- 重构
- 重构审核相关逻辑
- 修改文案
- 出票规则字段修改
- Prod
- 优化待支付倒计时
- 权益卡/票下单时添加字段
- 购买权益票添加身份证字段
- 下单添加实名校验
- 优化下单前的状态判断
- Merge branch 'canary' into dev
- 修复刷新页面时报错问题
- 统一获取路由参数的方式
- 支付去除检测实名的验证
- 添加 canary 环境 loginUrl 环境变量
- 将项目改造成 cli 模式
- 替换环境变量
- 修复检票二维码
- 退票
- 二维码添加判断
- 重构下单选择日期代码
- 统一返回字段 list 为 data
- 权益票不可选择数量
- 优化下单页传参
- 修复下单添加按钮无法添加问题
- 修复退单报错
- 优化重定向跳转
- 重定向
- 修复环境变量 VITE_APPID 读取不到的问题
- 优化联系人提示
- 修复退票勾选问题
- Merge branch 'dev' into test
- 权益卡隐藏退票按钮
- 解决精度问题
- 升级 dcloudio
- 移除 unocss
- 解决收藏页面渲染问题
- 智慧导览卫星图、缩放工具、用户定位、天气
- 位置监听
- 定位精度范围
- 聚合点位
- 自定义聚合点位
- 聚合点位性能优化
- 交互点位持久化
- 点位弹窗
- 事件交互解耦
- 弹窗层级、交互解耦
- 导览列表、Tab
- 聚合样式算法、点位详情、路线列表、路线详情
- 路线详情、路线地图、聚合样式
- 路线弹窗、点位导航
- 位置点位、导航
- 罗盘功能、航线实例优化
- 音频播放、点位类型枚举
- 搜索
- 菜单图标
- 区分点位类型、弹窗
- 优化 tutu 拖动效果
- 处理录音静默时间问题
- Dev
- 第三方地图导航、播放器 bug、点位背景
- 点位详情吸附
- 播放器
- 点位弹窗指令
- 详情轮播
- 路线详情、菜单交互
- 缓存解析
- 罗盘定位
- 点位列表
- 修改权益卡分页列表字段
- Tab 交互样式、全局默认图片
- 定位超时
- 定位调试
- 播放时长、推荐距离
- 无图点位不可点击、tab 容差
- Icon 大小
- Svg 矢量图
- 屏蔽 AI
- 第三方导航调试
- 公众号安全域名配置文件
- 暂存
- 更改权限卡显示错误
- 注销首页搜索
- 修复不过
- 修复样式
- 阅读量
- 外链
- 外链增加阅读量
- 样式修复
- 修改图片样式
- Z- index
- Z-index
- Markdown
- Merge canary
- Merge
- 地图同步加载、点位弹窗指令、直线距离
- 聚合算法、搜索算法、移除防抖
- 点位排序
- 路线点位、地图点位距离异步更新
- 导览新列表、聚合防抖、聚合动画
- 按需计算点位距离
- 更新默认矢量图
- 首页tab缓存所有请求
- 修改枚举值
- 下单实名验证修复
- 移除defineProps导入
- 导航栏
- 地图层级
- 移除https
- 修复新增意见反馈缺少picture参数
- 进入景区范围提示
- 天气矢量图标
- 点位弹窗层级、优化直线距离
- 更新景区信息接口
- 音频播放器
- *(2984)* 修复意见反馈图片上传样式
- 优化点位搜索算法、智能讲解
- 全局音频销毁
- 智能讲解触发同步
- 微信签名
- 签名调试
- Jssdk
- IsJWeixin
- 打开微信位置
- 打开微信位置 jWeixin
- Flex-end
- 路线详情交互动画
- 动画时长
- 天气位置
- 导航拦截
- 路线点位定位
- 添加剩余次数
- GuideId
- 修改权益卡过期判断
- *(2908)* 优化样式
- 修复下单成功后不跳转
- Svg 插件
- 动态渲染 tab
- 店铺导航
- 店铺导航支持自定义图标
- Svg 插件 path
- 新增 tab 路由组件
- Tab
- 链接路由
- 导航页面路由
- 链接设置 - 自定义链接
- 单票、组合票、权益卡详情链接
- Svg 调整
- 图文导航样式调整
- 样式调整
- 图文导航指示器
- Refactor orderDetailFooter.vue and refund.vue components
- 导航栏主题、商品组件
- Tab 组件区域滚动、上拉加载
- 活动页
- 准生产环境图床环境变量
- ReturnableQuantity
- 添加订单状态
- 修复活动资讯类型异常
- Update logo
- 解决标签样式问题
- NFT 标签
- NFT 标签、订单发行信息
- 智旅链查询
- 发行信息
- 1
- 溯源记录
- 智旅链链接
- 组合票门票溯源
- 优化订单部分
- 解决单票实名与非实名展示bug
- 修复订单详情页跳转问题
- 修复票二维码组件状态判断错误的问题
- 修复订单详情页剩余时间显示错误的问题
- 修复购买日期选择错误的问题
- 修复二维码组件的值绑定问题
- 修复二维码覆盖层层级问题
- 修复人脸跳转穿透问题
- 修复订单详情页的人脸跳转按钮传参
- Canary 区块链链接
- UI
- 修复身份证信息显示问题https://git.shukeyun.com/scenic/workflow/-/issues/3909
- 修改已核销次数显示位置
- 更新产品下拉列表接口
- 解决初始化loading问题
- 替换退票接口
- 修复退票的请求方式
- 优化登录问题
- AI 行程规划
- 行程规划
- Pull test
- 修复跳转问题
- 聊天框markdown样式优化、语音播放功能
- 增加回复中不可提问的限制
- 优化录音体验，松开按钮停止录音，识别完成发送
- 连接中不可发送消息
- 地点导航规划
- 修复序号异常
- 优化录音各种异常处理
- 行程状态管理
- 只读交互
- 优化录音不给
- 行程按钮展示逻辑优化
- 解决推荐商品无法跳转问题
- 修复语音播放bug
- 商品推荐
- Npm
- 格式化导航信息
- Yml
- UI 修复
- Del
- 全局更换跳转方法
- Merge branch 'canary' into test
- 登录页UI
- 优化ai咨询页的历史记录
- 文章推荐
- 导航
- 空点位数据展示
- 优化语音播放打断问题
- 添加断线重连功能
- 行程交互
- 文章跳转
- 局部滚动
- 移除打印信息
- 行程地图
- 更改setCookie接口
- 解决公用方法 getRoute 问题
- Merge branch 'feat/debug' into test
- AI 行程详情
- AI 行程空指针异常
- 数据异常处理
- 简介展开按钮
- 提示语间距
- 添加按钮样式
- 屏蔽弹窗拖拽
- Merge remote-tracking branch 'origin/master' into canary
- 优化行程偏好显示效果
- 优化ai咨询会话
- 解决高亮渲染错乱问题
- 修复行程规划按钮显示问题
- 代码优化
- 修复分时时段设置库存的问题
- 修复分时时段置灰逻辑
- 修复组合票置灰的逻辑
- 修复分时预约没时段的库存问题，更改实名制的取值字段
- 解决语音进度条问题
- 语音导览ui优化
- 文案更改
- 解决音频使用体验
- 导览路线、语音枚举
- 更换 AI 生产域名
- 屏蔽 Trace
- 更换 ws 生产域名
- 聚合级别
- 修复购买非本人购买权益卡下单的问题
- 修复 AI 路线按钮异常
- 行程优化
- 区分点位路线
- 行程窗口拖动
- 行程导航
- Wx login
- Wxlogin
- 微信登录参数设置为自动登录
- 进入登录页添加判断，是否有cookie，有直接跳转首页
- 修改字段
- 解绑微信
- 绑定微信接口替换
- 优化绑定微信接口
- 调试绑定微信接口
- Tutu 提供默认插槽
- 行程智能生成
- 拖动排序
- 点位描点定位
- Merge branch 'test' into canary
- Ai 弹窗固定两个位置
- 增大ai弹窗拖动的触发范围
- 继续增大ai弹窗拖动的触发范围
- 解决音频选择 bug
- 行程点位列表样式布局调整，修复文章推荐跳转异常
- 优化dialogue，删除不必要代码
- 修复tutu不能滚动查看历史记录的问题
- 提交
- 解决录音未开启成功，页面报错问题
- 修改
- 修复苹果不兼容的问题
- 优化一下登陆失效逻辑
- 解决登录重定向问题
- 修复tutu回复过程中，向上滚动值有偏差造成自动停止的问题
- 加个提示
- Ai回复终极优化
- 优化滚动
- 配置404页面
- 修复智旅链交易详情地址变化的问题
- 修复购买门票时，购买数量和库存数量对不上的问题
- 订单详情 组合票 一票多人开发
- Debugh
- 处理人脸图片链接问题

### 💼 Other

- 添加无店铺错误页
- 修改订单相关UI
- 修复人脸图片不回显问题

### ⚙️ Miscellaneous Tasks

- 添加自动导入组件功能
- 调整二维码尺寸和页面高度
- 调整单个门票组件样式
- 添加登录页、个人中心页
- 删除忘记密码页面和相关组件
- 调整页面初始化逻辑和重定向处理
- Optimize build process by enabling console log removal during minification
- 修复登录和加载页面的重定向逻辑
- 优化页面加载性能和重定向逻辑
- 添加页面loading动效，优化逻辑
- 修改过度时间

## [jaffrey-test] - 2022-09-20

### 🚀 Features

- Init
- Add login page
- 登录页
- 添加跳转方法 goPage，添加店铺 storeId
- 新加上传图片组件
- 订单详情背景主题
- 新增日历选择器组件
- 解决小程序无法携带cookie问题
- 新增退出登陆功能
- 解决跨域问题
- 添加缺省组件，修改一些样式
- 优化 tabBar 样式
- 电子票样式
- 添加导航组件
- 新加【加载更多】组件
- 订单加载更多
- 新增首页轮播图
- 优化票样式
- 添加 markdown 解析器
- 新加收货地址功能
- 个人中心新功能
- 微信解绑，绑定功能
- 添加枚举值 团体票
- 添加枚举 orderStatus
- 弹窗确认是否换绑手机号
- 微信换绑手机号确认弹窗

### 🐛 Bug Fixes

- H5
- Home
- Home-ticket
- Merge branch 'feat/merge' into deng
- 优化登陆模块
- Tarbar
- 优化上传图片组件
- 登陆页样式
- 优化兼容小程序
- @tab
- 优化样式
- 兼容微信获取路由参
- Merge branch 'deng' of git.shukeyun.com:scenic/shop into feat/merge
- 优化轮播图
- 优化tabbar显示隐藏逻辑
- 修复一些bug，优化一些样式
- Pull merge
- 联系人
- 组合退票
- 审批
- 景区轮播图传数据
- 人脸识别
- 服务条款
- 条款勾选样式
- 组合订单展示+下单加上代理商
- Test打包
- 实名认证加上点击
- 实名认证手机号检验
- 组合旅游标签标签修改
- 订单检票规则
- Ios时间戳
- Merge branch 'feat/deng' of git.shukeyun.com:scenic/shop into merge
- 退票状态
- Bug test
- 实名去空格 优化
- 审批细化  票数区间
- Test appid
- Text appid
- Text 添加logOut
- 去除审批方式判断
- IsPeopleNumber
- 添加单票审批提示
- 拉取test 打包
- 修改 bug
- 优化跳转登录页逻辑
- 优化常用联系人跳转问题
- 价格策略固定传值 1
- 修改审批判断逻辑
- 旅游卡时间计算
- 旅游卡名称
- 套票新增控制修改
- Merge test
- 组合联系人 分时预约
- 旅游卡默认数 ，最大库存限制
- 推荐景点单票
- 修复票状态枚举
- Build
- 出库失败
- SystemType
- 参数数组问题

### 💼 Other

- Uni_modules
- Merge remote-tracking branch 'origin/deng' into feat/merge
- 部署dev
- 部署 test

### 🎨 Styling

- 新加门票预定页
- 订单详情页
- 票样式
- 优化 UI
- 样式
- 二维码的样式
- 优化样式问题

### ⚙️ Miscellaneous Tasks

- Delete .vite
- Delete .hbuilderx
- Update .gitignore
- 配置环境变量

<!-- generated by git-cliff -->
