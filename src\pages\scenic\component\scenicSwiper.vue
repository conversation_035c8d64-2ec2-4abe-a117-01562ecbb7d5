<template>
	<view class="swiper-main" @touchstart="touchstart" @touchend="touchend" @touchmove="touchmove">
		<view class="swiper-list" :style="swiperStyle">
			<view v-for="(url, index) in weiperList" :key="url" class="swiper-item">
				<image class="img" :src="url" mode="aspectFill"></image>
			</view>
		</view>
		<view class="swiper-total">
			{{ curNumber }}/{{ weiperList.length - 1 }}
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onUnmounted, nextTick, computed, watch } from "vue"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
	//轮播图列表
	list: {
		type: String,
		default: ""
	},
	//滑块自动切换时间间隔，ms
	interval: {
		type: Number,
		default: 4000
	},
	//滑块切换过程所需时间 ms
	duration: {
		type: Number,
		default: 1000
	}
})
// 轮播图，添加第一张到最后一张，用于无缝轮播
const weiperList = ref([])
watch(
	() => props.list,
	newList => {
		let list = []
		newList.split(",").map(e => {
			if (e !== "") {
				list.push(getEnv().VITE_IMG_HOST + e)
			}
		})
		//默认图片
		if (list.length == 0) {
			list = [
				"https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png"
			]
		}
		weiperList.value = [...list, list[0]]
	},
	{ deep: true }
)
// TODO: 重构这个轮播图
// 当前轮播的张数
const curNumber = ref(1)
// 偏移的距离
const swiperOffset = computed(() => curNumber.value * -100 + 100)

// 轮播图样式
const swiperStyle = reactive({
	transform: `translateX(${swiperOffset.value}%)`, // 偏移量
	transition: props.duration / 1000 + "s", // 动画时间
	transitionProperty: "all" // 是否添加动画
})
// 滑动一次所需时间
const swiperDuration = props.interval + props.duration
const timer = setInterval(() => {
	// 执行轮播
	swiperStyle.transitionProperty = "all"
	swiperStyle.transform = `translateX(${swiperOffset.value}%)`
	// 页数加一
	curNumber.value += 1
	//滑到最后一张的时候，偷偷切换到第一张
	if (curNumber.value === weiperList.value.length) {
		curNumber.value = 1
		//此延时是为了让过度动画走完再切换
		setTimeout(() => {
			swiperStyle.transitionProperty = "none" //偷偷切换时要关闭动画，不然就被发现了
			swiperStyle.transform = `translateX(${swiperOffset.value}%)`
		}, props.duration)
	}
}, swiperDuration)
onUnmounted(() => {
	clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.swiper-main {
	position: relative;
	overflow: hidden;
	margin: 0 auto;
	height: 422rpx;
	border-top-left-radius: 32rpx;
	border-top-right-radius: 32rpx;

	.swiper-list {
		display: flex;
		height: 100%;

		// transition: 0.3s;
		.swiper-item {
			flex: none;
			width: 670rpx;
			height: 893rpx;

			.img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.swiper-total {
		position: absolute;
		bottom: 21rpx;
		right: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 32rpx;
		padding: 0 11rpx;
		background-color: rgba(255, 255, 255, 0.79);
		border-radius: 16rpx;
		font-size: 24rpx;
		font-weight: 500;
		color: #050505;
		letter-spacing: 4rpx;
	}
}
</style>
