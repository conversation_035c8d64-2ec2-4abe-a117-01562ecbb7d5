<template>
	<view class="loading-page">
		<y-loading-text text="loading" />

		<!-- <view class="loading">
			<view class="letter">
				<view>L</view>
				<view class="back">L</view>
			</view>
			<view class="letter">
				<view>o</view>
				<view class="back">o</view>
			</view>
			<view class="letter">
				<view>a</view>
				<view class="back">a</view>
			</view>
			<view class="letter">
				<view>d</view>
				<view class="back">d</view>
			</view>
			<view class="letter">
				<view>i</view>
				<view class="back">i</view>
			</view>
			<view class="letter">
				<view>n</view>
				<view class="back">n</view>
			</view>
			<view class="letter">
				<view>g</view>
				<view class="back">g</view>
			</view>
			<view class="letter dot">
				<view>.</view>
				<view class="back">.</view>
			</view>
			<view class="letter dot">
				<view>.</view>
				<view class="back">.</view>
			</view>
			<view class="letter dot">
				<view>.</view>
				<view class="back">.</view>
			</view>
		</view> -->
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import { onShow } from "@dcloudio/uni-app"
import { getRoute } from "@/utils/tool.js"
import request from "@/utils/request.js"

// const loadingRef = ref(null)
// setTimeout(() => {
// 	loadingRef.value.style.opacity = 1
// }, 0)
const { storeId } = getRoute.params()
onShow(async () => {
	try {
		const redirectLoading = sessionStorage.getItem("redirectLoading")
		
		await setStoreInfo()
		await setTheme()
		await setStoreConfig()
		if (redirectLoading && redirectLoading.indexOf("/pages/loading/loading") === -1) {
			uni.redirectTo({
				url: redirectLoading
			})
		} else {
			Tool.goPage.replace("/") // 跳转回原来的页面
		}
	} catch (e) {
		console.error(e)
		//TODO handle the exception
	}
})
// 设置主题
async function setTheme() {
	const { data: theme } = await request.get(`/store/style/info`, {
		storeId
	})

	const themeConfig = {
		shopNav: [
			{
				name: "首页",
				link: "home",
				icon: "1_1",
				icon_active: "1_1_"
			},
			{
				name: "导览",
				link: "tour",
				icon: "1_2",
				icon_active: "1_2_"
			},
			{
				name: "订单",
				link: "order",
				icon: "1_3",
				icon_active: "1_3_"
			},
			{
				name: "我的",
				link: "my",
				icon: "1_4",
				icon_active: "1_4_"
			}
		],
		shopStyle: {
			color: "#349FFF",
			gradation: ["#BBD9FF", "#EFF6FF"]
		},
		...JSON.parse(theme?.pageContent || "{}")
	}
	getApp().globalData.themeConfig = themeConfig
	localStorage.setItem("themeConfig", JSON.stringify(themeConfig))	// 存本地缓存
	localStorage.setItem("themeConfigTime", new Date().getTime())	// 存本地缓存

	const { color, gradation } = themeConfig.shopStyle

	setCssVar("--theme-color", color)
	setCssVar("--theme-bg", hexToRgb(color))


	if (gradation) {
		setCssVar("--linear-gradient0", gradation[0])
		setCssVar("--linear-gradient1", gradation[1])
	}
}
// 获取店铺信息
async function setStoreInfo() {
	let app = ""
	// #ifdef H5
	app = getApp()
	// #endif
	// #ifdef MP-WEIXIN
	app = this.$scope
	// #endif
	if (storeId) {
		app.globalData.storeId = storeId
		// 店铺 id，冗余处理
		uni.setStorage({
			key: "storeId",
			data: storeId
		})

		//获取店铺名称
		try {
			const {
				data: { name, iconFileId, iconType, introduce, fileInfo }
			} = await request.get(`/ticketStore/noAuth/store/${storeId}`)
			Tool.globalData.set("storeInfo", {
				name,
				iconUrl: fileInfo?.fileUrl,
				iconType,
				introduce
			})
		} catch (error) {
			if (error.code === 2) {
				Tool.goPage.replace("/pages/error/noStore")
			}
		}
	} else {
		console.error("缺失店铺 storeId")
		uni.showModal({
			title: "找不到店铺",
			showCancel: false,
			success: obj => {
				window.close()
			}
		})
	}
}
// 获取店铺装修配置
async function setStoreConfig() {
	const { data } = await request.get(`/store/design/home/<USER>
		storeId
	})
	// 保存店铺配置
	getApp().globalData.storeConfig = JSON.parse(data.pageContent || "{}")
}

// 修改主题
function setCssVar(prop, val, dom = document.documentElement) {
	dom.style.setProperty(prop, val)
}
</script>
<style lang="scss" scoped>
.loading-page {
	opacity: 1;
	/* 初始透明度设置为 0 */
	transition: opacity 0.5s ease-in-out;
	/* 设置过渡效果，持续时间为 1 秒 */
	padding: 0;
	height: 100vh;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	font-size: 40rpx;
	font-family: Monaco, Consolas, "Lucida Console", monospace;
	position: fixed;
	top: 0;
	bottom: 0;
	background: #fff;
	width: 100vw;
}

.loading {
	text-align: center;
	display: table-cell;
	vertical-align: middle;
	text-shadow: grey 1px 1px 1px;
}

.letter {
	float: left;
	width: 35rpx;
	height: 60px;
	position: relative;
	animation: flip 2s infinite;
	transform-style: preserve-3d;
	transition: transform 1s;
}

.letter div {
	width: 100%;
	height: 100%;
	position: absolute;
	transform: translate(0);
	backface-visibility: hidden;
	animation: color 8s infinite;
}

.letter div.back {
	transform: rotateY(180deg);
}

.letter:nth-child(1),
.letter:nth-child(1) div {
	animation-delay: 0.125s;
}

.letter:nth-child(2),
.letter:nth-child(2) div {
	animation-delay: 0.25s;
}

.letter:nth-child(3),
.letter:nth-child(3) div {
	animation-delay: 0.375s;
}

.letter:nth-child(4),
.letter:nth-child(4) div {
	animation-delay: 0.5s;
}

.letter:nth-child(5),
.letter:nth-child(5) div {
	animation-delay: 0.625s;
}
.letter:nth-child(6),
.letter:nth-child(6) div {
	animation-delay: 0.75s;
}
.letter:nth-child(7),
.letter:nth-child(7) div {
	animation-delay: 0.875s;
}
.letter:nth-child(8),
.letter:nth-child(8) div {
	animation-delay: 1s;
}
.letter:nth-child(9),
.letter:nth-child(9) div {
	animation-delay: 1.125s;
}
.letter:nth-child(10),
.letter:nth-child(10) div {
	animation-delay: 1.25s;
}
@keyframes flip {
	0% {
		transform: rotateY(0deg) translate(0);
	}
	40%,
	100% {
		transform: rotateY(180deg) translate(0);
	}
}
@keyframes color {
	0% {
		color: #88e488;
	}
	25% {
		color: #eeadb7;
	}
	50% {
		color: #90c9db;
	}
	75% {
		color: #f3b034;
	}
	100% {
		color: #828282;
	}
}
</style>
