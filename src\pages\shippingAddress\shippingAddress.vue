<template>
	<y-nav-bar fontColor="#050505" backBtn solid>收货地址</y-nav-bar>
	<view class="shipping-address">
		<!-- 地址列表 -->
		<template v-if="addressPage === 'listPage'">
			<view class="address-list" v-show="addressList.length > 0">
				<view
					class="address-list-item"
					v-for="(item, index) in addressList"
					:key="index">
					<view class="address-list-item-title">
						<y-font-weight
							>{{ item.address.province }}{{ item.address.city
							}}{{ item.address.district }}{{ item.address.town
							}}{{ item.address.detail }}</y-font-weight
						>
						<text class="address-default" v-if="item.address.isDefault"
							>默认</text
						>
					</view>
					<view class="address-list-item-info">
						<view class="left">
							<view class="address-name">{{ item.address.name }}</view>
							<view class="address-phone">{{ item.address.phone }}</view>
						</view>

						<view class="right">
							<y-svg @tap="onEditAddress(item)" name="edit-icon" class="icon" />
							<image
								class="icon"
								@tap="onDelAddress(item.id)"
								src="@/static/image/trashcan-icon.png"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="y-fixed-bottom">
				<y-button
					:disable="false"
					@tap="addAddress"
					v-show="addressList.length > 0"
					>添加地址</y-button
				>
			</view>
			<!-- 地址缺省页 -->
			<view
				class="empty"
				style="
					display: flex;
					justify-content: center;
					flex-direction: column;
					align-items: center;
					margin-top: 300rpx;
				"
				v-show="addressList.length === 0">
				<!-- <image
					style="width: 565rpx; height: 516rpx; margin: 100rpx auto 0"
					src="@/static/image/address-empty.png" />
				<view style="margin-top: -230rpx"></view> -->
				<y-empty>暂无地址～</y-empty>
				<view class="y-fixed-bottom">
					<y-button :disable="false" @tap="addAddress">添加地址</y-button>
				</view>
			</view>
		</template>

		<!-- 编辑地址 -->
		<view v-if="addressPage === 'editPage'">
			<view class="y-list">
				<view class="y-list-item">
					<view class="y-list-item-left">收货人</view>
					<input
						v-model="params.address.name"
						placeholder-class="y-list-item-placeholder"
						class="y-list-item-input"
						type="text"
						placeholder="请输入" />
				</view>
				<view class="y-list-item">
					<view class="y-list-item-left">手机号码</view>
					<input
						maxlength="11"
						v-model="params.address.phone"
						placeholder-class="y-list-item-placeholder"
						class="y-list-item-input"
						type="text"
						placeholder="请输入" />
				</view>
				<view class="y-list-item">
					<view class="y-list-item-left">所属地区</view>
					<view class="prov">
						<y-provinces
							:defaultValue="defaultProvin"
							@onChange="onChangeProvices"></y-provinces>
						<uni-icons type="right"></uni-icons>
					</view>
				</view>
				<view class="y-list-item textarea">
					<view class="y-list-item-left">详细地址</view>
					<textarea
						v-model="params.address.detail"
						placeholder-class="y-list-item-placeholder textarea"
						class="y-list-item-textarea"
						type="text"
						placeholder="请输入" />
				</view>
			</view>
			<view class="y-list">
				<view class="y-list-item">
					<view class="y-list-item-left">设为默认收货地址</view>
					<switch
						class="right"
						:checked="params.address.isDefault"
						@change="setDefaultAddress"
						color="var(--theme-color)"
						style="transform: scale(0.7)" />
				</view>
			</view>
			<view class="y-fixed-bottom">
				<y-button @tap="onSubmitAddress" :disable="!canSubmit">确定</y-button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import { getRoute } from "@/utils/tool.js"
import request from "@/utils/request.js"
import { onShow } from "@dcloudio/uni-app"

const stase = reactive({
	userInfo: {}
})

//页面状态
const addressPage = ref("listPage")

const params = reactive({
	address: {
		city: "",
		detail: "",
		district: "",
		isDefault: false,
		name: "",
		phone: "",
		province: "",
		town: ""
	},
	userId: ""
})

//重置请求参数
const resetParams = () => {
	for (let key in params.address) {
		params.address[key] = ""
	}
	params.id = ""
}

const canSubmit = computed(() => {
	return (
		params.address.name &&
		params.address.phone &&
		params.address.city &&
		params.address.province &&
		params.address.district &&
		params.address.detail
	)
})

//默认地区
const defaultProvin = ref([])
//添加地址
const addAddress = () => {
	resetParams()
	Tool.goPage.push("/pages/shippingAddress/shippingAddress?page=editPage")
}
//修改地址
const onEditAddress = item => {
	// resetParams()
	Tool.goPage.push(
		"/pages/shippingAddress/shippingAddress?page=editPage&id=" + item.id
	)
}

//删除地址
const onDelAddress = async id => {
	const { code, data } = await request.delete(`/my/info/${id}`)
	uni.showToast({
		title: "删除成功"
	})
	getAddressList()
}

//修改省市区
const onChangeProvices = e => {
	console.log(e)
	params.address.province = e[0].addressName
	params.address.city = e[1].addressName
	params.address.district = e[2].addressName
	params.address.town = e[3].addressName
}

//获取地址列表
const addressList = ref([])
const getAddressList = async editId => {
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { userId } = uesrData.userInfo
	const { code, data } = await request.post(`/my/list/${userId}`)
	uni.hideLoading()
	addressList.value = data

	if (data.length !== 0 && editId) {
		//当前是编辑页，遍历赋值
		const editInfo = data.find(e => editId === e.id)
		params.address = {
			...editInfo.address
		}
		params.id = editInfo.id
		defaultProvin.value = [
			editInfo.address.province,
			editInfo.address.city,
			editInfo.address.district,
			editInfo.address.town
		]
	}
}

//添加收货地址
const onSubmitAddress = async () => {
	if (!canSubmit.value) return
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	if (params.id) {
		//修改
		const { code, data } = await request.put(`/my/info`, params)
		uni.hideLoading()
		uni.showToast({
			title: "修改成功"
		})
	} else {
		//新增
		const { code, data } = await request.post(`/my/info`, params)
		uni.hideLoading()
		uni.showToast({
			title: "添加成功"
		})
	}
	Tool.goPage.back()
}

// 设置默认地址
const setDefaultAddress = e => {
	params.address.isDefault = e.detail.value
}
let uesrData = {}
onShow(async () => {
	uesrData = await Tool.getUserInfo()
	params.userId = uesrData.userInfo.userId
	const page = getRoute.params().page
	const editId = getRoute.params().id
	addressPage.value = page ? page : "listPage"
	getAddressList(editId)
})
</script>
<style lang="scss" scoped>
.shipping-address {
	background: #f6f6f6;
	min-height: 100%;
	overflow: hidden;
	.prov {
		display: flex;
		align-items: center;
	}
	.defaule-address {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 40rpx 0;
		line-height: 34rpx;
		.left {
			font-size: 28rpx;
			font-weight: 400;
			color: #14131f;
		}
	}
	.address-list {
		margin: 0rpx 40rpx 30rpx;
		padding-top: 30rpx;
		.address-list-item {
			margin-bottom: 30rpx;
			padding: 30rpx;
			background: #ffffff;
			border-radius: 17rpx;
			.address-list-item-title {
				// display: flex;
				// align-items: center;
				flex-direction: column;
				font-size: 34rpx;
				font-weight: 500;
				color: #14131f;
				line-height: 50rpx;
				.address-default {
					margin-left: 20rpx;
					display: inline-block;
					line-height: 25rpx;
					padding: 4rpx;
					border-radius: 8rpx;
					border: 1rpx solid #ff7070;
					font-size: 24rpx;
					font-weight: 400;
					color: #db5656;
				}
			}
			.address-list-item-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				// margin-top: 20rpx;
				.left {
					display: flex;
					font-size: 30rpx;
					font-weight: 400;
					color: #14131f;
					.address-name {
					}
					.address-phone {
						margin-left: 44rpx;
					}
				}
				.right {
					.icon {
						width: 50rpx;
						height: 50rpx;
						&:not(:last-child) {
							margin-right: 26rpx;
						}
					}
				}
			}
		}
	}
}
</style>
