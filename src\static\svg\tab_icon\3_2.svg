<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 52 51" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份 4</title>
    <defs>
        <linearGradient x1="90.3393911%" y1="0%" x2="9.66060891%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FD9F86" offset="0%"></stop>
            <stop stop-color="#EF6E53" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="90.9979825%" y1="0%" x2="9.00201749%" y2="100%" id="linearGradient-2">
            <stop stop-color="currentColor" offset="0%"></stop>
            <stop stop-color="currentColor" offset="99.3143575%"></stop>
        </linearGradient>
        <path d="M30.007285,46.6987952 C27.3411943,46.6987952 10.8114318,33.9169816 10.8114318,23.3801205 C10.8114318,12.8432593 19.4058418,4.30120482 30.007285,4.30120482 C40.6087281,4.30120482 49.2031381,12.8432593 49.2031381,23.3801205 C49.2031381,33.9169816 32.6733757,46.6987952 30.007285,46.6987952 Z" id="path-3-2"></path>
        <filter x="-1.3%" y="-1.2%" width="102.6%" height="102.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.786576705 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M29.9457364,27.8 C33.2593728,27.8 35.9457364,25.1136364 35.9457364,21.8 C35.9457364,18.4863636 33.2593728,15.8 29.9457364,15.8 C26.6321001,15.8 23.9457364,18.4863636 23.9457364,21.8 C23.9457364,25.1136363 26.6321001,27.8 29.9457364,27.8 Z" id="path-5-2"></path>
        <filter x="-87.5%" y="-70.8%" width="308.3%" height="308.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板备份" transform="translate(-284.000000, -716.000000)">
            <g id="编组备份-4" transform="translate(284.604651, 716.000000)">
                <rect id="矩形" opacity="0.072172619" fill-rule="nonzero" x="0" y="0" width="51.3953488" height="51"></rect>
                <path d="M15.7906977,45.6 C14.0691215,45.6 3.39534884,37.27929 3.39534884,30.42 C3.39534884,23.56071 8.94502195,18 15.7906977,18 C22.6363734,18 28.1860465,23.56071 28.1860465,30.42 C28.1860465,37.27929 17.5122739,45.6 15.7906977,45.6 Z" id="路径" fill="currentColor"></path>
                <g id="路径">
                    <use fill-opacity="0.1" fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3-2"></use>
                </g>
                <g id="路径" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5-2"></use>
                    <use fill="#FFFFFF" xlink:href="#path-5-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>