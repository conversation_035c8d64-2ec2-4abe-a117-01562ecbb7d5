<style lang="scss" scoped>
.doalogue__operation {
	display: flex;
	align-items: flex-end;
	padding: 20rpx 0rpx;
	.doalogue__operation__input {
		flex: 1;
		margin: 0 20rpx;
		padding: 20rpx;
		background-color: white;
		border-radius: 12rpx;
	}
	.doalogue__operation__speech,
	.doalogue__operation__submit {
		flex: 0;
		width: 60rpx;
		height: 60rpx;
		padding-bottom: 10rpx;
	}
	.doalogue__speech__input {
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;
		margin: 0 20rpx;
		padding: 20rpx;
		color: white;
		font-size: 28rpx;
		background-color: #349fff;
		border-radius: 12rpx;
	}
}
</style>
<template>
	<div class="doalogue__operation">
		<img v-show="!isSpeech" @click="onSpeechInput(true)" class="doalogue__operation__speech"
			src="@/static/image/ai/speech-icon.png" />
		<img v-show="isSpeech" @click="onSpeechInput(false)" class="doalogue__operation__speech"
			src="@/static/image/ai/keyboard-icon.png" />
		<!-- 语音输入 -->
		<y-longpress v-show="isSpeech" @startpress="onSpeech" @longpressEnd="onSpeech(false)"
			class="doalogue__speech__input" :style="{ opacity: isPress ? 0.5 : 1 }">
			{{ recStatus === 'recording' ? '松开发送' : '按住说话' }}
		</y-longpress>
		<!-- 文字输入 -->
		<div v-show="!isSpeech" class="doalogue__operation__input">
			<textarea style="width: 100%" :value="modelValue" @input="e => emits('update:modelValue', e.detail.value)"
				type="text" auto-height placeholder="有什么问题尽管问我" />
		</div>
		<img @click="onSubmit" class="doalogue__operation__submit" src="@/static/image/ai/submit-icon.png" />
	</div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue"
import useRecord from "@/hooks/useRecord"
import useWebSocket from "@/hooks/useWebSocket"
import YLongpress from "@/components/y-longpress/y-longpress.vue"

const props = defineProps({
	// 消息
	modelValue: {
		type: String,
		default: ""
	},
	socketMsg: {
		type: Object,
		default: () => ({})
	}
})
const isPress = ref(false)
const emits = defineEmits(["onSubmit", "update:modelValue", "onRecord"])

// 录音
const { recStart, recStop, recStatus, asrText, asrStatus } =
	useRecord({
		asr: true,
		onStop() {
			// onSubmit()
		},
		onStart() {

		},
		onAsred(text) {
			// 语音转文字完成
			console.log("语音转文字", text)
			onSubmit()
		},
		onError(err) {
			if (err.code === 1001) {
				// 初始化失败
				uni.showToast({
					icon: "none",
					title: err?.msg || '无法录音',
				})
			} else {
				// uni.showToast({
				// 	icon: "none",
				// 	title: err.message
				// })
			}
			recStop()
		}
	})

const isSpeech = ref(false)

// watch(() => recStatus.value, val => {
// 	if (val === 'recording') {
// 		emits("onRecord", true)
// 	} else {
// 		emits("onRecord", false)
// 	}
// })

// 录音按钮事件
const onSpeech = async (isStart = true) => {
	if (props.socketMsg.status === "start") {
		uni.showToast({
			icon: "none",
			title: "正在回复中，请稍后"
		})
		return
	}

	isPress.value = isStart
	if (isStart) {
		await recStart()
	} else {
		await recStop()
	}
	emits("onRecord", isStart)
}

// 切换语音输入
const onSpeechInput = (isS = true) => {
	isSpeech.value = isS
}

// 监听语音识别结果
watch(asrText, val => {
	if (val) {
		emits("update:modelValue", val)
	}
})

// 发送消息
const onSubmit = () => {
	emits("onSubmit", props.modelValue)
}

onUnmounted(() => {
	console.log("wsClose-asr")
})

// // 暴露给父组件的方法
// defineExpose({
// 	onSpeech
// })
</script>
