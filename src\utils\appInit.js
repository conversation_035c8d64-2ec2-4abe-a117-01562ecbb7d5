/**
 * App 初始化相关功能
 */

/**
 * 动态加载 vConsole
 */
export function loadVConsole() {
  const isDebug =
    location.href.indexOf("DEBUG=123") > -1 || sessionStorage.getItem("isDebug")
  if (isDebug) {
    sessionStorage.setItem("isDebug", 1)
    const script = document.createElement("script")
    script.src =
      "https://cdn.bootcdn.net/ajax/libs/vConsole/3.9.1/vconsole.min.js"
    script.onload = () => {
      new VConsole()
    }
    document.body.appendChild(script)
  }
}

/**
 * 处理微信登录逻辑
 * @param {Object} getRoute 获取路由参数的工具函数
 * @returns {Boolean} 是否已经处理了微信登录逻辑
 */
export function handleWechatLogin(getRoute) {
  // 微信登录
  if (
    location.href.indexOf("/?code") > -1 &&
    location.href.indexOf("state=") > -1
  ) {
    const urlArr = location.href.split("/?")
    const rightUrl = urlArr[1].split("#/")[0]
    const queryObj = {}
    rightUrl
      .split("&")
      .map(item => {
        const splitStr = item.split("=")
        return {
          key: splitStr[0],
          value: splitStr[1]
        }
      })
      .forEach(item => {
        queryObj[item.key] = item.value
      })
    const { code, state } = queryObj
    const [appId, route] = decodeURIComponent(state).split(",")

    if (route) {
      let href = ""
      if (route.indexOf("?")) {
        href =
          location.origin +
          location.pathname +
          `#${route}&code=${code}&storeId=${appId}`
      } else {
        href =
          location.origin +
          location.pathname +
          `#${route}?code=${code}&storeId=${appId}`
      }
      // location.href = href
      location.replace(href)
    } else {
      const url =
        location.origin +
        location.pathname +
        `#/pages/login/login?code=${code}&storeId=${appId}`

      // location.href = url
      location.replace(url)
    }
    return true
  } else if (!getRoute.params().storeId) {
    // 没有店铺 ID，跳转到默认店铺
    const ENV = import.meta.env.MODE
    if (import.meta.env.DEV) {
      let storeId = ""
      if (ENV === "dev") storeId = "449581220603904000"
      if (ENV === "test") storeId = "451422851179233280"
      if (ENV === "canary") storeId = "462651960039325696"

      location.replace(
        location.origin + "/#/pages/index/index?storeId=" + storeId
      )
      location.reload()
      return true
    } else if (ENV === "prod") {
      location.replace(
        location.origin +
        location.pathname +
        "/#/pages/index/index?storeId=385065956093214720"
      )
      return true
    }
  }
  return false
}

/**
 * 创建返回原系统的全局按钮
 */
export function createBackButton() {
  // 创建返回原系统的全局按钮
  const backButton = document.createElement('div')
  backButton.className = 'back-to-system-button'
  backButton.innerHTML = 'travel ai'
  
  // 设置按钮样式 - 更精致的贴边按钮
  backButton.style.position = 'fixed'
  backButton.style.zIndex = '9999'
  backButton.style.left = '0'
  backButton.style.top = '30%'
  backButton.style.padding = '6px 14px 6px 10px'
  backButton.style.background = 'linear-gradient(90deg, #349fff 0%, #6ec6ff 100%)'
  backButton.style.color = '#fff'
  backButton.style.fontSize = '13px'
  backButton.style.fontWeight = 'bold'
  backButton.style.borderRadius = '0 16px 16px 0'
  backButton.style.boxShadow = '3px 3px 12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.3)'
  backButton.style.cursor = 'pointer'
  backButton.style.display = 'flex'
  backButton.style.alignItems = 'center'
  backButton.style.border = 'none'
  backButton.style.transition = 'box-shadow 0.2s, transform 0.2s, padding 0.3s'

  // 悬停效果
  backButton.addEventListener('mouseenter', () => {
    backButton.style.boxShadow = '4px 4px 16px rgba(0,0,0,0.35), 0 0 0 1px rgba(255,255,255,0.5)'
    backButton.style.transform = 'translateX(3px)'
  })
  backButton.addEventListener('mouseleave', () => {
    backButton.style.boxShadow = '3px 3px 12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.3)'
    backButton.style.transform = 'none'
  })
  
  // 添加返回箭头图标
  const arrowIcon = document.createElement('span')
  arrowIcon.innerHTML = `<svg width="14" height="14" viewBox="0 0 24 24" fill="none" style="display:block" xmlns="http://www.w3.org/2000/svg"><path d="M15 18l-6-6 6-6" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`
  arrowIcon.style.marginRight = '6px'
  arrowIcon.style.display = 'flex'
  arrowIcon.style.alignItems = 'center'
  arrowIcon.style.justifyContent = 'center'
  arrowIcon.style.height = '14px'
  arrowIcon.style.width = '14px'
  arrowIcon.style.background = 'rgba(255,255,255,0.12)'
  arrowIcon.style.borderRadius = '50%'
  arrowIcon.style.padding = '3px'
  arrowIcon.style.transition = 'background 0.2s'

  // 箭头悬停效果
  backButton.addEventListener('mouseenter', () => {
    arrowIcon.style.background = 'rgba(255,255,255,0.22)'
  })
  backButton.addEventListener('mouseleave', () => {
    arrowIcon.style.background = 'rgba(255,255,255,0.12)'
  })
  
  backButton.prepend(arrowIcon)
  
  // 添加点击事件
  backButton.addEventListener('click', () => {
    // 使用 sourceUrl 进行跳转
    const sourceUrl = sessionStorage.getItem("sourceUrl")
    if (sourceUrl) {
      window.location.href = sourceUrl
    }
  })
  
  // 将按钮添加到 body
  document.body.appendChild(backButton)
  
  // 5 秒后隐藏文字，只保留箭头图标
  setTimeout(() => {
    // 获取文本节点并移除
    const textNodes = Array.from(backButton.childNodes).filter(node => node.nodeType === Node.TEXT_NODE)
    textNodes.forEach(node => backButton.removeChild(node))
    
    // 调整按钮样式为圆形
    backButton.style.padding = '6px'
    backButton.style.borderRadius = '0 50% 50% 0'
    
    // 移除箭头的右边距
    arrowIcon.style.marginRight = '0'
  }, 5000)
} 