<template>
  <image
    :src="imageSource"
    :class="customClass"
    :style="customStyle"
    :mode="mode"
    @error="handleError"
    @load="handleLoad"
  />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import request from '@/utils/request';

// 定义props
const props = defineProps({
  // 图片URL
  src: {
    type: String,
    default: ''
  },
  // 图片加载模式
  mode: {
    type: String,
    default: 'aspectFill'
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({})
  },
  // 是否自动转换为base64
  autoConvert: {
    type: Boolean,
    default: true
  },
  // 图片前缀（例如CDN域名）
  imgHost: {
    type: String,
    default: ''
  },
  // 最大重试次数
  maxRetries: {
    type: Number,
    default: 2
  }
});

// 定义事件
const emit = defineEmits(['load', 'error', 'base64-start', 'base64-success', 'base64-error']);

// 图片源
const imageSource = ref(props.src);
// 转换状态
const converting = ref(false);
// 是否已转换
const converted = ref(false);
// 当前重试次数
const retryCount = ref(0);

// 图片转base64
const convertToBase64 = async (url) => {
  if (!url || converting.value || converted.value) return;
  
  converting.value = true;
  emit('base64-start', { url });
  
  try {
    const res = await request.post('/image/base64', {
      imageUrl: url
    });
    
    if (res.data) {
      imageSource.value = res.data;
      converted.value = true;
      retryCount.value = 0;
      emit('base64-success', { 
        originalUrl: url, 
        base64: res.data 
      });
    } else {
      handleConversionError(url, new Error('Empty response data'));
    }
  } catch (error) {
    handleConversionError(url, error);
  }
};

// 处理转换错误
const handleConversionError = (url, error) => {
  console.error('转换图片失败:', error);
  
  // 如果未超过最大重试次数，则重试
  if (retryCount.value < props.maxRetries) {
    retryCount.value++;
    console.log(`重试转换图片 (${retryCount.value}/${props.maxRetries})...`);
    
    // 延迟重试
    setTimeout(() => {
      converting.value = false;
      convertToBase64(url);
    }, 1000);
  } else {
    // 重试次数已用完
    converting.value = false;
    emit('base64-error', { 
      url, 
      error,
      retries: retryCount.value
    });
  }
};

// 处理图片加载完成
const handleLoad = (e) => {
  emit('load', e);
};

// 处理图片加载错误
const handleError = (e) => {
  emit('error', e);
};

// 监听src变化
watch(() => props.src, (newVal) => {
  if (newVal !== imageSource.value) {
    imageSource.value = newVal;
    converted.value = false;
    retryCount.value = 0;
    
    if (props.autoConvert && newVal) {
      const fullUrl = props.imgHost ? props.imgHost + newVal : newVal;
      convertToBase64(fullUrl);
    }
  }
}, { immediate: false });

// 组件挂载时，如果有src且autoConvert为true，则自动转换
onMounted(() => {
  if (props.src && props.autoConvert) {
    const fullUrl = props.imgHost ? props.imgHost + props.src : props.src;
    convertToBase64(fullUrl);
  }
});

// 暴露方法
defineExpose({
  convertToBase64,
  imageSource,
  isConverted: () => converted.value,
  isConverting: () => converting.value
});
</script>

<style scoped>
/* 无需额外样式 */
</style> 