<template>
	<view class="tab">
		<view class="tab_box" :class="{ tab__border: !isList }">
			<image :class="{ scroll_img: tabLeft }"
				@click="Tool.goPage.push(`/pages/tourSearch/tourSearch?guideId=${guideId}`)"
				src="@/static/image/tour/tour_tab_search.svg" mode="aspectFit" />
			<view class="scroll_view" @scroll="scroll">
				<view :class="{ scroll_item: true, active: item == tabIndex }" v-for="item in tabList" :key="item"
					@click="getPointList(item)">
					{{ tabEnum[item] }}
				</view>
			</view>
			<image :class="{ scroll_img: tabRight }" @click="tabBig = true" src="@/static/image/tour/tour_tab_menu.svg"
				mode="aspectFit" />
		</view>
		<view v-if="tabBig" class="tab_box__big">
			<view class="top">
				<text>全部</text>
				<image @click="tabBig = false" src="@/static/image/tour/tour_close.svg" mode="aspectFit" />
			</view>
			<view class="bottom">
				<view :class="{ active: item == tabIndex }" v-for="item in tabList" :key="item"
					@click="getPointList(item)">
					<image :src="tabIcon[item][item == tabIndex ? 1 : 0]" mode="sapectFit" />
					<text>{{ tabEnum[item] }}</text>
				</view>
				<view v-if="tabList.length % 4" style="flex: 1"></view>
			</view>
		</view>
		<!-- 点位列表 -->
		<view v-if="isList" class="tab_list">
			<view class="type tab__border">
				<text v-for="(item, index) in ['推荐', '距离最近']" :key="index" :class="{ active: typeIndex == index }"
					@click="typeIndex = index">{{ item }}</text>
			</view>
			<view class="list">
				<view v-for="(item, index) in typeIndex ? pointSortList : pointList" :key="index" @click="
					item.pointType < 6 &&
					Tool.goPage.push(
								`/pages/tourDetail/tourDetail?guideId=${guideId}&id=${item.id}&distance=${item.distance}`
							)
					">
					<image :src="imgSrc(item.publicizeUrl)" mode="aspectFill" />
					<view>
						<text>{{ item.pointName }}</text>
						<view>
							<image src="@/static/image/tour/tour_distance.svg" mode="aspectFit" />
							<text>距您{{ item.distance }}公里</text>
						</view>
					</view>
				</view>
			</view>
			<image class="icon" src="@/static/image/tour/tour_map.svg" mode="aspectFit"
				@click="emits('setIsList', false)" />
		</view>
		<y-chat showTour :sceincName="scenicName" :scenicId="guideId" />
	</view>
</template>

<script setup>
import { ref, nextTick, onMounted, provide } from "vue"
import { onShow, onHide } from "@dcloudio/uni-app"
import { getRoute, imgSrc, pointDistance } from "@/utils/tool.js"
import request from "@/utils/request.js"
import icon_1 from "@/static/image/tour/tour_icon_1.svg"
import icon_2 from "@/static/image/tour/tour_icon_2.svg"
import icon_3 from "@/static/image/tour/tour_icon_3.svg"
import icon_4 from "@/static/image/tour/tour_icon_4.svg"
import icon_5 from "@/static/image/tour/tour_icon_5.svg"
import icon_6 from "@/static/image/tour/tour_icon_6.svg"
import icon_7 from "@/static/image/tour/tour_icon_7.svg"
import icon_8 from "@/static/image/tour/tour_icon_8.svg"
import icon_9 from "@/static/image/tour/tour_icon_9.svg"
import icon_10 from "@/static/image/tour/tour_icon_10.svg"
import icon_11 from "@/static/image/tour/tour_icon_11.svg"
import icon_12 from "@/static/image/tour/tour_icon_12.svg"
import icon_13 from "@/static/image/tour/tour_icon_13.svg"
import icon_14 from "@/static/image/tour/tour_icon_14.svg"
import icon_1_ from "@/static/image/tour/tour_icon_1_.svg"
import icon_2_ from "@/static/image/tour/tour_icon_2_.svg"
import icon_3_ from "@/static/image/tour/tour_icon_3_.svg"
import icon_4_ from "@/static/image/tour/tour_icon_4_.svg"
import icon_5_ from "@/static/image/tour/tour_icon_5_.svg"
import icon_6_ from "@/static/image/tour/tour_icon_6_.svg"
import icon_7_ from "@/static/image/tour/tour_icon_7_.svg"
import icon_8_ from "@/static/image/tour/tour_icon_8_.svg"
import icon_9_ from "@/static/image/tour/tour_icon_9_.svg"
import icon_10_ from "@/static/image/tour/tour_icon_10_.svg"
import icon_11_ from "@/static/image/tour/tour_icon_11_.svg"
import icon_12_ from "@/static/image/tour/tour_icon_12_.svg"
import icon_13_ from "@/static/image/tour/tour_icon_13_.svg"
import icon_14_ from "@/static/image/tour/tour_icon_14_.svg"

const props = defineProps(["isList", "scenicName"])
const { guideId } = getRoute.params()
const emits = defineEmits(["setPointList", "setIsList"])
const pointList = ref([])
const pointSortList = ref([])
const typeIndex = ref(0)
const tabList = ref([])
const tabLeft = ref(false)
const tabRight = ref(false)
const tabIndex = ref()
const tabBig = ref(false)
const tabEnum = {
	1: "景点",
	2: "住宿",
	3: "餐饮",
	4: "购物",
	5: "娱乐",
	6: "卫生间",
	7: "停车场",
	8: "出入口",
	9: "服务点",
	10: "乘车点",
	11: "售票处",
	12: "医务室",
	13: "母婴室",
	14: "其它"
}
const tabIcon = {
	1: [icon_1, icon_1_],
	2: [icon_2, icon_2_],
	3: [icon_3, icon_3_],
	4: [icon_4, icon_4_],
	5: [icon_5, icon_5_],
	6: [icon_6, icon_6_],
	7: [icon_7, icon_7_],
	8: [icon_8, icon_8_],
	9: [icon_9, icon_9_],
	10: [icon_10, icon_10_],
	11: [icon_11, icon_11_],
	12: [icon_12, icon_12_],
	13: [icon_13, icon_13_],
	14: [icon_14, icon_14_]
}

// 点击选项
const tabClick = (index, item) => {
	tabBig.value = false
	tabIndex.value = index
	getPointList(item)
	nextTick(() => {
		document.querySelector(".active").scrollIntoView({
			behavior: "smooth"
		})
	})
}
// 更新点位列表（添加距离、排序）
const initPointList = (pointId, notUpdateView) => {
	const sum = []
	pointList.value.map(item => {
		sum.push({
			...item,
			distance: pointDistance([item.latitude, item.longitude])
		})
	})
	pointList.value = [...sum]
	sum.sort((a, b) => a.distance - b.distance)
	pointSortList.value = sum
	// 更新地图点位列表
	emits("setPointList", sum, pointId, notUpdateView)
}

// 获取点位列表
const getPointList = (pointType, pointId) => {
	tabBig.value = false
	tabIndex.value = pointType
	request
		.get("/navigation/point/list", { pointType, scenicId: guideId, sort: 1 })
		.then(({ data }) => {
			pointList.value = data
			initPointList(pointId)
		})
	nextTick(() => {
		document.querySelector(".active").scrollIntoView({
			behavior: "smooth"
		})
	})
}

// 获取自定义点位列表（合并多种类型）
const getCustomPointList = async () => {
	const customPointList = Tool.globalData.get("customPointList")
	if (!customPointList || customPointList.length === 0) {
		return false
	}
	const pointTypeList = [...new Set(customPointList.map(item => item.point_type))]

	tabBig.value = false
	// 清空当前点位列表
	pointList.value = []
	let allPoints = []
	
	// 循环获取每种类型的点位
	for (const pointType of pointTypeList) {
		try {
			const { data } = await request.get("/navigation/point/list", { 
				pointType, 
				scenicId: guideId, 
				sort: 1 
			})
			// 合并点位列表
			allPoints = [...allPoints, ...data]
		} catch (error) {
			console.error(`获取点位类型 ${pointType} 失败:`, error)
		}
	}
	
	// 更新点位列表
	pointList.value = allPoints
	initPointList()
	return true
}

// tab 滚动
const scroll = () => {
	nextTick(() => {
		const dom = document.querySelector(".scroll_view")
		tabLeft.value = dom.scrollLeft > 1
		tabRight.value = dom.scrollLeft < dom.scrollWidth - dom.offsetWidth - 1
	})
}
defineExpose({ initPointList })
onShow(async () => {
	// 先判断是否有自定义点位列表
	const hasCustomPoints = await getCustomPointList()
	console.log('hasCustomPoints')
	console.log(hasCustomPoints)
	// 如果没有自定义点位列表，再处理重定向逻辑
	if (!hasCustomPoints) {
		try {
			// 重定向到点位弹窗
			const { pointType, pointId } = uni.getStorageSync("pointObj")
			if (pointType && pointId) {
				uni.setStorageSync("pointObj", {})
				getPointList(pointType, pointId)
			}
		} catch (error) {
			console.error("获取 pointObj 失败：", error)
		}
	}
})
onMounted(async () => {
	// 获取点位类型列表
	request
		.get("/navigation/point/type/list", { scenicId: guideId })
		.then(async ({ data }) => {
			// 设置 tab 列表数据
			tabList.value = data.sort((a, b) => a - b)
			const hasCustomPoints = await getCustomPointList()
			// 如果没有自定义点位列表，再获取默认点位类型列表
			if (!hasCustomPoints) {
				getPointList(tabList.value[0])
			}
			scroll()
		})
		.catch(error => {
			console.error("获取点位类型列表失败：", error)
		})
})

provide("getPointList", getPointList)
</script>

<style lang="scss" scoped>
.tab {
	position: relative;
	.tab_box {
		width: 750rpx;
		height: 88rpx;
		background: #fff;
		display: flex;
		align-items: center;
		border-bottom: 1px solid transparent;
		> image {
			width: 88rpx;
			height: 100%;
			padding: 26rpx;
		}
		> image:first-child.scroll_img {
			box-shadow: 4rpx 0rpx 6rpx 0rpx rgba(219, 219, 219, 0.5);
		}
		> image:last-child.scroll_img {
			box-shadow: -4rpx 0rpx 6rpx 0rpx rgba(219, 219, 219, 0.5);
		}
		.scroll_view {
			width: 0;
			flex: 1;
			display: flex;
			overflow: scroll;
			white-space: nowrap;
			&::-webkit-scrollbar {
				display: none;
			}
			.scroll_item {
				display: inline-block;
				height: 52rpx;
				border-radius: 26rpx;
				padding: 0 30rpx;
				line-height: 52rpx;
				font-size: 30rpx;
				color: #14131f;
			}
			.active {
				background: var(--theme-color);
				color: #fff;
			}
		}
	}
	.tab_box__big {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 2;
		.top {
			width: 750rpx;
			height: 88rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #fff;
			border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
			> text {
				width: 125rpx;
				height: 88rpx;
				font-size: 30rpx;
				color: #14131f;
				text-align: center;
				line-height: 88rpx;
			}
			> image {
				width: 88rpx;
				height: 100%;
				padding: 30rpx;
			}
		}
		.bottom {
			display: flex;
			flex-wrap: wrap;
			gap: 1rpx;
			background: #e7e7e7;
			border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
			> view {
				width: 186.75rpx;
				height: 186.75rpx;
				background: #fff;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				> image {
					width: 72rpx;
					height: 72rpx;
				}
				> text {
					margin-top: 1rpx;
					font-size: 28rpx;
					color: #000;
					line-height: 40rpx;
				}
			}
			.active {
				> text {
					color: var(--theme-color);
				}
			}
		}
	}
	.tab_list {
		position: absolute;
		top: 88rpx;
		left: 0;
		z-index: 1;
		width: 750rpx;
		height: calc(100vh - 88rpx);
		display: flex;
		flex-direction: column;
		.type {
			width: 100%;
			height: 88rpx;
			background: #fff;
			padding: 0 14rpx;
			display: flex;
			> text {
				padding: 0 32rpx;
				height: 100%;
				line-height: 88rpx;
				font-size: 30rpx;
				color: rgba(20, 19, 31, 0.68);
				position: relative;
			}
			.active {
				color: #14131f;
				&::after {
					content: "";
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 72rpx;
					height: 10rpx;
					background: #459bf8;
					border-radius: 3rpx;
				}
			}
		}
		.list {
			width: 100%;
			height: 0;
			flex: 1;
			background: #f1f1f1;
			overflow: auto;
			> view {
				margin: 30rpx;
				width: 690rpx;
				height: 180rpx;
				background: #fff;
				border-radius: 12rpx;
				overflow: hidden;
				display: flex;
				align-items: center;
				> image {
					width: 240rpx;
					height: 100%;
				}
				> view {
					width: 0;
					flex: 1;
					margin: 30rpx;
					display: flex;
					flex-direction: column;
					> text {
						font-size: 36rpx;
						font-weight: bold;
						color: #090909;
						line-height: 1;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					> view {
						margin-top: 7rpx;
						display: flex;
						align-items: center;
						> image {
							width: 24rpx;
							height: 24rpx;
						}
						> text {
							margin-left: 9rpx;
							font-size: 24rpx;
							color: #535353;
							line-height: 33rpx;
						}
					}
				}
			}
		}
		.icon {
			position: absolute;
			right: 20rpx;
			bottom: 80rpx;
			width: 80rpx;
			height: 80rpx;
			background: #fff;
			box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
			border-radius: 50%;
			padding: 14rpx;
		}
	}
	.tab__border {
		border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
	}
}
</style>
