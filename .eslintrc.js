module.exports = {
  root: true,
  parser: 'vue-eslint-parser',
  env: {
    browser: true,
    node: true,
    es2021: true,
  },
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    parser: '@typescript-eslint/parser',
    requireConfigFile: false,
    ecmaFeatures: {
      jsx: true,
    },
    extraFileExtensions: ['.vue'],
  },
  plugins: ['vue', '@typescript-eslint'],
  extends: ['eslint:recommended', 'plugin:vue/vue3-essential'],
  // extends: ['plugin:vue/vue3-recommended', 'plugin:@typescript-eslint/recommended'],
  rules: {
    'vue/no-unused-components': 'off',
    'vue/no-side-effects-in-computed-properties': 'off',
    'vue/valid-v-on': 'off',
    'no-console': 'off',
    'no-unused-labels': 'off',
    'vue/no-mutating-props': 'off',
    'no-empty': ['error', { allowEmptyCatch: true }],
    'vue/no-v-text-v-html-on-component': 'off',
    'vue/no-computed-properties-in-data': 'off',
    'no-irregular-whitespace': 'off',
    'vue/no-deprecated-destroyed-lifecycle': 'off',
    'no-mixed-spaces-and-tabs': 'off', // 禁止混用tab和空格
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/multi-word-component-names': 'off',
    'vue/html-self-closing': 'off',
    'vue/require-default-prop': 'off',
    'vue/no-v-html': 'off',
    'no-undef': 'off',
    'no-unused-vars': 'off',
    'parser-error': 'off',
    '@typescript-eslint/no-parsing-error': 'off',
    // 禁用所有可能的解析错误
    'babel/no-invalid-this': 'off',
    'babel/quotes': 'off',
    'babel/semi': 'off',
    'babel/no-unused-expressions': 'off',
    // 添加一个通配符规则
    'vue/no-unused-vars': 'off',
  },
  globals: {
    // uni-app 全局变量
    uni: true,
    wx: true,
    plus: true,
    getApp: true,
    getCurrentPages: true,
    Tool: true,
    dayjs: true,
  },
  overrides: [
    {
      files: ['src/utils/tools.ts'],
      parser: 'espree',
      rules: {
        'no-parsing-error': 'off',
      },
    },
  ],
};
