{
	"pages": [
		//pages 数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "首页",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/ai/ai",
			"style": {
				"navigationBarTitleText": "AI",
			}
		},
		{
			"path": "pages/tourList/tourList",
			"style": {
				"navigationBarTitleText": "导览",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/order/order",
			"style": {
				"navigationBarTitleText": "订单",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "我的",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/my/myCheckIn",
			"style": {
				"navigationBarTitleText": "我的打卡"
			}
		},
		{
			"path": "pages/my/checkInMap",
			"style": {
				"navigationBarTitleText": "打卡地图"
			}
		},
		{
			"path": "pages/my/addCheckIn",
			"style": {
				"navigationBarTitleText": "添加打卡"
			}
		},
		{
			"path": "pages/forgetPwd/forgetPwd",
			"style": {
				"navigationBarTitleText": "忘记密码"
			}
		},
		{
			"path": "pages/ticketList/ticketList",
			"style": {
				"navigationBarTitleText": "景区门票",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/myCollect/myCollect",
			"style": {
				"navigationBarTitleText": "我的收藏",
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/scenic/scenic",
			"style": {
				"navigationBarTitleText": "景区门票"
			}
		},
		{
			"path": "pages/scenic/scenicByTravelai",
			"style": {
				"navigationBarTitleText": "景区门票"
			}
		},
		{
			"path": "pages/book/book",
			"style": {
				"navigationBarTitleText": "门票预订"
			}
		},
		{
			"path": "pages/travelCardList/travelCardList",
			"style": {
				"navigationBarTitleText": "权益卡列表"
			}
		},
		{
			"path": "pages/travelCardRights/travelCardRights",
			"style": {
				"navigationBarTitleText": "权益卡权益"
			}
		},
		{
			"path": "pages/orderDetail/orderDetail",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},
		{
			"path": "pages/travelCardDetail/travelCardDetail",
			"style": {
				"navigationBarTitleText": "权益卡详情"
			}
		},
		{
			"path": "pages/certification/certification",
			"style": {
				"navigationBarTitleText": "实名认证"
			}
		},
		{
			"path": "pages/my/contactUs",
			"style": {
				"navigationBarTitleText": "联系我们"
			}
		},
		{
			"path": "pages/faceRecognition/faceRecognition",
			"style": {
				"navigationBarTitleText": "人脸识别"
			}
		},
		{
			"path": "pages/approval/approval",
			"style": {
				"navigationBarTitleText": "审批"
			}
		},
		{
			"path": "pages/approvalDetail/approvalDetail",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/registerRule/registerRule",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/ETicket/ETicket",
			"style": {
				"navigationBarTitleText": "电子票"
			}
		},
		{
			"path": "pages/blockchain/blockchainTransactions",
			"style": {
				"navigationBarTitleText": "区块链交易存证"
			}
		},
		{
			"path": "pages/refund/refund",
			"style": {
				"navigationBarTitleText": "退票"
			}
		},
		{
			"path": "pages/information/information",
			"style": {
				"navigationBarTitleText": "活动资讯"
			}
		},
		{
			"path": "pages/travelNotes/travelNotes",
			"style": {
				"navigationBarTitleText": "攻略游记"
			}
		},
		{
			"path": "pages/articleDetail/articleDetail",
			"style": {
				"navigationBarTitleText": "详情页"
			}
		},
		{
			"path": "pages/personalCenter/personalCenter",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		},
		{
			"path": "pages/shippingAddress/shippingAddress",
			"style": {
				"navigationBarTitleText": "收货地址"
			}
		},
		{
			"path": "pages/contactsList/contactsList",
			"style": {
				"navigationBarTitleText": "常用人"
			}
		},
		{
			"path": "pages/travelList/travelList",
			"style": {
				"navigationBarTitleText": "我的行程"
			}
		},
		{
			"path": "pages/travelInfo/travelInfo",
			"style": {
				"navigationBarTitleText": "行程信息"
			}
		},
		{
			"path": "pages/travelDetails/travelDetails",
			"style": {
				"navigationBarTitleText": "行程详情"
			}
		},
		{
			"path": "pages/travelAddress/travelAddress",
			"style": {
				"navigationBarTitleText": "查找地点"
			}
		},
		{
			"path": "pages/loading/loading",
			"style": {
				"navigationBarTitleText": "登录中"
			}
		},
		{
			"path": "pages/error/notStore",
			"style": {
				"navigationBarTitleText": "无门店"
			}
		},
		{
			"path": "pages/error/404",
			"style": {
				"navigationBarTitleText": "404"
			}
		},
		{
			"path": "pages/error/500",
			"style": {
				"navigationBarTitleText": "500"
			}
		},
		{
			"path": "pages/tour/tour",
			"style": {
				"navigationBarTitleText": "导览"
			}
		},
		{
			"path": "pages/tourDetail/tourDetail",
			"style": {
				"navigationBarTitleText": "点位详情"
			}
		},
		{
			"path": "pages/tourLineList/tourLineList",
			"style": {
				"navigationBarTitleText": "游览路线"
			}
		},
		{
			"path": "pages/tourLineDetail/tourLineDetail",
			"style": {
				"navigationBarTitleText": "游览路线"
			}
		},
		{
			"path": "pages/tourSearch/tourSearch",
			"style": {
				"navigationBarTitleText": "查找地点"
			}
		},
		{
			"path": "pages/locationSearch/locationSearch",
			"style": {
				"navigationBarTitleText": "查找城市"
			}
		},
		{
			"path": "pages/my/userAgreement",
			"style": {
				"navigationBarTitleText": "用户协议"
			}
		},
		{
			"path": "pages/advice/advice",
			"style": {
				"navigationBarTitleText": "意见反馈"
			}
		},
		{
			"path": "pages/blockchainCertificate/blockchainCertificate",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/demo/demo",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/comment/postEvaluation",
			"style": {
				"navigationBarTitleText": "发表点评"
			}
		},
		{
			"path": "pages/comment/commentRules",
			"style": {
				"navigationBarTitleText": "点评规则"
			}
		},
		{
			"path": "pages/scenic/commentList",
			"style": {
				"navigationBarTitleText": "用户点评"
			}
		},
		{
			"path": "pages/scenic/commentDetail",
			"style": {
				"navigationBarTitleText": "点评详情"
			}
		},
		{
			"path": "pages/scenic/checkInSuccess",
			"style": {
				"navigationBarTitleText": "打卡成功"
			}
		},
		{
			"path": "pages/gloabSearch/gloabSearch",
			"style": {
				"navigationBarTitleText": "全局搜索"
			}
		},
		{
			"path": "pages/gloabSearch/filterSearch",
			"style": {
				"navigationBarTitleText": "全局搜索"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "易旅宝",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#FCFCFC",
		"navigationStyle": "custom"
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"^y-(.*)": "@/components/y-$1.vue", // 匹配 components 目录内的 vue 文件
			"^l-(.*)": "./component/l-$1.vue" // 匹配 node_modules 内的 vue 文件
		}
	}
}