import { defineStore } from 'pinia';

export const useTravelStore = defineStore('travel', () => {
	const userJourneyDto = ref({});
	const update = ref(false);
	return { userJourneyDto, update };
});

export const useAiVoiceStore = defineStore("voiceType", () => {
	const aiVoiceType = ref(localStorage.getItem("aiVoiceType") || "")
	const wordType = ref(localStorage.getItem("wordType") || "")
	const setAiVoiceType = (type: string) => {
		aiVoiceType.value = type
		localStorage.setItem("aiVoiceType", type)
	}
	const setWordType = (type: string) => {
		wordType.value = type
		localStorage.setItem("wordType", type)
	}
	return { aiVoiceType, wordType, setAiVoiceType, setWordType }
})