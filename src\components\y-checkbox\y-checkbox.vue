<template>
	<view class="y-checkbox" @click="onChangeCheck">
		<template v-if="props.type === 'checkbox'">
			<view
				class="checkbox"
				:class="{ active: props.checked, disabled: props.disabled }">
				<uni-icons type="checkmarkempty" size="12" color="#fff"></uni-icons>
			</view>
			<text class="checkbox-text">
				<slot />
			</text>
		</template>
		<template v-if="props.type === 'radio'">
			<view
				class="radio"
				:class="{ active: props.checked, disabled: props.disabled }">
				<uni-icons
					v-show="props.checked"
					type="checkmarkempty"
					size="12"
					color="#fff"></uni-icons>
			</view>
			<text class="radio-text">
				<slot />
			</text>
		</template>
		<template v-if="props.type === 'collect'">
			<image
				v-show="props.checked"
				class="collect-icon"
				src="@/static/image/collect-icon.png"
				mode="widthFix"></image>
			<image
				v-show="!props.checked"
				class="collect-icon"
				src="@/static/image/uncollect-icon.png"
				mode="widthFix"></image>
		</template>
	</view>
</template>

<script setup>
const props = defineProps({
	checked: {
		type: Boolean,
		default: false
	},
	disabled: {
		type: Boolean,
		default: false
	},
	type: {
		type: String,
		default: "checkbox"
	}
})
const emits = defineEmits(["onCheck"])

const onChangeCheck = () => {
	if (!props.disabled) {
		// emits('update:modelValue', !props.modelValue)
		emits("onCheck")
	}
}
</script>

<style lang="scss" scoped>
.y-checkbox {
	display: flex;
	justify-content: center;
	align-items: center;
	.checkbox {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 6rpx;
		// margin-top: 4rpx;
		width: 28rpx;
		height: 28rpx;
		border-radius: 6rpx;
		border: 1rpx solid #999999;
		transition: $transition-time;
		&.active {
			background-color: var(--theme-color);
			border: none;
		}
		&.disabled {
			background-color: #666 !important;
			color: #666 !important;
			border: none;
			.uni-icons {
				color: #666 !important;
			}
		}
	}
	.checkbox-text {
		font-size: 28rpx;
		font-weight: 400;
		color: #999999;
	}

	.radio {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 6rpx;
		// margin-top: 4rpx;
		width: 28rpx;
		height: 28rpx;
		border-radius: 50%;
		border: 1rpx solid var(--theme-color);
		transition: $transition-time;
		&.active {
			background-color: var(--theme-color) !important;
			border: none;
		}
		&.disabled {
			background-color: #666 !important;
			color: #666 !important;
			border: none;
			.uni-icons {
				color: #666 !important;
			}
		}
	}
	.radio-text {
		font-size: 28rpx;
		font-weight: 400;
		color: #999999;
	}

	.collect-icon {
		width: 30rpx;
		height: 30rpx;
	}
}
</style>
