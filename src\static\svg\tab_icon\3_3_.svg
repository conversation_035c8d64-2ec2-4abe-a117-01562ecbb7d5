<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 2备份 2</title>
    <defs>
        <linearGradient x1="98.9379673%" y1="0%" x2="1.06203268%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FD9F86" offset="0%"></stop>
            <stop stop-color="#EF6E53" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="98.6854996%" y1="0%" x2="1.3145004%" y2="100%" id="linearGradient-2">
            <stop stop-color="currentColor" offset="0%"></stop>
            <stop stop-color="currentColor" offset="99.3143575%"></stop>
        </linearGradient>
        <path d="M7.56,0 L27.5537052,0 C31.7289779,-1.65516357e-15 35.1137052,3.38472729 35.1137052,7.56 L35.1137052,28.0245801 C35.1137052,32.1998528 31.7289779,35.5845801 27.5537052,35.5845801 L7.56,35.5845801 C3.38472729,35.5845801 -3.76854984e-16,32.1998528 0,28.0245801 L0,7.56 C3.76854984e-16,3.38472729 3.38472729,-1.21193266e-16 7.56,0 Z" id="path-3-3"></path>
        <filter x="-1.4%" y="-1.4%" width="102.8%" height="102.8%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.786576705 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-5-3" x="8.28644856" y="7.79145888" width="9.56944933" height="2.42444392" rx="1.21222196"></rect>
        <filter x="-109.7%" y="-350.6%" width="361.2%" height="1131.2%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <rect id="path-7" x="8.28644856" y="16.2770126" width="17.9427175" height="2.42444392" rx="1.21222196"></rect>
        <filter x="-58.5%" y="-350.6%" width="239.3%" height="1131.2%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.956862745   0 0 0 0 0.498039216   0 0 0 0 0.396078431  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <rect id="path-9" x="8.28644856" y="24.7625663" width="17.9427175" height="2.42444392" rx="1.21222196"></rect>
        <filter x="-58.5%" y="-350.6%" width="239.3%" height="1131.2%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.956862745   0 0 0 0 0.498039216   0 0 0 0 0.396078431  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板备份" transform="translate(-469.000000, -712.000000)">
            <g id="编组-2备份-2" transform="translate(469.000000, 712.000000)">
                <g id="订单备份" opacity="0.163876488">
                    <rect id="矩形备份-8" x="0" y="0" width="60" height="60"></rect>
                </g>
                <g id="编组-2备份" transform="translate(6.849799, 6.040203)">
                    <path d="M11.7946365,4.20298079 L28.2084951,4.13837484 C32.3837355,4.12194085 35.7817589,7.49331953 35.7981929,11.6685599 C35.7982706,11.6882911 35.798271,11.7080225 35.7981942,11.7277537 L35.7329503,28.4832843 C35.7167824,32.6354276 32.3549066,35.9974452 28.202764,36.0137883 L11.7889055,36.0783943 C7.61366509,36.0948283 4.21564163,32.7234496 4.19920763,28.5482092 C4.19912996,28.528478 4.19912955,28.5087466 4.19920638,28.4890154 L4.26445024,11.7334848 C4.28061815,7.58134155 7.64249398,4.21932387 11.7946365,4.20298079 Z" id="矩形" fill="currentColor" transform="translate(19.998700, 20.108385) rotate(-18.000000) translate(-19.998700, -20.108385) "></path>
                    <g id="编组" transform="translate(12.036496, 11.382223)">
                        <g id="矩形备份">
                            <use fill-opacity="0.1" fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3-3"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3-3"></use>
                        </g>
                        <g id="矩形" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5-3"></use>
                            <use fill="#FFFFFF" xlink:href="#path-5-3"></use>
                        </g>
                        <g id="矩形备份-2" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                            <use fill="#FFFFFF" xlink:href="#path-7"></use>
                        </g>
                        <g id="矩形备份-3" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                            <use fill="#FFFFFF" xlink:href="#path-9"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>