<template>
  <view class="tour-detail">
    <swiper class="tour-detail--swiper">
      <swiper-item v-for="(item, index) in (detail?.publicizeList || [])" :key="index">
        <image class="swiper-item" :src="imgSrc(item)" mode="aspectFill" />
      </swiper-item>
    </swiper>
    <view :class="{ 'tour-detail--content': true, active: isTop }">
      <text class="title">{{ detail?.pointName }}</text>
      <text class="distance">距您{{ distance }}公里</text>
      <view class="address">
        <text>{{ detail?.pointAddress }}</text>
        <image src="@/static/image/tour/tour_detail_nav.svg" mode="aspectFit" />
        <span @click="nav">去这里</span>
      </view>
      <view v-if="showPlayer" class="audo_box">
        <view class="audo_box--left">
          <image :src="isPlay ? $pause : $play" mode="aspectFit" @click="playAudio" />
          <view></view>
        </view>
        <view class="audo_box--right">
          <text>{{ transformTime(value) }}</text>
          <slider :disabled="!detail?.aiAudioUrl" :value="value" :max="max" :block-size="w" activeColor="#2874FF"
            backgroundColor="#BBD6F2" @change="(e) => change(e, true)" @changing="(e) => change(e, false)" />
          <text>{{ transformTime(max) }}</text>
        </view>
      </view>
      <text class="content">{{ wordContent }}</text>
    </view>
    <l-broadcast :detail="detail" @setAiVoice="setAiVoice" />
  </view>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { getRoute, imgSrc, transformTime, setJWeixin } from "@/utils/tool.js";
import request from '@/utils/request.js';
import $play from '@/static/image/tour/tour_play.svg'
import $pause from '@/static/image/tour/tour_pause.svg'
import YouSheBiaoTiHei from '@/static/YouSheBiaoTiHei-2.ttf'
import { onHide, onUnload } from '@dcloudio/uni-app'
import { useAiVoiceStore } from "@/stores/travel";
const { id, distance } = getRoute.params()
const detail = ref()
const isTop = ref(false)
const isPlay = ref(false)
const value = ref(0)
const max = ref(0)
const w = innerWidth / 750 * 18
const innerAudioContext = uni.createInnerAudioContext();
let timeId = null
const travelVoice = useAiVoiceStore();
const wordContent = ref('')
const setAiVoice = () => {
  const { pointIntro, aiAudioDuration } = Tool.getAiVoice(detail.value)
  wordContent.value = pointIntro
  max.value = aiAudioDuration
  resetAudio()
  setTimeout(playAudio, 100);
}


  // 跳转第三方导航
  const nav = () => {
    jWeixin.openLocation({
      latitude: Number(detail.value?.latitude),
      longitude: Number(detail.value?.longitude),
      name: detail.value?.pointName,
      address: detail.value?.pointAddress,
    });
  }
  // 滑块改变
  const change = (e,b) => {
    if (timeId) {
      clearInterval(timeId)
      timeId = null
    }
    isPlay.value = b
    value.value = e.detail.value
    if (b) {
      innerAudioContext.seek(e.detail.value)
      innerAudioContext.play()
      startTime()
    } else {
      innerAudioContext.pause()
    }
  }
  // 启动定时器
  const startTime = () => {
    timeId = setInterval(() => {
      value.value++
      if (value.value == max.value) {
        // clearInterval(timeId)
        // timeId = null
        resetAudio()
      }
    }, 1000)
  }
  // 播放音频
  const playAudio = () => {
    const { aiAudioUrl } = Tool.getAiVoice(detail.value)
    console.log(Tool.getAiVoice(detail.value));
    
    if(!aiAudioUrl) return
    innerAudioContext.src = aiAudioUrl
    isPlay.value = !isPlay.value
    if (timeId) {
      clearInterval(timeId)
      timeId = null
    }
    if (isPlay.value) {
      if (innerAudioContext.currentTime >= innerAudioContext.duration) value.value = 0
      innerAudioContext.play()
      if (value.value < max.value) startTime()
    } else {
      innerAudioContext.pause()
    }
  }
// 重置音频
const resetAudio = () => {
  if (timeId) {
    clearInterval(timeId)
    timeId = null
  }
  innerAudioContext.currentTime = 0
  innerAudioContext.pause()
  value.value = 0
  isPlay.value = false
}

const showPlayer = computed(()=>{
  if(detail.value){
    return Tool.getAiVoice(detail.value).aiAudioDuration > 0
  }else{
    return false
  }
})

  onscroll = () => {
    isTop.value = scrollY >= innerWidth/750*378 - 1
  }
  innerAudioContext.onEnded(() => {
    timeId && clearInterval(timeId)
    isPlay.value = false
    if (value.value != max.value) value.value = max.value
  })
  request.get('/navigation/point/info', { id }).then(({ data }) => {
    detail.value = data
    setAiVoice(data)
  })
  uni.loadFontFace({
    family: 'YouSheBiaoTiHei',
    source: `url(${YouSheBiaoTiHei})`,
    success() {
      console.log('success')
    }
  })
  // 清除对象
  const clearn = () => {
    if (timeId) {
      clearInterval(timeId)
      timeId = null
    }
    innerAudioContext?.pause()
    isPlay.value = false
  }
  onUnload(clearn)
  onHide(clearn)
  setJWeixin()

</script>

<style lang="scss" scoped>
  .tour-detail {
    padding-top: 378rpx;
    .tour-detail--swiper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 422rpx;
      .swiper-item {
        width: 100%;
        height: 100%;
      }
    }
    .tour-detail--content {
      position: relative;
      width: 100%;
      height: 100vh;
      background: linear-gradient(134deg, #FFFFFF 0%, #FAFDFF 55%, rgba(173, 215, 250, 0.56) 100%);
      border-radius: 44rpx 44rpx 0 0;
      padding: 40rpx 30rpx;
      display: flex;
      flex-direction: column;
      transition: .2s;
      .title {
        font-size: 40rpx;
        font-family: YouSheBiaoTiHei;
        color: #000;
        line-height: 1;
      }
      .distance {
        margin-top: 14rpx;
        font-size: 24rpx;
        color: #535353;
        line-height: 33rpx;
      }
      .address {
        margin-top: 3rpx;
        display: flex;
        align-items: center;
        >text {
          font-size: 24rpx;
          color: #535353;
        }
        >image {
          margin-left: 14rpx;
          width: 40rpx;
          height: 40rpx;
        }
        >span {
          font-size: 26rpx;
          color: #349FFF;
        }
      }
      .audo_box {
        margin-top: 30rpx;
        width: 720rpx;
        height: 154rpx;
        background: url('@/static/image/tour/tour_audo_bg.webp') no-repeat right center/contain;
        border-radius: 77rpx 0rpx 0rpx 77rpx;
        display: flex;
        align-items: center;
        .audo_box--left {
          margin: 0 32rpx;
          width: 113rpx;
          height: 113rpx;
          border: 4rpx solid #6DBEF9;
          border-radius: 50%;
          display: flex;
          >image {
            margin: auto;
            width: 91rpx;
            height: 91rpx;
            background: #349FFF;
            border-radius: 50%;
            padding: 32rpx 31rpx 32rpx 36rpx;
          }
        }
        .audo_box--right {
          margin-right: 32rpx;
          flex: 1;
          display: flex;
          align-items: center;
          >text {
            font-size: 28rpx;
            color: #14131F;
            letter-spacing: 1px;
          }
          >slider {
            margin: 0 18rpx;
            flex: 1;
            :global(.uni-slider-thumb) {
              border: 3rpx solid #349FFF;
              box-sizing: border-box;
            }
          }
        }
      }
      .content {
        margin-top: 20rpx;
        width: 691rpx;
        height: 0;
        flex: 1;
        font-size: 30rpx;
        color: #090909;
        line-height: 47rpx;
      }
    }
    .active {
      border-radius: 0;
      .content {
        overflow: auto;
      }
    }
  }
</style>