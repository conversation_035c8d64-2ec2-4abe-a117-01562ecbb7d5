<template>
  <view :style="{
    paddingLeft: conf.pageMargin,
    paddingRight: conf.pageMargin,
  }">
    <view class="information">
      <view class="header" v-if="conf.text || conf.moreType == 0">
        <view class="title">{{ conf.text }}</view>
        <view v-if="conf.moreType === '0'" class="more" @tap="toPage">查看更多</view>
      </view>
      <!-- 大图模式 -->
      <template v-if="conf.articleType === 'large'">
        <view v-if="articleList.length !== 0" class="content">
          <view v-for="(item, index) in articleList" :key="item.image" class="large-article" :style="{
            boxShadow: conf.boxShadow,
            border: conf.border,
            borderRadius: conf.borderRadius,
            marginTop: index === 0 ? 0 : conf.imageMargin,
          }">
            <image class="large-bg" :src="item.image" @click="handleClick(item)" mode="aspectFill" />
            <view class="large-title" :style="{
              background: conf.titleBg,
              color: conf.titleColor,
              fontWeight: conf.textType,
            }">
              {{ item.articleName }}
              <view class="large-time" :style="{
                color: conf.timeColor,
              }">{{ dayjs(item.publishTime).format("YYYY.MM.DD") }}</view>
            </view>
          </view>
        </view>
      </template>
      <!-- 左右列表 -->
      <template v-if="conf.articleType === 'around'">
        <view v-for="(item, index) in articleList" :key="item.articleName" class="card-box" :style="{
          boxShadow: conf.boxShadow,
          border: conf.border,
          borderRadius: conf.borderRadius,
          marginTop: index === 0 ? 0 : conf.imageMargin,
        }" @click="handleClick(item)">
          <view class="card-box__left">
            <view class="card-box__title" :style="{
              fontWeight: conf.textType,
            }">{{ item.articleName }}</view>
            <view class="card-box__date">{{
              dayjs(item.publishTime).format("YYYY.MM.DD")
            }}</view>
            <view class="card-box__info">
              <template v-if="conf.authorSwitch">
                <image class="user-icon" :src="item.image" mode="aspectFill" />
                <view class="user-name">{{ item.articleAuthor }}</view>
              </template>
              <template v-if="conf.viewsSwitch">
                <image class="eye" src="@/static/image/message/eye.svg" mode="aspectFill" />
                <view class="eye-num">{{
                  item.readCount >= 100
                    ? (item.readCount / 1000).toFixed(1) + "k"
                    : item.readCount
                }}</view>
              </template>
            </view>
          </view>
          <image class="card-box__right" :src="item.image" mode="aspectFill" />
        </view>
      </template>
      <!-- 瀑布流 -->
      <view v-if="conf.articleType === 'flow'">
        <Waterfall v-if="articleList.length !== 0" :value="articleList" :config="conf" :listStyle="{
          // width: '335rpx'
        }">
        </Waterfall>
      </view>
      <view v-if="conf.moreType === '1' && articleList.length !== 0" style="
          font-size: 26rpx;
          text-align: center;
          margin-top: 20rpx;
          color: rgba(20, 19, 31, 0.6);
        " @tap="toPage">查看更多</view>
      <view v-if="articleList.length === 0">暂无数据 </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed, defineComponent } from "vue";
import Waterfall from "@/components/custom-waterfalls-flow/custom-waterfalls-flow_item.vue";
import dayjs from "dayjs";
import request from "@/utils/request.js";
import { getRoute, goPage, pxToRpx } from "@/utils/tool.js";
import { getEnv } from "@/utils/getEnv";

// defineComponent({
// 	"custom-waterfalls-flow": Waterfall
// })

interface ArticleListItem {
  id: string;
  articleName: string;
  articleAuthor: string;
  publishTime: string;
  readCount: number;
  publicizeImgUrl: string;
  createTime: string;
  articleType: number;
  externalUrl: string;
  articleSource: number;
}

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      imageList: [],
    }),
  },
});

const toPage = () => {
  let url = "";
  if (conf.value.sourceType === "0") {
    url = "/pages/information/information";
  } else {
    url = "/pages/travelNotes/travelNotes";
  }
  goPage.push(url);
};

const conf = computed(() => {
  const style: any = {};
  const { config } = props;

  console.log(config);
  console.log("config.sourceTypeconfig.sourceType");

  // 数据来源
  fetchArticleData(config.sourceType?.split(",") || [1, 4]);

  // 卡片样式
  if (config.cardType === "white") {
    // 无底白边
    style.titleBg = "#fff";
  } else if (config.cardType === "projection") {
    // 卡片投影
    style.titleBg = "#fff";
    style.boxShadow = "0px 3px 6px 3px rgba(209,209,209,0.5)";
  } else if (config.cardType === "outline") {
    // 描边白底
    style.titleBg = "#fff";
    style.border = "1px solid #D6D6D6";
  } else if (config.cardType === "transparent") {
    // 透明底
    style.titleBg =
      "linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.52) 100%)";
    style.titleColor = "#fff";
    style.timeColor = "#D6D6D6";
  }
  // 文字样式
  if (config.textType === "bold") {
    // 加粗
    style.textType = "bold";
  } else {
    // 常规
    style.textType = "normal";
  }
  // 图片圆角
  if (config.borderRadius) {
    style.borderRadius = pxToRpx(config.borderRadius);
  }
  // 图片间距
  if (config.imageMargin) {
    style.imageMargin = pxToRpx(config.imageMargin);
  }
  // 页面间距
  if (config.pageMargin) {
    style.pageMargin = pxToRpx(config.pageMargin);
  }

  console.log("configconfigconfigconfig");
  console.log(config);

  // 模板
  return {
    ...config,
    ...style,
  };
});

const host = getEnv().VITE_IMG_HOST;

const current = ref(0);
const articleList = ref<ArticleListItem[]>([]);

const fetchArticleData = async (articleTypeList) => {
  try {
    const params = {
      articleTypeList,
      storeId: getRoute.params().storeId,
    };
    const { data = [] } = await request.get(`/article/h5/home/<USER>
    console.log(data);
    // articleList.value = data
    articleList.value = data.map((i) => ({
      ...i,
      image: host + (i.publicizeImgUrl || "").split(",")[0],
      createTime: dayjs(i.createTime).format("YYYY.MM.DD"),
    }));
  } catch (error) {
    console.log(error);
  }
};

const onChange = (e: { detail: { current: number } }) => {
  current.value = e.detail.current;
};

const onSelect = (index: number) => {
  current.value = index;
};

const handleClick = (item: ArticleListItem) => {
  if (item.articleSource == 2 && item.externalUrl) {
    request.get(`/article/h5/info/external`, { id: item.id });
    window.location.href = item.externalUrl.includes("https")
      ? item.externalUrl.trim()
      : `https://${item.externalUrl.trim()}`;
  } else {
    goPage.push(`/pages/articleDetail/articleDetail?id=${item.id}`);
  }
};

onMounted(() => {
  // fetchArticleData()
});
</script>
<style lang="scss" scoped>
.card-box {
  display: flex;
  overflow: hidden;
  height: 214rpx;
  border-radius: 12rpx;

  .card-box__left {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 18rpx 40rpx 18rpx 20rpx;
    flex: 1;

    .card-box__title {
      font-size: 30rpx;
      font-weight: 500;
      color: #090909;
      line-height: 42rpx;
      width: 396rpx;
      max-height: 84rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .card-box__date {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: rgba(20, 19, 31, 0.5);
    }

    .card-box__info {
      margin-top: auto;
      display: flex;
      align-items: center;

      .user-icon {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
      }

      .user-name {
        margin-left: 8rpx;
        font-size: 23rpx;
        font-weight: 400;
        color: #14131f;
        margin-right: 40rpx;
      }

      .eye {
        width: 32rpx;
        height: 32rpx;
      }

      .eye-num {
        font-size: 23rpx;
        opacity: 0.5;
        font-weight: 400;
        color: #14131f;
      }
    }
  }

  .card-box__right {
    width: 263rpx;
    height: 214rpx;
  }
}

.information {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .title {
      height: 50rpx;
      font-size: 36rpx;
      font-weight: bold;
      color: #14131f;
      line-height: 50rpx;
    }

    .more {
      font-size: 26rpx;
      color: rgba(20, 19, 31, 0.6);
      line-height: 1;
    }
  }

  .content {
    border-radius: 12rpx;
    position: relative;

    // height: 388rpx;

    .swiper {
      height: 100%;
      position: relative;
      border-radius: 12rpx;
      overflow: hidden;

      .swiper-item {
        display: block;
        text-align: center;
        background-color: orange;
      }

      .img {
        border-radius: 12rpx;
        width: 100%;
        height: 100%;
      }

      .extra {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 104rpx;
        padding: 16rpx 20rpx;
        background: linear-gradient(180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.25) 100%);

        .title {
          font-size: 30rpx;
          max-width: 390rpx;
          overflow: hidden;
          text-wrap: nowrap;
          text-overflow: ellipsis;
          font-weight: 500;
          color: #ffffff;
          line-height: 36rpx;
          margin-bottom: 4rpx;
          text-align: start;
        }

        .time {
          height: 33rpx;
          font-size: 24rpx;
          font-weight: 400;
          color: #ffffff;
          line-height: 33rpx;
          text-align: start;
        }
      }
    }

    .dots {
      display: flex;
      flex-direction: column;
      position: absolute;
      // z-index: 1;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);

      .dot-item {
        border-radius: 6rpx;
        width: 110rpx;
        height: 62rpx;
        margin-bottom: 20rpx;
        overflow: hidden;

        .img {
          height: 100%;
          width: 100%;
        }
      }

      .active {
        border: 3rpx solid white;
      }
    }
  }
}

.large-article {
  height: 388rpx;
  width: 100%;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;

  .large-bg {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .large-title {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 15rpx 20rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: #14131f;

    .large-time {
      font-size: 24rpx;
      font-weight: 400;
      color: #14131f;
    }
  }
}
</style>
