<template>
  <view class="eye" @tap="toggle">
    <image v-show="modelValue" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png" mode="widthFix" />
    <image v-show="!modelValue" class="icon-eye" src="@/static/image/icon_inputeye.png" mode="widthFix" />
  </view>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "EyeToggle",
  props: {
    modelValue: {
      type: Boolean,
      required: true
    }
  },
  emits: ["update:modelValue"],
  methods: {
    toggle() {
      this.$emit("update:modelValue", !this.modelValue);
    }
  }
});
</script>

<style scoped lang="scss">
.eye {
  width: 60rpx;
  text-align: right;

  .icon-eye {
    width: 30rpx;
    height: 15rpx;
  }

  .icon-open-eye {
    width: 30rpx;
    height: 23rpx;
  }
}
</style>
