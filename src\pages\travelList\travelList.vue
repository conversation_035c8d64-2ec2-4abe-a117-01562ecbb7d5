<template>
  <view class="travel">
    <view v-if="travelList.length" class="travelList">
      <view class="item" @click="
        Tool.goPage.push(
          '/pages/travelDetails/travelDetails?journeyId=' + item.journeyId
        )
        " v-for="(item, index) in travelList" :key="index">
        <image class="item__img" :src="imgHost + item.journeyPictures" mode="aspectFill" />
        <view class="item__info">
          <view class="item__info__title">{{ item.journeyName }}</view>
          <view class="item__info__tips">{{ item.journeyDays }}天 | {{ item.journeyPlaces }}个地点</view>
          <view class="item__info__place">
            <image src="@/static/image/travel/sign.svg" mode="aspectFit" />
            <view>
              <view v-for="(itemPlace, indexPlace) in item.journeyCity
                ? item.journeyCity.split('-')
                : []" :key="indexPlace">
                <view class="tips" v-if="indexPlace">-</view>
                <view class="tag">{{ itemPlace }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="item__button">
          <image src="@/static/image/travel/edit.svg" mode="aspectFit" @click.stop="
            goPage.push(
              '/pages/travelDetails/travelDetails?edit=true&journeyId=' +
              item.journeyId
            )
            " />
          <image src="@/static/image/travel/del.svg" mode="aspectFit" @click.stop="del(item.journeyId)" />
        </view>
      </view>
    </view>
    <view v-else class="nullData">
      <image class="icon" src="@/static/image/travel/404.svg" mode="aspectFit" />
      <view class="text">暂无行程</view>
    </view>
    <view class="button">
      <view class="button__left">
        <y-chat>智能生成</y-chat>
      </view>
      <view class="button__right" @click="goPage.push('/pages/travelDetails/travelDetails?edit=true')">创建行程</view>
    </view>
  </view>
</template>
<script setup>
import request from "@/utils/request.js";
import { ref } from "vue";
import { getEnv } from "@/utils/getEnv";

const imgHost = ref(getEnv().VITE_IMG_HOST);
const travelList = ref([]);

const getData = () => {
  request
    .get("/user/journey/pageList", { current: 1, size: 10 })
    .then(({ data }) => {
      travelList.value = data.data;
    });
};
const del = (journeyId) => {
  uni.showModal({
    content: "删除该行程？",
    success: function (res) {
      if (res.confirm) {
        request.put("/user/journey/delete?journeyId=" + journeyId).then(() => {
          getData();
          uni.showToast({
            icon: "none",
            title: "删除成功",
          });
        });
      }
    },
  });
};
onShow(getData);
</script>
<style lang="scss" scoped>
.travel {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;

  .travelList,
  .nullData {
    flex: 1;
    overflow: auto;
  }

  .travelList {
    background: #f1f1f1;
    display: flex;
    flex-direction: column;
    padding: 20rpx;

    .item {
      margin: 10rpx;
      width: 690rpx;
      // height: 168rpx;
      background: #fff;
      border-radius: 12rpx;
      padding: 14rpx;
      display: flex;
      position: relative;

      .item__img {
        flex-shrink: 0;
        width: 140rpx;
        height: 140rpx;
        border-radius: 12rpx;
        background: #f1f1f1;
      }

      .item__info {
        margin-left: 20rpx;

        .item__info__title {
          width: 78%;
          margin-top: 5rpx;
          font-weight: bold;
          font-size: 28rpx;
          color: #14131f;
          line-height: 30rpx;
        }

        .item__info__tips {
          margin-top: 14rpx;
          font-size: 24rpx;
          color: #14131f;
          line-height: 24rpx;
        }

        .item__info__place {
          margin-top: 24rpx;
          display: flex;

          >image {
            flex-shrink: 0;
            width: 26rpx;
            height: 29rpx;
          }

          >view {
            margin-left: 14rpx;
            display: flex;
            flex-wrap: wrap;
            gap: 7rpx 0;

            >view {
              display: flex;
              align-items: center;

              .tips {
                margin: 0 7rpx;
                font-size: 22rpx;
                color: #349fff;
                line-height: 34rpx;
              }

              .tag {
                min-width: 61rpx;
                height: 34rpx;
                background: #edf7ff;
                border-radius: 6rpx;
                border: 1rpx solid #349fff;
                font-size: 22rpx;
                color: #349fff;
                text-align: center;
                line-height: 34rpx;
                padding: 0 8rpx;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .item__button {
        position: absolute;
        top: 7rpx;
        right: 12rpx;
        display: flex;
        gap: 19rpx;

        >image {
          width: 50rpx;
          height: 50rpx;
        }
      }
    }
  }

  .nullData {
    position: relative;

    .icon {
      margin: 256rpx 72rpx;
      width: 606rpx;
      height: 606rpx;
    }

    .text {
      position: absolute;
      left: 50%;
      top: 663rpx;
      transform: translateX(-50%);
      font-size: 28rpx;
      color: #14131f;
    }
  }

  .button {
    width: 100%;
    display: flex;
    padding: 15rpx;

    .button__left,
    .button__right {
      margin: 15rpx;
      flex: 1;
      height: 88rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 36rpx;
    }

    .button__left {
      background: rgba(52, 159, 255, 0.17);
      color: #349fff;
    }

    .button__right {
      background: rgba(52, 159, 255, 1);
      color: #fff;
    }
  }
}
</style>
