<template>
	<y-nav-bar fontColor="#050505" backBtn solid>常用人</y-nav-bar>
	<view class="contacts-list">
		<!-- 列表 -->
		<template v-if="contactsPage === 'listPage'">
			<view v-show="contactsList.length > 0">
				<view class="contacts-list-box">
					<view class="contacts-list-item" v-for="(item, index) in contactsList" :key="index">
						<view class="left">
							<image v-if="item.faceImageUrl" class="avatar" :src="imgHost + item.faceImageUrl" mode="aspectFill" />
							<view>
								<view class="name">
									<y-font-weight>{{ item.name }}</y-font-weight>
								</view>
								<view class="idCard">
									身份证
									<text>{{ desensitization(item.idCard, "idCard") }}</text>
								</view>
							</view>
						</view>
						<view class="right">
							<y-svg @tap="onEditContacts(item)" name="edit-icon" class="icon" />
							<image class="icon" @click="onDelContacts(item.id)" src="@/static/image/trashcan-icon.png"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
				<view class="y-fixed-bottom">
					<y-button :disable="false" @click="addContacts">新增</y-button>
				</view>
			</view>
			<!-- 地址缺省页 -->
			<view class="empty" v-show="contactsList.length === 0">
				<y-empty>暂无常用人</y-empty>
				<view class="y-fixed-bottom">
					<y-button :disable="false" @click="addContacts">添加</y-button>
				</view>
			</view>
		</template>

		<!-- 编辑地址 -->
		<view v-if="contactsPage === 'editPage'">
			<view class="y-list">
				<view class="y-list-item">
					<view class="y-list-item-left">姓名</view>
					<input v-model="params.name" placeholder-class="y-list-item-placeholder" class="y-list-item-input" type="text"
						placeholder="与证件姓名一致" />
				</view>
				<view class="y-list-item">
					<view class="y-list-item-left">身份证号</view>
					<input v-model="params.idCard" placeholder-class="y-list-item-placeholder" class="y-list-item-input"
						type="text" placeholder="请保持与证件一致" />
				</view>
			</view>
			<view class="face-box">
				<view>请上传一张脸部照片，确保为本人照片，人脸入闸时使用</view>
				<y-upload v-model="params.faceImageUrl" style="margin-top: 34rpx" upload />
				<view class="tip-title">注意事项</view>
				<view class="tip-content">1，正对手机，背景尽量纯白色。</view>
				<view class="tip-content">2，露出额头和耳朵，无逆光。</view>
				<view class="tip-content">3，确保照片中均为本人照片，不能上传其他人照片，否则会影响开门。</view>
				<view class="tip-content">4，人脸铺满整个手机屏幕，不要太远或太近</view>
			</view>
			<view class="y-fixed-bottom">
				<y-button @tap="onSubmitContacts" :disable="!canSubmit">保存</y-button>
			</view>
			<view class="agreement">
				<view class="agreement-title">
					<y-checkbox style="margin-top: -4rpx" type="radio" :checked="agreementVisible"
						@onCheck="onCheckAgreement"></y-checkbox>
					<text style="color: #050505">阅读并同意以下内容</text>
				</view>
				<view style="position: relative">
					<view class="agreement-content" :class="showAllLine ? '' : 'agreement-two'">
						您已知晓您在易旅宝录入的常用人身份证件信息，将用于您预订门票等所有需要实名制的旅游产品，并在使用时进行验证，请确保此信息的真实有效。易旅宝将通过加密等方式保护此信息，且仅在有具体交易时授权提供给相关第三方。
					</view>
					<text v-if="!showAllLine" class="zk" @click="showAllLine = true">展开</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import { getRoute, desensitization } from "@/utils/tool.js"
import request from "@/utils/request.js"
import { getEnv } from "@/utils/getEnv";

const stase = reactive({
	userInfo: {}
})
const showAllLine = ref(false)
const imgHost = getEnv().VITE_IMG_HOST
//页面状态
const contactsPage = ref("listPage")

//是否同意协议
const agreementVisible = ref(false)
const onCheckAgreement = () => {
	agreementVisible.value = !agreementVisible.value
}

const params = reactive({
	faceImageUrl: "",
	idCard: "",
	name: "",
	phone: ""
})

//重置请求参数
const resetParams = () => {
	for (let key in params) {
		params[key] = ""
		if (key === "id") delete params[key] //新增不能传 id
	}
}

const canSubmit = computed(() => {
	return params.name && params.idCard && agreementVisible.value
})

//添加常用人
const addContacts = () => {
	Tool.goPage.push("/pages/contactsList/contactsList?page=editPage")
}
//修改常用人
const onEditContacts = item => {
	Tool.goPage.push("/pages/contactsList/contactsList?page=editPage&id=" + item.id)
}

//删除常用人
const onDelContacts = async id => {
	const { code, data } = await request.delete(`/appContract/info/${id}`)
	uni.showToast({
		title: "删除成功"
	})
	getContactsList()
}
//获取常用人列表
const contactsList = ref([])
const getContactsList = async (editId, idCard) => {
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { code, data } = await request.get(`/appContract/list`)
	uni.hideLoading()
	contactsList.value = data
	if (data.length === 0) return
	//当前是编辑页，遍历赋值
	let editInfo = {}
	if (editId) editInfo = data.find(e => editId == e.id)
	if (idCard) editInfo = data.find(e => idCard == e.idCard)
	console.log(editId, idCard, editInfo, data);

	params.idCard = editInfo.idCard
	params.name = editInfo.name
	params.id = editInfo.id
	params.faceImageUrl = editInfo.faceImageUrl
}

//提交修改添加常用人请求
const onSubmitContacts = async () => {
	if (!agreementVisible.value) {
		uni.showToast({
			icon: "none",
			title: "请先阅读并同意协议"
		})
		return
	}
	if (!canSubmit.value) return
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	if (params.id) {
		//修改
		await request.put(`/appContract/info`, params)
	} else {
		//新增
		await request.post(`/appContract/info`, params)
	}
	uni.hideLoading()
	uni.showToast({
		title: `${params.id ? "修改" : "添加"}成功`
	})
	Tool.goPage.back()
}

// 设置默认地址
const setDefaultAddress = e => {
	params.address.isDefault = e.detail.value
}

onShow(() => {
	const page = getRoute.params().page
	const { id: editId, idCard } = getRoute.params()
	contactsPage.value = page ? page : "listPage"
	getContactsList(editId, idCard)
})
</script>
<style lang="scss" scoped>
.contacts-list {
	background: #f1f1f1;
	min-height: 100%;
	padding-bottom: 200rpx;
	overflow: hidden;

	.prov {
		display: flex;
		align-items: center;
	}

	.contacts-list-box {
		background-color: #f1f1f1;
		overflow: hidden;

		.contacts-list-item {
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 30rpx;
			padding: 30rpx;
			height: 140rpx;
			background-color: #fff;

			&:not(:last-child) {
				border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);
			}

			.left {
				display: flex;
				flex: 1;
				align-items: center;

				.avatar {
					width: 80rpx;
					height: 80rpx;
					margin-right: 20rpx;
					border-radius: 50%;
				}

				.name {
					margin-bottom: 15rpx;
					font-size: 32rpx;
					font-weight: 500;
					color: #050505;
				}

				.idCard {
					font-size: 28rpx;
					font-weight: 400;

					color: rgba(20, 19, 31, 0.5);

					text {
						display: inline-block;
						margin-left: 40rpx;
					}
				}
			}

			.right {
				margin-left: auto;

				.icon {
					width: 50rpx;
					height: 50rpx;

					&:not(:last-child) {
						margin-right: 26rpx;
					}
				}
			}
		}
	}

	.agreement {
		margin: 40rpx 40rpx 0;
		text-align: center;

		.agreement-title {
			display: flex;
			align-items: center;
			margin-bottom: 13rpx;
			font-size: 26rpx;
			font-weight: 400;
			color: #050505;
			line-height: 37rpx;

			text {
				margin-left: 10rpx;
			}
		}

		.agreement-content {
			position: relative;
			text-align: left;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
			line-height: 36rpx;
		}
	}

	.btnnn {
		position: fixed;
		bottom: 30rpx;
		left: 0;
		right: 0;
	}

	.agreement-two {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.zk {
		position: absolute;
		width: 70rpx;
		text-align: right;
		background: #f1f1f1;
		bottom: 2rpx;
		right: 0rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: var(--theme-color);
	}
}

.face-box {
	margin: 30rpx;
	padding: 40rpx;
	border-radius: 12rpx;
	background-color: #fff;
	font-size: 28rpx;
	font-weight: 400;
	color: #000;
}

.tip-title {
	font-size: 26rpx;
	color: #14131f;
	font-weight: 500;
	margin-top: 33rpx;
	margin-bottom: 25rpx;
}

.tip-content {
	line-height: 40rpx;
	font-size: 24rpx;
	opacity: 0.52;
	// margin-bottom: 16rpx;
}
</style>
