<template>
  <view class="point_pop--top--right">
    <view class="audo_box">
      <image
        :src="isGuideExplaining ? pauseIcon : playIcon"
        mode="aspectFit"
        @click="toggleGuideExplain"
      />
      <view></view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import pauseIcon from "@/static/image/tour/tour_pause.svg";
import playIcon from "@/static/image/tour/tour_play.svg";
import eventBus from "@/utils/eventBus.js";

// 定义 props，接收当前点位信息
const props = defineProps({
  lastPoint: {
    type: Object,
    default: null
  }
});

// API 地址
const generateIntroductionApi = 'https://dev-gcluster.shukeyun.com/algorithm/travel-assistant/generate_introduction';
const ttsApi = 'https://canary-gcluster.shukeyun.com/algorithm/data-convert-server/tts/';

// 普通音频播放相关状态
const isPlaying = ref(false);
let innerAudioContext = null;
let timeId = null;

// 导览讲解相关状态变量
const speechQueue = ref([]); // 语音队列
const isPlayingSpeech = ref(false); // 是否正在播放语音
const currentAudio = ref(null); // 当前播放的音频对象
const isGuideExplaining = ref(false); // 是否正在进行导览讲解

/**
 * 切换导览讲解状态
 * 如果当前没有讲解，则开始讲解
 * 如果当前正在讲解，则停止讲解
 */
const toggleGuideExplain = () => {
  if (isGuideExplaining.value) {
    stopGuideExplain();
  } else {
    startGuideExplain();
  }
};

/**
 * 开始导览讲解
 */
const startGuideExplain = () => {
  if (!props.lastPoint) {
    uni.showToast({
      title: '暂无景点信息',
      icon: 'none'
    });
    return;
  }
  
  // 直接发送 guideExplain 事件
  eventBus.emit('guideExplain', {
    name: props.lastPoint.content,
  });
};

/**
 * 停止导览讲解
 */
const stopGuideExplain = () => {
  // 直接发送 stopGuideExplain 事件
  eventBus.emit('stopGuideExplain');
};

/**
 * 模拟获取导览讲解文案
 * @param {Object} data 景点数据
 * @returns {Promise} 返回讲解文案
 */
const getGuideExplain = async (content) => {
  const { data } = await reqAi.post(generateIntroductionApi, { scenicName: content.name });
  const explainText = data[0].intro;
  return explainText;
};

/**
 * 将文案切割成句子
 * @param {String} content 文案内容
 * @returns {Array} 切割后的句子数组，每项包含 text 和 voice 字段
 */
const splitContentToSentences = (content) => {
  // 使用正则表达式按句号、问号、感叹号等标点符号分割文本
  const sentences = content.split(/(?<=[。！？.!?])/);
  
  // 过滤掉空句子并格式化为所需结构
  return sentences
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0)
    .map(sentence => ({
      text: sentence,
      voice: ''
    }));
};

/**
 * 模拟文字转语音接口
 * @param {String} text 需要转换的文字
 * @returns {Promise} 返回语音 URL
 */
const textToSpeech = async (text) => {
  const { data } = await reqAi.post(ttsApi, { input_text: text, speaker: 'xingxing' });
  // 将 Base64 数据转换为临时 URL
  const binary = atob(data);
  const buffer = new ArrayBuffer(binary.length);
  const view = new Uint8Array(buffer);
  for (let i = 0; i < binary.length; i++) {
    view[i] = binary.charCodeAt(i);
  }
  const blob = new Blob([buffer], { type: "audio/wav" });
  const url = URL.createObjectURL(blob);
  return url;
};

/**
 * 播放下一段语音
 * 当前段语音播放完毕后自动调用
 */
const playNextSpeech = () => {
  // 停止当前播放的语音
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
  
  // 如果队列为空，结束播放
  if (speechQueue.value.length === 0) {
    isPlayingSpeech.value = false;
    
    // 所有语音播放完毕，讲解结束
    if (isGuideExplaining.value) {
      isGuideExplaining.value = false;
      // 发送讲解状态变更事件
      eventBus.emit('guideExplainStatus', { status: 'ended' });
      // 发送隐藏气泡事件
      eventBus.emit('textUpdate', { text: '', hide: true });
    }
    return;
  }
  
  // 获取队列中的第一项
  const currentSpeech = speechQueue.value[0];
  
  // 如果没有语音 URL，可能还在请求中，等待下一次触发
  if (!currentSpeech.voice) {
    isPlayingSpeech.value = false;
    return;
  }
  
  // 发送文本内容给 tutu.vue 显示
  eventBus.emit('textUpdate', { text: currentSpeech.text });
  
  // 创建音频对象
  const audio = new Audio(currentSpeech.voice);
  currentAudio.value = audio;
  
  // 播放语音
  audio.play();
  isPlayingSpeech.value = true;
  isPlaying.value = true;
  
  // 监听播放结束事件
  audio.onended = () => {
    // 移除已播放的项目
    speechQueue.value.shift();
    // 播放下一段
    playNextSpeech();
  };
};

/**
 * 处理语音队列
 * 开始批量获取语音并播放
 * @param {Array} sentences 句子数组
 */
const processSpeechQueue = async (sentences) => {
  // 清空之前的队列
  speechQueue.value = sentences;
  
  // 开始获取每个句子对应的语音
  for (let i = 0; i < sentences.length; i++) {
    try {
      // 获取语音URL
      const voiceUrl = await textToSpeech(sentences[i].text);
      
      // 更新队列中的语音URL
      if (i < speechQueue.value.length) {
        speechQueue.value[i].voice = voiceUrl;
        
        // 如果是第一项且当前没有语音在播放，开始播放
        if (i === 0 && !isPlayingSpeech.value) {
          playNextSpeech();
        }
      }
    } catch (error) {
      console.error('获取语音失败:', error);
    }
  }
};

/**
 * 停止当前所有语音播放
 */
const stopAllSpeech = () => {
  // 停止当前播放的语音
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
  
  // 停止普通音频播放
  if (innerAudioContext) {
    innerAudioContext.pause();
  }
  
  // 清空队列
  speechQueue.value = [];
  isPlayingSpeech.value = false;
  isPlaying.value = false;
  
  // 如果当前正在讲解，发送讲解结束的状态
  if (isGuideExplaining.value) {
    isGuideExplaining.value = false;
    // 发送讲解状态变更事件
    eventBus.emit('guideExplainStatus', { status: 'ended' });
    // 发送隐藏气泡事件
    eventBus.emit('textUpdate', { text: '', hide: true });
  }
};

// 创建音频对象
const createAudioContext = () => {
  if (!innerAudioContext) {
    innerAudioContext = uni.createInnerAudioContext();
    innerAudioContext.onEnded(() => {
      isPlaying.value = false;
      timeId && clearInterval(timeId);
    });
  }
  return innerAudioContext;
};

// 普通音频播放控制
const togglePlay = () => {
  const audioContext = createAudioContext();
  if (!audioContext.src || !audioContext.src.trim()) {
    uni.showToast({
      title: '暂无音频',
      icon: 'none'
    });
    return;
  }
  
  isPlaying.value = !isPlaying.value;
  
  if (timeId) {
    clearInterval(timeId);
    timeId = null;
  }
  
  if (isPlaying.value) {
    audioContext.play();
  } else {
    audioContext.pause();
  }
};

// 订阅导览讲解事件
onMounted(() => {
  // 添加点击事件监听器，当点击其他点位时停止当前语音播放
  eventBus.on('pointClick', () => {
    stopAllSpeech();
  });

  eventBus.on('guideExplain', async (data) => {
    // 停止之前的语音播放
    stopAllSpeech();
    
    try {
      // 设置正在讲解状态
      isGuideExplaining.value = true;
      // 发送讲解状态变更事件
      eventBus.emit('guideExplainStatus', { status: 'started' });
      
      // 先显示"让我想一下~"的提示
      eventBus.emit('textUpdate', { text: "让我组织一下语言~" });
      
      // 获取景点讲解文案
      const explainContent = await getGuideExplain(data);
      
      // 将文案分割成句子
      const sentences = splitContentToSentences(explainContent);
      
      // 处理语音队列，开始播放
      processSpeechQueue(sentences);
    } catch (error) {
      console.error('导览讲解失败：', error);
      // 发生错误时，恢复状态
      isGuideExplaining.value = false;
      eventBus.emit('guideExplainStatus', { status: 'ended' });
      uni.showToast({
        title: "该处没有讲解内容",
        icon: 'none'
      });
    }
  });
  
  // 监听停止导览讲解事件
  eventBus.on('stopGuideExplain', () => {
    // 如果当前正在讲解，停止讲解
    if (isGuideExplaining.value) {
      stopAllSpeech();
    }
  });
  
  // 监听讲解状态变更事件
  eventBus.on('guideExplainStatus', (data) => {
    isGuideExplaining.value = data.status === 'started';
  });
});
const clear = () =>{
  // 停止所有语音播放
  stopAllSpeech();
  if (innerAudioContext) {
    innerAudioContext.destroy();
    innerAudioContext = null;
  }
  // 发送隐藏气泡事件
  eventBus.emit('textUpdate', { text: '', hide: true });
  eventBus.off('guideExplain');
  eventBus.off('stopGuideExplain');
  eventBus.off('guideExplainStatus');
  eventBus.off('pointClick'); // 移除点击事件监听器
}
// 组件卸载时取消订阅
onUnmounted(clear);
onHide(()=>{
  stopAllSpeech();
  // 发送隐藏气泡事件
  eventBus.emit('textUpdate', { text: '', hide: true });
});
</script>

<style lang="scss" scoped>
.point_pop--top--right {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 19rpx;
  .audo_box {
    margin: 18rpx 0 14rpx;
    width: 154rpx;
    height: 154rpx;
    background: rgba(52, 159, 255, 0.1);
    border-radius: 77rpx 0rpx 0rpx 77rpx;
    display: flex;
    position: relative;
    > image {
      margin: auto;
      width: 91rpx;
      height: 91rpx;
      background: #349fff;
      border-radius: 50%;
      padding: 32rpx 31rpx 32rpx 36rpx;
    }
    > view {
      position: absolute;
      top: 50%;
      left: 50%;
      pointer-events: none;
      transform: translate(-50%, -50%);
      width: 109rpx;
      height: 109rpx;
      opacity: 0.47;
      border-radius: 50%;
      box-shadow: 0 0 0 4rpx #6dbef9;
    }
  }
}
</style> 