<style lang="scss" scoped>
.more-date {
	width: 45rpx;
	height: 45rpx;
}
.calendar {
	// position: fixed;
	// left: 0%;
	// right: 0%;
	// bottom: 0;
	// padding: 0 46rpx 66rpx;
	background-color: #fff;
	.header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 33rpx;
		.left {
			font-size: 40rpx;
			font-weight: 500;
			color: #000000;
		}
		.right {
		}
	}
	.month-box {
		margin-bottom: 33rpx;
		.month-title {
			padding-bottom: 21rpx;
			font-size: 36rpx;
			font-weight: 400;
			color: #000000;
			border-bottom: 1px solid #e5e5e5;
		}
		.week-box {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 35rpx;
			margin-bottom: 10rpx;
			view {
				display: flex;
				justify-content: center;
				// $w: 94rpx;
				// width: $w;
				flex: 1;
			}
			.weekend {
				color: var(--theme-color);
			}
		}
		.day-box {
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
			// margin: 0 -17px;
			> .day {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				// $w: 89rpx;
				width: calc(100% / 7);
				height: 121rpx;
				text-align: center;
				border-radius: 23rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #000000;
				transition: $transition-time;
				.price {
					font-size: 24rpx;
					font-weight: 400;
					color: #050505;
				}
			}
			> .active {
				background: var(--theme-color);
				color: #fff;
				.price {
					color: #fff;
				}
			}
		}
	}
}
</style>
<template>
	<view @click="onShowMoreDay" style="z-index: 2">
		<slot>
			<image
				class="more-date"
				src="@/static/image/moreDate-icon.png"
				mode="widthFix"></image>
		</slot>
	</view>

	<y-popup title="请选择日期" v-model="popModel">
		<view class="calendar">
			<view class="month-box" v-for="(item, index) in monthList" :key="index">
				<view class="month-title">{{ item.year }}年{{ item.month }}月</view>
				<view class="week-box">
					<view class="weekend">日</view>
					<view class="">一</view>
					<view class="">二</view>
					<view class="">三</view>
					<view class="">四</view>
					<view class="">五</view>
					<view class="weekend">六</view>
				</view>
				<view class="day-box">
					<template v-for="(e, i) in item.list" :key="i">
						<template v-if="timecompare(e.date)">
							<view class="day">
								<template v-if="e.day">
									<view style="color: #c5c5c5">{{ e.day }}</view>
									<view class="price" style="color: #c5c5c5">¥{{ e.price }}</view>
								</template>
							</view>
						</template>
						<template v-else>
							<view
								class="day"
								:class="{ active: e.date === props.date }"
								@click="onSetActiveDate(e)">
								<template v-if="e.day">
									<view>{{ e.day }}</view>
									<view class="price">¥{{ e.price }}</view>
								</template>
							</view>
						</template>
					</template>
				</view>
			</view>
		</view>
	</y-popup>
</template>
<script setup>
import { toRefs, reactive, ref, watch, computed } from "vue"
import dayjs from "dayjs"
const props = defineProps({
	date: {
		type: String,
		default: ""
	},
	price: {
		type: Number,
		default: 0
	},
	// 时间可选范围，第一个为开始时间，第二个为结束时间，不填默认不限制
	dateRange: {
		type: Array,
		default: () => [dayjs().format("YYYY-MM-DD")]
	},
	// 价格列表，如果传了价格列表，则日历数据使用价格列表的，价格列表没有的日期，置灰不可选中
	// 数据结构：[{day: '2022-08-08',price: '0.01'}]
	priceList: {
		type: Array,
		default: () => []
	}
})
const emits = defineEmits(["onChangeDate"])

const popModel = ref(false)
//显示日历
const onShowMoreDay = () => {
	popModel.value = true
}
//时间比较
const timecompare = targetDate => {
	let isDisable = false
	const oneDay = 1000 * 60 * 60 * 24

	// 新增：判断是否早于当天
	if (dayjs(targetDate).isBefore(dayjs(), 'day')) {
		isDisable = true
	}

	// 原有时间范围判断
	if (props.dateRange.length) {
		const [startDate, endDate] = props.dateRange
		if (startDate && dayjs(targetDate).isBefore(startDate, "day")) {
			isDisable = true
		}
		if (endDate && dayjs(targetDate).isAfter(endDate, "day")) {
			isDisable = true
		}
	}
	
	// 如果有价格列表，判断日期是否在价格列表中
	if (props.priceList && props.priceList.length > 0) {
		const priceItem = props.priceList.find(item => item.day === targetDate)
		if (!priceItem) {
			isDisable = true
		}
	}
	
	return isDisable
}

// 获取指定日期的价格
const getDatePrice = (targetDate) => {
	if (props.priceList && props.priceList.length > 0) {
		// 如果价格列表有，则返回价格列表中的价格
		const priceItem = props.priceList.find(item => item.day === targetDate)
		console.log('props.priceList----', targetDate, props.priceList, priceItem)
		return priceItem ? priceItem.price : 0
	}else{
		// 如果价格列表没有，则不是动态定价，返回默认价格
		return props.price
	}
}

/* 返回指定月份的日期表 */
const getMonthList = (calendarYear, calendarMonth) => {
	// new Date() 中的最后一个参数为 0，表示的是当月的最后一天，
	// 因此我们通过这一句获得的就是传入的年月的当月最后一天。
	let date = new Date(calendarYear, calendarMonth, 0)
	// 获得当月的最后一天的日期号，就知道了当月有多少天
	const days = date.getDate()
	// setDate(1) 的作用是把日期变为当月的第一天
	date.setDate(1)
	// 在通过 getDay()，就能知道当月的第一天是星期几，并偏移对应天数
	let delay = date.getDay()
	const list = Array(days + delay)
		.fill("")
		.map((e, i) => {
			const dayObj = {
				day: "",
				date: "",
				price: ""
			}
			if (i >= delay) {
				dayObj.day = i - delay + 1
				const dateStr = `${calendarYear}-${
					calendarMonth < 10 ? "0" : ""
				}${calendarMonth}-${dayObj.day < 10 ? "0" : ""}${dayObj.day}`
				dayObj.date = dateStr
				dayObj.price = getDatePrice(dateStr)
			}
			return dayObj
		})
	return list
}




const monthList = ref()
watch(() => props.priceList, (newVal) => {
	// 获取当前月份开始连续 12 个月的数组
	let curYear = new Date().getFullYear()
	let curMonth = new Date().getMonth() + 1
	const mList = Array(12)
		.fill("")
		.map(e => {
			const monthObj = {
				year: curYear,
				month: curMonth,
				list: getMonthList(curYear, curMonth)
			}
			if (curMonth === 12) {
				curMonth = 1
				curYear++
			} else {
				curMonth++
			}
			return monthObj
		})
	monthList.value = mList
	console.log('monthList----', monthList.value)
}, { immediate: true })

//设置选中日期
const onSetActiveDate = e => {
	// if (!timecompare(e.date)) return;
	if (!e.day) return
	if (timecompare(e.date)) return // 如果日期被禁用，则不可选
	emits("onChangeDate", e.date, e.price)
	popModel.value = false
}
</script>
