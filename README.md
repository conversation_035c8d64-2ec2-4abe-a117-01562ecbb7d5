# 易旅宝

本项目使用 `uni-app` 框架，使用 `cli` 方式开发，使用 `vscode` 开发即可，无需下载 `HbuilderX`

## 安装依赖

```
pnpm install
```

## husky 安装

从 git 下载下来的 husky 如果不生效，需要删除.husky 目录，重新创建。

```
# 生成husky目录
npx husky install
# 生成脚本文件
npx husky add .husky/pre-commit "npm run lint"
```

在 pre-commit 中添加如下内容

```
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# set -e
# set -x
trap 'echo "Error at line $LINENO"; exit 1' ERR

# 注意这里加了 || true
files=$(git diff --cached --name-only --diff-filter=AM | grep -E '\.(ts|tsx|vue)$' || true)

if [ -z "$files" ]; then
  echo "No changed ts/tsx/vue files to check."
  exit 0
fi

echo "Running ESLint on the following files:"
echo "$files"

# 用 xargs -r 只有在有输入时才执行
echo "$files" | xargs -r npx eslint

echo "All files passed ESLint."
exit 0

```

## 运行

打开 `vscode` 运行 `package.json` 里的脚本，比如：
本地开发 H5 开发环境：`pnpm dev:h5:dev`
本地开发 H5 测试环境：`pnpm dev:test`
本地开发 H5 生产环境：`pnpm dev:master`

## 部署

传到对应的远程 git 分支即可自动构建：`master / canary / test / dev`

## 注意点

#### 店铺 id

- 项目运行需要添加 url 参数 storeId，来指定特定的店铺。开发时：H5 可直接修改地址栏参数，小程序可通过添加编译模式来添加参数
- 如何获取店铺 id？初始化时会在 App.vue 将店铺 id 存进全局对象 globalData 里，以供全局调用

#### 路由跳转

- 跳转路由请使用封装的方法 goPage，不能使用 uni 默认的跳转方法
- 此方法作用是在跳转时带上参数 storeId，保证项目运行过程中 url 始终带有店铺 id，防止用户分享页面时丢失店铺 id

#### 登录

本系统没有内置登录功能，登录功能抽离成独立的 cas 系统，它的运行方式是这样的：

- 进入业务系统时，如没有登录，接口会返回 40001 状态码
- 业务系统携带 `appId` 参数（唯一标识符，在 cas 系统中申请），跳转到 cas 系统去登录
- 登录成功，跳转回业务系统并携带参数 `tk`
- 业务系统拿 `tk` 请求后端接口 `setCookie`，完成登录
-
