import { getEnv } from '@/utils/getEnv';

const { VITE_UPLOAD_HOST, VITE_UPLOAD_HOST__DEV } = getEnv();
// 系统页面
const designerConf = {
  home: '/pages/home/<USER>',
  tour: '/pages/tourList/tourList',
  order: '/pages/order/order',
  my: '/pages/my/my',
  ticketList: '/pages/ticketList/ticketList',
  travelCardList: '/pages/travelCardList/travelCardList?index=2',
  information: '/pages/information/information',
  travelNotes: '/pages/travelNotes/travelNotes',
};

// 内部辅助函数
export const isPlainObject = (obj: object) => {
  return obj && typeof obj === 'object' && obj.constructor === Object;
};
const formatPath = (params) => {
  let newParams = {};
  let storeId = Tool.getRoute.params().storeId;

  // // #ifdef H5
  // storeId = getApp().globalData.storeId
  // // #endif
  // // #ifdef MP-WEIXIN
  // storeId = this.$scope.globalData.storeId
  // // #endif
  // console.log("getApp().globalData.storeId", getApp().globalData.storeId)

  // if (!storeId) {
  // 	storeId = Tool.getRoute.params().storeId
  // }

  // 设计器枚举值
  if (designerConf[params]) {
    params = designerConf[params];
  }
  if (typeof params === 'string') {
    if (params.indexOf('?') === -1) {
      newParams.url = `${params}?storeId=${storeId}`;
    } else {
      const p = params.split('?')[1];
      if (p.indexOf('storeId') === -1) {
        // 没有则添加
        newParams.url = `${params}&storeId=${storeId}`;
      } else {
        // 已有 storeId 就不用添加了
        newParams.url = `${params}`;
      }
    }
  } else {
    const path = params.url;
    newParams = { ...params };
    if (path.indexOf('?') === -1) {
      newParams.url = `${path}?storeId=${storeId}`;
    } else {
      const p = params.split('?')[1];
      if (p.indexOf('storeId') === -1) {
        // 没有则添加
        newParams.url = `${params}&storeId=${storeId}`;
      } else {
        // 已有 storeId 就不用添加了
        newParams.url = `${params}`;
      }
    }
  }

  // 添加失败回调
  newParams.fail = (err) => {
    // goPage.replace("/pages/error/404")
  };
  return newParams;
};
const goLoadingPage = () => {
  const replacePath = location.href.split('#')[1];
  if (replacePath.indexOf('/loading') === -1) sessionStorage.setItem('redirectLoading', replacePath);

  Tool.goPage.replace('/pages/loading/loading');
};
export const Tool = {
  goPage: {
    push(params) {
      // 保留当前页面，跳转到应用内的某个页面
      const obj = formatPath(params);
      uni.navigateTo(obj);
    },
    tab(params) {
      // 跳转到 tabBar 页面
      const obj = formatPath(`/pages/index/index?curTab=${params}`);
      uni.navigateTo(obj);
    },
    back(params = 1) {
      // 关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPages() 获取当前的页面栈，决定需要返回几层。
      const obj = {
        delta: 1,
      };
      getCurrentPages()?.length > 1 ? uni.navigateBack(obj) : history.back();
    },
    replace(params) {
      // 关闭当前页面，跳转到应用内的某个页面
      const obj = formatPath(params);
      uni.redirectTo(obj);
    },
    reLaunch(params) {
      // 关闭所有页面，打开到应用内的某个页面。
      const obj = formatPath(params);
      uni.reLaunch(obj);
    },
  },
  getRoute: {
    params() {
      console.log('getRoute.params');

      const url = window.location.href;
      const [_, paramsStr] = url.split('?');
      console.log('paramsStr', paramsStr, _);

      if (paramsStr) {
        const urlParams = {};
        paramsStr.split('&').forEach((item) => {
          const [key, value] = item.split('=');
          urlParams[key] = decodeURIComponent(value);
        });
        console.log('urlParams', urlParams);

        return urlParams;
      } else {
        return {};
      }
    },
    path() {
      const url = window.location.href;
      const [path, _] = url.split('#')[1].split('?');
      return path;
    },
  },
  globalData: {
    get: (key) => (key ? getApp().globalData[key] : getApp().globalData),
    set: (key, value) => {
      getApp().globalData[key] = value;
    },
  },
  /**
   * @returns 返回用户信息 {userInfo, realNameInfo}
   * @param isReload 是否重新发请求加载
   */
  async getUserInfo(isReload = false) {
    const { userInfo, realNameInfo } = getApp().globalData;
    if (!isReload && Object.keys(userInfo).length > 0) {
      return { userInfo, realNameInfo };
    } else {
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      const { data: userInfo } = await request.get(`/user/userSocial`);
      userInfo.nickname = userInfo.nickname || userInfo.username || '游客';
      getApp().globalData.userInfo = userInfo;
      //获取实名
      const { data } = await request.get(`/orgStructure/realNameInfo/${userInfo.userId}`);
      getApp().globalData.realNameInfo = data;
      if (data.cardNumber != '' && data.idNumber != '') {
        uni.setStorageSync('autonym', data);
      }
      uni.hideLoading();
      return { userInfo, realNameInfo: data };
    }
  },
  /**
   * @returns 返回主题配置信息
   */
  async getThemeConfig() {
    const { themeConfig } = getApp().globalData;
    if (Object.keys(themeConfig).length > 0) {
      return themeConfig;
    } else {
      goLoadingPage();
      throw new Error('主题配置信息为空，自动跳转 loading 页');
    }

    // let { themeConfig } = getApp().globalData as any
    // if (Object.keys(themeConfig).length == 0) {
    // 	const time = localStorage.getItem("themeConfigTime")
    // 	// 本地缓存没超过 1 天，使用缓存
    // 	if (time && new Date().getTime() - Number(time) < 86400000) {
    // 		themeConfig = JSON.parse(localStorage.getItem("themeConfig") || "{}") // 本地缓存
    // 	}
    // }
    // if (Object.keys(themeConfig).length > 0) {
    // 	return themeConfig
    // } else {
    // 	goLoadingPage()
    // 	throw new Error("主题配置信息为空，自动跳转 loading 页")
    // }
  },
  /**
   * 获取店铺装修配置
   */
  async getStoreConfig() {
    const { storeConfig } = getApp().globalData;
    if (Object.keys(storeConfig).length > 0) {
      return storeConfig;
    } else {
      goLoadingPage();
      throw new Error('店铺配置信息为空，自动跳转 loading 页');
    }
  },
  /**
   * 判断是否为空
   * @param value 任意类型
   * @returns {Boolean}
   */
  isEmpty(value) {
    // 判断 null 和 undefined
    if (value == null) return true;

    // 判断数组
    if (Array.isArray(value)) return value.length === 0;

    // 判断字符串，去除空格
    if (typeof value === 'string') return value.trim().length === 0;

    // 判断对象
    if (typeof value === 'object') return Object.keys(value).length === 0;

    // 判断数字
    if (typeof value === 'number') return false; // 数字不考虑为空，除非特殊需求可以加入 isNaN 判断

    // 判断布尔值
    if (typeof value === 'boolean') return !value;

    // 其他类型数据默认不为空
    return false;
  },
  /**
   * 防抖
   */
  debounce: (func: Function, delay = 500) => {
    let timeout: NodeJS.Timeout;
    return (...args: any) => {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), delay);
    };
  },
  /**
   * 节流
   */
  throttle: (fn: Function, delay = 500) => {
    let timer: any = null;
    return function (...args) {
      if (!timer) {
        fn.apply(this, args);
        timer = setTimeout(() => {
          timer = null;
        }, delay);
      }
    };
  },
  /**
   * 获取 cookie
   */
  getCookie(name: string) {
    const cookieString = document.cookie;
    const cookieArray = cookieString.split(';');
    for (let i = 0; i < cookieArray.length; i++) {
      const cookiePair = cookieArray[i].trim().split('=');
      if (cookiePair[0] === name) {
        return cookiePair[1];
      }
    }
    return null;
  },
  /**
   * 移除cookie
   */
  removeCookie(name: string) {
    if (Tool.getCookie(name)) {
      document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/';
    }
  },
  /**
   * 清除cookie
   */
  clearAllCookies() {
    const cookies = document.cookie.split('; ');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf('=');
      const cookieName = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
      document.cookie = cookieName + '=; expires=' + new Date(0).toUTCString();
    }
  },
  /**
   * rpx 转 px
   */
  toPx(v: number) {
    return (document.body.offsetWidth / 750) * v;
  },
  /**
   * ai 语音播报
   */
  getAiVoice(data: any = {}) {
    const allAiVoice = JSON.parse(localStorage.getItem('aiVoiceType') || '{}'); // 本地缓存 (全部)
    const pointIntro = JSON.parse(data.pointIntro?.trim() || '[]');
    const aiAudioUrlList = JSON.parse(data.aiAudioUrl?.trim() || '[]');
    const aiAudioDurationList = JSON.parse(data.aiAudioDuration?.trim() || '[]');

    const localVoice = allAiVoice[data.id];
    const curVoice = {
      aiVoiceType: '', // 声音类型
      wordType: '', // 文案类型
      pointIntro: '', // 讲解内容
      aiAudioUrl: '', // 语音地址
      aiAudioDuration: 0, // 语音时长
    };

    if (localVoice && localVoice.wordType && data.wordType.split(',').includes(localVoice.wordType)) {
      // 【文案类型】有缓存并且在返回的接口里
      curVoice.wordType = localVoice.wordType;
    } else {
      // 无缓存，读取第一个
      curVoice.wordType = data.wordType?.split(',')[0];
    }

    if (localVoice && localVoice.aiVoiceType && data.aiVoiceType?.split(',').includes(localVoice.aiVoiceType)) {
      // 【声音类型】有缓存并且在返回的接口里
      curVoice.aiVoiceType = localVoice.aiVoiceType;
    } else {
      // 无缓存，读取第一个
      curVoice.aiVoiceType = data.aiVoiceType.split(',')[0];
    }
    console.log(pointIntro, curVoice.wordType);

    // 文案类型是英文版，特殊处理
    if (curVoice.wordType === '2' && !['english_man', 'english_woman'].includes(curVoice.aiVoiceType)) {
      curVoice.aiVoiceType = 'english_woman';
    }

    curVoice.pointIntro = pointIntro.find((item) => item.title === curVoice.wordType)?.content || '';
    curVoice.aiAudioUrl = aiAudioUrlList[`${curVoice.aiVoiceType}_${curVoice.wordType}`] || '';
    curVoice.aiAudioDuration = aiAudioDurationList[`${curVoice.aiVoiceType}_${curVoice.wordType}`] || 0;

    return curVoice;
  },
  // 十六进制颜色转换为 rgba
  hexToRGBA: (hex = '#6d8dfe', alpha = 1) => {
    let r = parseInt(hex.slice(1, 3), 16),
      g = parseInt(hex.slice(3, 5), 16),
      b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },
  /**
   * 隐藏长字符串中间部分，保留头尾
   * @param str 需要处理的字符串
   * @param frontLen 保留前面的字符数，默认 10
   * @param endLen 保留后面的字符数，默认 10
   * @returns 处理后的字符串
   */
  hideMiddleChars(str: string, frontLen: number = 10, endLen: number = 10): string {
    if (!str) return '';
    if (str.length <= frontLen + endLen) return str;
    return str.slice(0, frontLen) + '...' + str.slice(-endLen);
  },
  /**
   * 获取系统来源参数
   */
  getSystemSource() {
    // 获取系统来源
    const systemSource = sessionStorage.getItem('systemSource');
    // 设置 sourceType，如果来源是 travelai 则设为 8，否则为默认值 0
    const sourceType = systemSource === 'travelai' ? 8 : 0;
    return sourceType;
  },
  /**
   * 图片上传相关工具函数
   */
  imageUpload: {
    /**
     * 选择图片并上传
     * @param options 配置选项
     * @returns Promise 返回上传后的图片路径
     */
    chooseAndUpload(options: {
      count?: number;
      sizeLimit?: number;
      uploadHost?: string;
      success?: (path: string) => void;
      fail?: (err: any) => void;
    }): Promise<string> {
      const {
        count = 1,
        sizeLimit = 0.5,
        uploadHost = import.meta.env.PROD ? VITE_UPLOAD_HOST : VITE_UPLOAD_HOST__DEV,
        success,
        fail,
      } = options;

      return new Promise((resolve, reject) => {
        uni.chooseImage({
          count,
          sizeType: ['original', 'compressed'],
          success: function (res) {
            const imgObj = res.tempFiles[0];
            const imgSize = imgObj.size / 1024 / 1024;
            const ocale = sizeLimit / imgSize;
            const tempFilePaths = res.tempFilePaths[0];

            uni.showLoading({ mask: true, title: '图片压缩' });
            Tool.imageUpload
              .compressImage(tempFilePaths, { quality: ocale })
              .then((res) => {
                uni.showLoading({ mask: true, title: '图片上传' });
                uni.uploadFile({
                  url: uploadHost,
                  filePath: res,
                  name: 'file',
                  success: async (uploadFileRes) => {
                    const imgObj = JSON.parse(uploadFileRes.data)[0];
                    uni.hideLoading();
                    if (success) success(imgObj.path);
                    resolve(imgObj.path);
                  },
                  fail: (err) => {
                    console.log('上传图片失败', err);
                    uni.hideLoading();
                    if (fail) fail(err);
                    reject(err);
                  },
                });
              })
              .catch((err) => {
                uni.hideLoading();
                if (fail) fail(err);
                reject(err);
              });
          },
          fail: function (err) {
            console.log('选择图片失败', err);
            if (fail) fail(err);
            reject(err);
          },
        });
      });
    },

    /**
     * 计算图片尺寸
     * @param res 图片信息
     * @param pixels 最大像素数
     * @returns 计算后的宽高
     */
    calcImageSize(res: { width: number; height: number }, pixels: number): { imgW: number; imgH: number } {
      let imgW = res.width;
      let imgH = res.height;

      let ratio;
      if ((ratio = (imgW * imgH) / pixels) > 1) {
        ratio = Math.sqrt(ratio);
        imgW = parseInt(String(imgW / ratio));
        imgH = parseInt(String(imgH / ratio));
      } else {
        ratio = 1;
      }

      return { imgW, imgH };
    },

    /**
     * 压缩图片
     * @param imgUrl 图片 URL
     * @param options 压缩选项
     * @returns Promise 返回压缩后的图片
     */
    compressImage(
      imgUrl: string,
      options: {
        pixels?: number;
        quality?: number;
        type?: string;
        canvasId?: string;
        base64?: boolean;
        width?: number;
        height?: number;
      } = {},
    ): Promise<string> {
      const MAX_PIXELS = 2000000; // 最大分辨率，宽 * 高 的值
      const MAX_QUALITY = 0.8; // 压缩质量
      const IMG_TYPE = 'jpg';
      const CANVAS_ID = 'compress_canvas';
      const BASE_64 = false;

      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: imgUrl,
          success: (res) => {
            let pixels = options.pixels || MAX_PIXELS;
            let quality = options.quality || MAX_QUALITY;
            let type = options.type || IMG_TYPE;
            let canvasId = options.canvasId || CANVAS_ID;
            let isBase64 = options.base64 || BASE_64;

            let { imgW, imgH } = Tool.imageUpload.calcImageSize(res, pixels);
            let w = options.width || imgW;
            let h = options.height || imgH;

            // #ifdef H5
            type = type == 'jpg' ? 'jpeg' : type;

            console.log(
              `%c 宽：${w} %c 高：${h} %c 分辨率：${w * h} %c 质量：${quality} %c 类型：${type}`,
              'color:#f00',
              'background-color:#f60;color:#fff',
              'color:#F00',
              'background-color:#f60;color:#fff',
              'color:#F00',
            );

            let img = new Image();
            img.src = res.path;
            img.onload = () => {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              canvas.width = w;
              canvas.height = h;

              ctx!.drawImage(img, 0, 0, w, h);

              let base64 = canvas.toDataURL(`image/${type}`, quality);
              resolve(base64);
            };
            img.onerror = (err) => {
              reject(err);
            };
            // #endif

            // #ifndef H5
            // 非 H5 环境的处理逻辑
            reject('当前环境不支持图片压缩');
            // #endif
          },
          fail: (err) => {
            reject(err);
          },
        });
      });
    },
  },
};
