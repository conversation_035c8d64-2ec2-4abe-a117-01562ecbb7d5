<style lang="scss" scoped>
	.y-confirm-model-bg{
		position: fixed;
		top: 0%;
		left: 0%;
		right: 0%;
		bottom: 0%;
		background: rgba(1,1,1,0.5);
		.y-confirm-model{
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 500rpx;
			padding: 60rpx 40rpx;
			border-radius: 14rpx;
			background-color: #fff;
			.y-confirm-tip{
				text-align:justify;
			}
			.y-confirm-btn{
				margin: 48rpx auto 0;
				background-color: #7ED3DA;
				width: 400rpx;
				height: 76rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				font-weight: 600;
				border-radius: 14rpx;
				color: #fff;
			}
			.y-confirm-close{
				position: absolute;
				bottom: -100rpx;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
</style>
<template>
	<view class="y-confirm-model-bg" v-show="modelVisit">
		<view class="y-confirm-model">
			<view class="y-confirm-tip">
				<slot></slot>
			</view>
			<view class="y-confirm-btn" @click="onConfirmModel()">
				确认
			</view>
			<uni-icons v-if="showCloseBtn"  @click="onCancelModel()" type="close" class="y-confirm-close" color="#fff" size="40"></uni-icons>
		</view>
	</view>
</template>
<script setup>
	import { ref } from 'vue'
	
	const emits = defineEmits(['confirm', 'cancel'])
	const props = defineProps({
		showCloseBtn:{
			type: Boolean,
			default: true
		},
		modelVisit: {
			type: Boolean,
			default: false
		}
	})

	//点击确认
	const onConfirmModel = (value) =>{
		emits('confirm', true)
	}
	// 点击取消
	const onCancelModel = () =>{
		emits('cancel' ,false)
	}
	
</script>
