import { ref, onMounted } from "vue";
import Recorder from "recorder-core";
import "recorder-core/src/engine/wav";
import useWebSocket from "@/hooks/useWebSocket";
import { getEnv } from "@/utils/getEnv";

/**
 *
 * @param asr 是否开启语音识别
 * @param autuStop 是否开启自动停止录音
 * @param onStart 开始录音回调
 * @param onStop 停止录音回调
 * @param onError 录音失败回调
 * @param onAsred 语音识别完成回调
 *
 * @returns
 */
export default function useRecord({
  asr = false,
  autuStop = false,
  onAsred,
  onStart,
  onStop,
  onError,
}: {
  asr?: boolean;
  autuStop?: boolean;
  onAsred?: (msg: string) => void;
  onStart?: () => void;
  onStop?: () => void;
  onError?: (err: string) => void;
}) {
  const recStatus = ref("close"); //录音状态 close 关闭 | open 开启 | error 出错 | recording 正在录音

  let rec: any = null; //录音对象
  let recBlob = null; //录音文件对象
  let isInit = false; //是否已经初始化录音
  const asrText = ref(""); //语音识别的文字
  const asrStatus = ref("close"); //语音识别的状态 ws 关闭：close  | ws 已连接：open | ws 报错：error | 语音转换文字中：asring | 语音转换文字完成：asred
  let isStart = false;
  let isLast = false;

  let realTimeSendTryTime = 0;
  let realTimeSendTryType: any;
  let realTimeSendTryEncBusy: any;
  let realTimeSendTryNumber: any;
  let transferUploadNumberMax: any;
  let realTimeSendTryChunk: any;
  var testSampleRate = 16000;
  var testBitRate = 60;
  var SendInterval = 300; //传输间隔，单位毫秒，每次传输的间隔，不是传输的时间，传输的时间是根据数据量决定的，这里是控制传输的间隔
  //重置环境，每次开始录音时必须先调用此方法，清理环境
  const RealTimeSendTryReset = function (type = "wav") {
    realTimeSendTryType = type;
    realTimeSendTryTime = 0;
    isStart = true;
    isLast = false;
  };
  // 语音转文字
  const { VITE_AI_ASR_URL } = getEnv();
  const { wsOpen, wsSend, wsStatus, wsClose } = useWebSocket({
    url: VITE_AI_ASR_URL,
    onSocketMsg: (msg) => {
      console.log(msg);
      asrText.value = msg.answer;
      if (msg.isFinal) {
        console.log("语音转文字完成");

        asrStatus.value = "asred";
        onAsred && onAsred(msg.answer);
        wsStatus.value === "open" && wsClose();
      } else {
        asrStatus.value = "asring";
      }
    },
    onOpen: () => {
      if (asrStatus.value !== "error") asrStatus.value = "open";
      // uni.showToast({
      // 	title: "语音识别已开启",
      // 	icon: "none"
      // })
    },
    onClose: () => {
      asrStatus.value = "close";
      // uni.showToast({
      // 	title: "语音识别已关闭",
      // 	icon: "none"
      // })
    },
    onError: (err) => {
      console.log("语言转文字 error");
      console.log(JSON.stringify(err));
      if (err) {
        asrStatus.value = "error";
      }
    },
  });

  //=====实时处理核心函数==========
  var RealTimeSendTry = function (buffers, bufferSampleRate, isClose) {
    var t1 = Date.now();
    if (realTimeSendTryTime == 0) {
      realTimeSendTryTime = t1;
      realTimeSendTryEncBusy = 0;
      realTimeSendTryNumber = 0;
      transferUploadNumberMax = 0;
      realTimeSendTryChunk = null;
    }
    if (!isClose && t1 - realTimeSendTryTime < SendInterval) {
      return; //控制缓冲达到指定间隔才进行传输
    }
    realTimeSendTryTime = t1;
    var number = ++realTimeSendTryNumber;

    var pcm = [],
      pcmSampleRate = 0;
    if (buffers.length > 0) {
      //借用 SampleData 函数进行数据的连续处理，采样率转换是顺带的，得到新的 pcm 数据
      var chunk = Recorder.SampleData(
        buffers,
        bufferSampleRate,
        testSampleRate,
        realTimeSendTryChunk,
        { frameType: isClose ? "" : realTimeSendTryType }
      );

      //清理已处理完的缓冲数据，释放内存以支持长时间录音，最后完成录音时不能调用 stop，因为数据已经被清掉了
      // for (
      // 	var i = realTimeSendTryChunk ? realTimeSendTryChunk.index : 0;
      // 	i < chunk.index;
      // 	i++
      // ) {
      // 	buffers[i] = null
      // }
      realTimeSendTryChunk = chunk; //此时的 chunk.data 就是原始的音频 16 位 pcm 数据（小端 LE），直接保存即为 16 位 pcm 文件、加个 wav 头即为 wav 文件、丢给 mp3 编码器转一下码即为 mp3 文件

      pcm = chunk.data;
      pcmSampleRate = chunk.sampleRate;
    }

    //没有新数据，或结束时的数据量太小，不能进行 mock 转码
    if (pcm.length == 0 || (isClose && pcm.length < 2000)) {
      TransferUpload(number, null, 0, null, isClose);
      return;
    }

    //实时编码队列阻塞处理
    if (!isClose) {
      if (realTimeSendTryEncBusy >= 2) {
        console.log("编码队列阻塞，已丢弃一帧", 1);
        return;
      }
    }
    realTimeSendTryEncBusy++;

    //通过 mock 方法实时转码成 mp3、wav；16 位 pcm 格式可以不经过此操作，直接发送 new Blob([pcm.buffer],{type:"audio/pcm"}) 要 8 位的就必须转码
    var encStartTime = Date.now();
    var recMock = Recorder({
      type: realTimeSendTryType,
      sampleRate: testSampleRate, //采样率
      bitRate: testBitRate, //比特率
    });
    recMock.mock(pcm, pcmSampleRate);
    recMock.stop(
      function (blob, duration) {
        realTimeSendTryEncBusy && realTimeSendTryEncBusy--;
        blob.encTime = Date.now() - encStartTime;

        //转码好就推入传输
        TransferUpload(number, blob, duration, recMock, isClose);
      },
      function (msg) {
        realTimeSendTryEncBusy && realTimeSendTryEncBusy--;

        //转码错误？没想到什么时候会产生错误！
        console.log("不应该出现的错误：" + msg, 1);
      }
    );
  };

  //=====数据传输函数==========
  var TransferUpload = function (
    number,
    blobOrNull,
    duration,
    blobRec,
    isClose
  ) {
    transferUploadNumberMax = Math.max(transferUploadNumberMax, number);
    if (blobOrNull) {
      var blob = blobOrNull;
      var encTime = blob.encTime;

      var reader = new FileReader();
      reader.onloadend = function () {
        var base64 = (/.+;\s*base64\s*,\s*(.+)$/i.exec(reader.result) || [])[1];

        if (wsStatus.value === "open") {
          wsSend({
            wav_base64: base64,
            isStart: true,
            isLast: false,
          });
          if (isStart) isStart = false;
        }
      };
      reader.readAsDataURL(blob);
    }

    if (isClose) {
      console.log(
        "No." +
          (number < 100 ? ("000" + number).substr(-3) : number) +
          ":已停止传输"
      );
    }
  };

  // 没说话的时候，静默时间超过最大阈值，停止录音
  const onSilenceStop = {
    silenceDuration: 0, // 静默时间
    lastProcessTime: null, // 上次录音时间
    silenceThreshold: 10, // 设置一个阈值
    maxSilenceDuration: 2000, // 设置最大静默时间
    // 监控
    onMonitor(powerLevel) {
      const now = Date.now();
      if (this.lastProcessTime === null) this.lastProcessTime = now;
      let interval = now - this.lastProcessTime;
      this.lastProcessTime = now;

      if (powerLevel < this.silenceThreshold) {
        this.silenceDuration += interval;
        if (this.silenceDuration >= this.maxSilenceDuration) {
          console.log("静默时间超过最大阈值，停止录音");
          this.silenceDuration = 0;
          this.lastProcessTime = null;
          recStop();
        }
      } else {
        this.silenceDuration = 0;
      }
    },
  };

  /**
   * 初始化录音
   * @param {Object} opt
   * @param {Boolean} opt.startRecord 是否立即开始录音
   * */
  const recInit = () => {
    if (rec) {
      rec.close();
    }
    //创建录音对象
    rec = Recorder({
      type: "wav", //录音格式
      sampleRate: 16000, //录音的采样率，越大细节越丰富越细腻
      bitRate: 16, //录音的比特率，越大音质越好
      onProcess: (
        buffers,
        powerLevel,
        bufferDuration,
        bufferSampleRate,
        newBufferIdx,
        asyncEnd
      ) => {
        //录音实时回调，大约 1 秒调用 12 次本回调
        //可实时绘制波形，实时上传（发送）数据
        // if(this.wave) this.wave.input(buffers[buffers.length-1],powerLevel,bufferSampleRate);
        if (autuStop) onSilenceStop.onMonitor(powerLevel);
        RealTimeSendTry(buffers, bufferSampleRate, false);
      },
    });

    //打开录音，获得权限
    return new Promise((resolve, reject) => {
      rec.open(
        () => {
          isInit = true;
          recStatus.value = "open";
          RealTimeSendTryReset(); // 重置环境，开始录音时必须调用一次
          console.log("录音已打开");
          resolve(true);
        },
        (msg: string, isUserNotAllow: boolean) => {
          // 用户拒绝了录音权限，或者浏览器不支持录音
          let error = {
            code: 1001,
            msg,
          };
          // 未开启rec，不需要提示
          if (["open被取消", "open被中断"]?.includes(msg)) {
            error.code = 1002;
          }
          onError && onError(error);
          console.log(
            (isUserNotAllow ? "UserNotAllow，" : "") + "无法录音：" + msg
          );
          reject(error);
        }
      );
    });
  };

  //开始录音
  const recStart = async () => {
    if (recStatus.value === "close") {
      await recInit();
    }
    rec.start();
    recStatus.value = "recording";
    // 打开语音识别
    if (asr) await wsOpen();
    console.log("已开始录音");

    onStart && onStart(); // 开始录音回调
  };

  // 停止录音
  const recStop = (isDestroy = true): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!rec) {
        console.error("未打开录音");
        reject(new Error("未打开录音"));
        return;
      }
      rec.stop(
        (blob, duration) => {
          // blob 就是我们要的录音文件对象，可以上传，或者本地播放
          recBlob = blob;
          // 简单利用 URL 生成本地文件地址，此地址只能本地使用，比如赋值给 audio.src 进行播放，赋值给 a.href 然后 a.click() 进行下载（a 需提供 download="xxx.mp3"属性）
          const localUrl = (window.URL || webkitURL).createObjectURL(blob);
          // recPlay()

          if (wsStatus.value === "open") {
            wsSend({
              wav_base64: "",
              isStart: false,
              isLast: true,
            });
          }
          // this.upload(blob);//把 blob 文件上传到服务器
          RealTimeSendTry([], 0, true); // 最后一次发送
          onStop && onStop();
          rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用 start
          rec = null;

          recStatus.value = "close";
          resolve();
        },
        (err) => {
          console.error("结束录音出错：" + err);
          rec.close(); // 关闭录音，释放录音资源，当然可以不释放，后面可以连续调用 start
          rec = null;
          recStatus.value = "close";
          if (wsStatus.value === "open") {
            wsSend({
              wav_base64: "",
              isStart: false,
              isLast: true,
            });
          }

          reject(new Error("结束录音出错：" + err));
        }
      );
    });
  };

  // 播放录音
  const recPlay = () => {
    if (!recBlob) {
      console.error("未录音");
      return;
    }
    const audio = new Audio();
    audio.src = (window.URL || webkitURL).createObjectURL(recBlob);
    audio.play();
  };

  return {
    recInit,
    recStart,
    recStop,
    recPlay,

    recStatus,
    asrText,
    asrStatus,
  };
}
