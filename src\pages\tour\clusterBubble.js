// 基于DOMOverlay实现聚合点气泡
function ClusterBubble(options) {
  TMap.DOMOverlay.call(this, options);
}
ClusterBubble.prototype = new TMap.DOMOverlay();
ClusterBubble.prototype.onInit = function (options) {
  this.content = options.content;
  this.position = options.position;
  this.moveValue = 0
  this.onTouchstart = () => this.moveValue = 0
  this.onTouchmove = () => this.moveValue++
};
// 销毁时需要删除监听器
ClusterBubble.prototype.onDestroy = function() {
  this.dom.removeEventListener('touchstart', this.onTouchstart);
  this.dom.removeEventListener('touchmove', this.onTouchmove);
  this.dom.removeEventListener('click', this.onClick);
  this.removeAllListeners();
};
ClusterBubble.prototype.onClick = function() {
  if (this.moveValue < 10) this.emit('click');
};
// 创建气泡DOM元素
ClusterBubble.prototype.createDOM = function () {
  let dom = document.createElement('div');
  let div = document.createElement('div');
  let span = document.createElement('span');
  span.innerText = this.content;
  div.classList.add('logo_box')
  div.append(span)
  dom.append(div)
  dom.classList.add('clusterBubble');
  // 监听点击事件，实现zoomOnClick
  this.onClick = this.onClick.bind(this);
  // pc端注册click事件，移动端注册touchend事件
  dom.addEventListener('touchstart', this.onTouchstart);
  dom.addEventListener('touchmove', this.onTouchmove);
  dom.addEventListener('touchend', this.onClick);
  return dom;
};
ClusterBubble.prototype.updateDOM = function () {
  if (!this.map) {
    return;
  }
  // 经纬度坐标转容器像素坐标
  let pixel = this.map.projectToContainer(this.position);
  // 气泡箭头对齐经纬度坐标点
  let left = pixel.getX() - this.dom.offsetWidth * 0.5 + 'px';
  let top = pixel.getY() - this.dom.offsetWidth * 1.1875 + 'px';
  this.dom.style.transform = `translate(${left}, ${top})`;
  this.emit('dom_updated');
};
window.ClusterBubble = ClusterBubble;