<template>
	<view
		class="ylb-button"
		:class="[type == 'default' ? 'ylb-default-btn' : '']"
		:style="{ opacity: disable ? 0.5 : 1 }">
		<slot></slot>
	</view>
</template>

<script>
export default {
	name: "ylb-button",
	props: {
		disable: {
			type: Boolean,
			default: false
		},
		type: {
			type: String,
			default: "primary"
		}
	},
	methods: {
		lll() {
			console.log("123456")
		}
	}
}
</script>

<style lang="scss" scoped>
.ylb-button {
	background-color: var(--theme-color);
	margin: 120rpx 60rpx 0;
	line-height: 96rpx;
	color: #fff;
	font-size: 36rpx;
	border-radius: 12rpx;
	text-align: center;
	font-weight: 500;
}
.ylb-default-btn {
	border: 1px solid var(--theme-color);
	background-color: #fff;
	color: var(--theme-color);
}
</style>
