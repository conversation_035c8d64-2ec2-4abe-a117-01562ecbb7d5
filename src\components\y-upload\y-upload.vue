<template>
  <view class="upload-img">
    <view class="img-list">
      <view class="img-box" v-for="(item, index) in imgList" :key="index">
        <image @tap="previewImage(index)" class="image" :src="item" mode="aspectFill"></image>
        <image @tap="delImage(index)" v-if="props.upload" class="delete-icon" src="@/static/image/delete-icon.png"
          mode="widthFix"></image>
      </view>
      <view v-show="showUploadBtn" class="add-btn" @tap="chooseImage">
        <view class="across"></view>
        <view class="vertical"></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, toRefs, reactive, watch, computed } from "vue"
import { Tool } from "@/utils/tools"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  //限制张数
  limit: {
    type: Number,
    default: 1
  },
  //是否具有上传功能
  upload: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(["update:modelValue"])

const { VITE_IMG_HOST } = getEnv()
// 处理图片列表字符串
const imgList = computed(() => {
  return props.modelValue
    ? props.modelValue.split(",").map((e) => {
      // 判断 e 是否包含 http 前缀
      return e.startsWith("http") ? e : VITE_IMG_HOST + e;
    })
    : [];
});

//显示隐藏上传按钮
const showUploadBtn = computed(() => {
  //长度
  const isLength = imgList.value.length < props.limit
  return isLength && props.upload
})

//选择图片上传
const chooseImage = () => {
  Tool.imageUpload.chooseAndUpload({
    success: (path) => {
      const newList = props.modelValue
        ? props.modelValue + "," + path
        : path
      emits("update:modelValue", newList)
    }
  })
}

//删除图片
const delImage = index => {
  let newList = JSON.parse(JSON.stringify(props.modelValue.split(",")))
  newList.splice(index, 1)
  newList = newList.join(",")
  emits("update:modelValue", newList)
}

//预览图片
const previewImage = (index) => {
  uni.previewImage({
    current: index,
    urls: imgList.value,
  });
};
</script>

<style lang="scss" scoped>
.upload-img {
  display: flex;
  flex-wrap: wrap;
}

.img-list {
  display: flex;
  flex-wrap: wrap;

  .img-box {
    position: relative;
    margin-right: 20rpx;
    margin-bottom: 20rpx;

    .image {
      width: 145rpx;
      height: 145rpx;

      border-radius: 16rpx;
    }

    .delete-icon {
      $w: 38rpx;
      position: absolute;
      top: (-$w, 2);
      right: (-$w, 3);
      width: $w;
      height: $w;
      z-index: 1;
    }
  }
}

.add-btn {
  position: relative;
  width: 145rpx;
  height: 145rpx;
  background: #f5f5f5;
  border-radius: 16rpx;

  .across,
  .vertical {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 52rpx;
    height: 5.6rpx;
    background-color: #d8d8d8;
    border-radius: 8rpx;
  }

  .across {
    transform: translate(-50%, -50%);
  }

  .vertical {
    transform: translate(-50%, -50%) rotate(90deg);
  }

  .takePic {
    text-align: center;
    font-size: 24rpx;

    img {
      padding-top: 24rpx;
      width: 48rpx;
      height: 48rpx;
    }
  }
}

// .img{
// 	position: relative;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	width: 160rpx;
// 	height: 160rpx;
// 	background: #F3FBFC;
// 	border-radius: 16rpx;
// }
.loading,
.loading>div {
  position: relative;
  box-sizing: border-box;
}

.loading {
  display: block;
  font-size: 0;
  color: #e7e7e7;
}

.loading.la-dark {
  color: #333;
}

.loading>div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.loading {
  width: 54px;
  height: 18px;
}

.loading>div {
  width: 10px;
  height: 10px;
  margin: 4px;
  border-radius: 100%;
  animation: ball-beat 0.7s -0.15s infinite linear;
}

.loading>div:nth-child(2n-1) {
  animation-delay: -0.5s;
}

@keyframes ball-beat {
  50% {
    opacity: 0.2;
    transform: scale(0.75);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
