<style lang="scss" scoped>
/* Basic Speech Bubble */
/* 尾巴位于右下角的对话框气泡 */
</style>
<template>
	<div class="dialogue__skill__title">我具备以下技能喔！</div>
	<scroll-view scroll-x="true" class="scroll-X" :show-scrollbar="false">
		<div style="display: flex">
			<div v-for="(item, index) in skillList" :key="index">
				<div
					v-for="(e, i) in item"
					:key="i"
					class="dialogue__skill__item"
					:style="{
						background: `url(${e.bg}) no-repeat`,
						backgroundSize: 'contain'
					}">
					<div class="dialogue__skill__item-title">
						{{ e.title }}
						<image
							class="dialogue__skill__item-icon"
							:src="e.icon"
							mode="scaleToFill" />
					</div>
					<div class="dialogue__skill__item-content">
						{{ e.content }}
					</div>
				</div>
			</div>
		</div>
	</scroll-view>
</template>

<script setup>
import { ref, onMounted } from "vue"

const props = defineProps({
	isMe: {
		type: Boolean,
		default: false
	},
	content: {
		type: String,
		default: ""
	},
	// 是否可编辑
	editable: {
		type: <PERSON>ole<PERSON>,
		default: false
	}
})
</script>
