<!--
 * @Author: xuanshuncong
 * @Date: 2022-10-18 10:34:08
 * @LastEditors: xuanshuncong
 * @LastEditTime: 2022-10-27 10:14:05
 * @FilePath: \mlogin\pages\personalCenter\component\l-bind-phone.vue
-->
<template>
	<view class="phone-bg">
		<view class="new-phone-box">
			<view class="new-phone-title">
				{{ isVerify ? "校验旧手机号" : "请输入您需要绑定的新手机号" }}
			</view>
			<view class="new-phone-input">
				<input
					type="number"
					:value="isVerify ? params.oldPhone : params.phone"
					@input="
						e =>
							isVerify
								? (params.oldPhone = e.detail.value)
								: (params.phone = e.detail.value)
					"
					:placeholder-style="placeholderColor"
					placeholder="输入手机号" />
			</view>
			<view class="new-phone-input">
				<input
					type="number"
					:value="isVerify ? params.oldPhoneCode : params.phoneCode"
					@input="
						e =>
							isVerify
								? (params.oldPhoneCode = e.detail.value)
								: (params.phoneCode = e.detail.value)
					"
					:placeholder-style="placeholderColor"
					placeholder="验证码" />
				<y-verification-code
					:mobile="isVerify ? params.oldPhone : params.phone" />
			</view>
		</view>
		<y-button :disable="disableBtn" @tap="submit">确定</y-button>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import request from "@/utils/request.js"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"

const placeholderColor = computed(() => {
	return { color: "#9397A8" }
})

const isVerify = ref(true)
const token = ref("")
const params = reactive({
	phone: "",
	phoneCode: "",
	oldPhone: "",
	oldPhoneCode: ""
})
let userData = {}
onMounted(async () => {
	userData = await Tool.getUserInfo()
	params.oldPhone = userData.userInfo.phone
})

const disableBtn = computed(() => {
	if (isVerify.value) {
		return !params.oldPhone || !params.oldPhoneCode
	} else {
		return !params.phone || !params.phoneCode
	}
})

const submit = async () => {
	if (disableBtn.value) return
	uni.showLoading({
		title: isVerify.value ? "校验中" : "修改中"
	})

	const { oldPhone, oldPhoneCode, phone, phoneCode } = params
	if (isVerify.value) {
		// 校验旧手机号
		const { code, message, data } = await request.casPost(
			"/bind/phone/check",
			{
				phone: oldPhone,
				phoneCode: oldPhoneCode
			},
			{
				intercept: false
			}
		)
		if (code === 20000) {
			token.value = data.token
			isVerify.value = false
			uni.showToast({
				icon: "success",
				title: "校验成功"
			})
		} else {
			uni.showToast({
				title: message,
				icon: "none"
			})
		}
	} else {
		const { code, message } = await request.casPut(
			"/bind/phone",
			{
				phone,
				phoneCode,
				token: token.value
			},
			{
				intercept: false
			}
		)
		if (code === 20000) {
			const url = `/pages/result/result?title=${encodeURIComponent(
				"修改手机号"
			)}&redirect=${encodeURIComponent("/pages/personalCenter/personalCenter")}`
			uni.hideLoading()
			uni.redirectTo({
				url
			})
		} else {
			uni.showToast({
				title: message,
				icon: "none"
			})
		}
	}
}
</script>
<style lang="scss" scoped>
.phone-bg {
	background-color: #f1f1f1;
	height: 100%;
}
.new-phone-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	.new-phone-title {
		margin: 125rpx 0 40rpx;
		font-size: 34rpx;
		font-weight: 400;
		color: #14131f;
	}
	.new-phone-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 36rpx;
		margin-bottom: 30rpx;
		width: 630rpx;
		height: 96rpx;
		background: #fff;
	}
}
</style>
