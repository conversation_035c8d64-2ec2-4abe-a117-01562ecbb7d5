<template>
	<view v-if="!loading" class="main">
		<!-- 分类 -->
		<view class="nav">
			<view v-for="{ label, key } in navData" :key="key"
				:class="{ 'nav-item': true, 'nav-item-active': activeKey === key }" @tap="onNavClick(key)">
				<view class="text"> {{ label }}</view>
			</view>
		</view>

		<view class="content">
			<!-- 轮播 -->
			<swiper v-if="swiperList.length" class="swiper" :interval="3000" :duration="500" circular autoplay indicator-dots>
				<block v-for="item in swiperList" :key="item.articleName">
					<swiper-item class="swiper-item">
						<image class="img" :src="host + (item.publicizeImgUrl || '').split(',')[0] || '-'" mode="aspectFill"
							@click="handleClick(item)" />
						<view class="extra">
							<view class="title">{{ item.articleName }}</view>
						</view>
					</swiper-item>
				</block>
			</swiper>

			<!-- 列表 -->
			<view class="list">
				<view v-for="item in articleList" class="list-item" :key="item.articleName" @click="handleClick(item)">
					<view class="article">
						<view class="title">{{ item.articleName }}</view>
						<view class="sub">
							<view class="time">{{
								dayjs(item.publishTime).format("YYYY-MM-DD")
							}}</view>
							<image class="eye" src="@/static/image/message/eye.svg" mode="scaleToFill" />
							<view class="count">{{
								item && item.readCount >= 100
									? (item.readCount / 1000).toFixed(1) + "k"
									: item.readCount
							}}</view>
						</view>
						<view style="display: flex; align-items: center">
							<image class="user" src="@/static/image/message/user.svg" mode="scaleToFill" />
							<view class="author">{{ item.articleAuthor }}</view>
						</view>
					</view>
					<image class="img" :src="host + (item.publicizeImgUrl || '').split(',')[0] || '-'" @click=""
						mode="aspectFill" />
				</view>
			</view>
			<view v-if="articleList.length === 0 && swiperList.length === 0">暂无数据</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import request from "@/utils/request.js"
import { getRoute } from "@/utils/tool.js"
import dayjs from "dayjs"
import { ref, watchEffect } from "vue"
import { getEnv } from "@/utils/getEnv";

interface ArticleListItem {
	id: string
	articleName: string
	articleAuthor: string
	publishTime: string
	readCount: number
	publicizeImgUrl: string
	createTime: string
	articleType: number
	externalUrl: string
	articleSource: number
}

const host = getEnv().VITE_IMG_HOST

const navData = [
	{
		label: "全部",
		key: "2,3"
	},
	{
		label: "资讯",
		key: "2"
	},
	{
		label: "活动",
		key: "3"
	}
]

// 轮播图文章列表 推荐排序 sortType = 1
const swiperList = ref<ArticleListItem[]>([])
// 全部文章列表 按排序值排序 sortType = 2
const articleList = ref<ArticleListItem[]>([])

const activeKey = ref("2,3")
const loading = ref(true)

const onNavClick = (key: string) => {
	activeKey.value = key
}

const fetchArticleData = async (key: string, sortType = 1) => {
	try {
		const params = {
			articleType: key,
			quoteType: 2,
			sortType,
			storeId: getRoute.params().storeId
		}
		const { data = [] } = await request.get(`/article/h5/list`, params)
		loading.value = false
		if (sortType === 1) {
			swiperList.value = data
		} else {
			articleList.value = data
		}
	} catch (error) {
		console.log(error)
	}
}

const handleClick = (item: ArticleListItem) => {
	if (item.articleSource == 2 && item.externalUrl) {
		request.get(`/article/h5/info/external`, { id: item.id })
		window.location.href = item.externalUrl.includes("https")
			? item.externalUrl.trim()
			: `https://${item.externalUrl.trim()}`
	} else {
		Tool.goPage.push(`/pages/articleDetail/articleDetail?id=${item.id}`)
	}
}

watchEffect(() => {
	fetchArticleData(activeKey.value, 1)
	fetchArticleData(activeKey.value, 2)
})
</script>
<style lang="scss" scoped>
.nav {
	display: flex;
	padding: 20rpx 30rpx;
	border-bottom: 2rpx solid rgba(191, 198, 209, 0.25);

	.nav-item {
		height: 52rpx;
		border-radius: 26rpx;
		text-align: center;
		padding: 0 30rpx;
		transition: 0.3s;

		.text {
			height: 52rpx;
			font-size: 30rpx;
			font-weight: 400;
			color: #14131f;
			line-height: 52rpx;
		}
	}

	.nav-item-active {
		background: var(--theme-color);

		.text {
			color: #ffffff;
		}
	}
}

.content {
	padding: 20rpx 30rpx;

	.swiper {
		height: 388rpx;
		background-color: orange;
		overflow: hidden;
		border-radius: 12rpx;
		position: relative;

		.swiper-item {
			display: block;
			text-align: center;
			background-color: orange;
		}

		.img {
			width: 100%;
			height: 100%;
		}

		.extra {
			position: absolute;
			left: 20rpx;
			bottom: 16rpx;

			.title {
				font-size: 30rpx;
				max-width: 390rpx;
				overflow: hidden;
				text-wrap: nowrap;
				text-overflow: ellipsis;
				font-weight: 500;
				color: #ffffff;
				line-height: 36rpx;
				margin-bottom: 4rpx;
				text-align: start;
			}

			.time {
				height: 33rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #ffffff;
				line-height: 33rpx;
				text-align: start;
			}
		}

		:deep(.uni-swiper-dots-horizontal) {
			right: 0 !important;
			left: initial;
		}

		:global(.uni-swiper-dot) {
			width: 8rpx;
			height: 8rpx;
			margin-right: 6rpx !important;
			background-color: #d8d8d8;
		}

		:global(.uni-swiper-dot-active) {
			transition: 0.3s;
			width: 16rpx;
			height: 8rpx;
			border-radius: 4rpx;
			background-color: #ffffff;
		}
	}

	.list {
		margin-top: 30rpx;
		height: fit-content;

		.list-item {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0;
			border-bottom: 2rpx solid rgba(191, 198, 209, 0.25);
			width: 100%;

			.article {
				width: 396rpx;

				.title {
					font-size: 30rpx;
					font-weight: 500;
					color: #090909;
					line-height: 42rpx;
					width: 396rpx;
					max-height: 84rpx;
					text-overflow: ellipsis;
					overflow: hidden;
				}

				.sub {
					display: flex;
					margin: 8rpx 0 20rpx 0;
					color: rgba(20, 19, 31, 0.5);
					height: 32rpx;
					font-size: 24rpx;
					font-weight: 400;
					line-height: 32rpx;

					.eye {
						width: 32rpx;
						height: 32rpx;
						margin-left: 45rpx;
						margin-right: 2rpx;
					}
				}

				.author {
					height: 32rpx;
					font-size: 23rpx;
					font-weight: 400;
					color: #14131f;
					line-height: 32rpx;
				}
			}

			.user {
				height: 36rpx;
				width: 36rpx;
				margin-right: 8rpx;
			}

			.img {
				border-radius: 8rpx;
				margin: 12rpx 0;
				width: 272rpx;
				height: 153rpx;
			}
		}
	}
}
</style>
