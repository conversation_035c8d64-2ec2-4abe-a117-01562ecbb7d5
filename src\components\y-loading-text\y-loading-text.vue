<template>
	<div :style="customStyle">{{ text }} {{ loadingDots }}</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue"

const props = defineProps({
	text: {
		type: String,
		required: true
	},
	customStyle: {
		type: Object,
		default: () => ({})
	}
})

const loadingDots = ref(".")

const updateDots = () => {
	if (loadingDots.value.length < 3) {
		loadingDots.value += "."
	} else {
		loadingDots.value = "."
	}
}

let intervalId = null

onMounted(() => {
	intervalId = setInterval(updateDots, 500)
})

onUnmounted(() => {
	if (intervalId) {
		clearInterval(intervalId)
	}
})
</script>
