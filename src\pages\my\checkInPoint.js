import markerIcon from '@/static/image/check-in/marker-bg.webp';

// 基于 DOMOverlay 实现打卡点位
function CheckInPoint(options) {
  TMap.DOMOverlay.call(this, options);
}
CheckInPoint.prototype = new TMap.DOMOverlay();
// 初始化
CheckInPoint.prototype.onInit = function (options) {
  console.log('options000');
  console.log(options);
  this.position = options.position;
  this.details = options.details || {};
  this.id = options.id;
};
// 销毁时需解绑事件监听
CheckInPoint.prototype.onDestroy = function () {
  this.dom.removeEventListener('touchstart', this.onTouchstart);
  this.dom.removeEventListener('touchmove', this.onTouchmove);
  this.dom.removeEventListener('touchend', this.onClick);
  this.removeAllListeners();
};
// 创建 DOM 元素，返回一个 DOMElement，使用 this.dom 可以获取到这个元素
CheckInPoint.prototype.createDOM = function () {
  let dom = document.createElement('div');
  let wrapper = document.createElement('div');
  let div = document.createElement('div');
  let img = document.createElement('img');
  let span = document.createElement('span');

  // 设置图标
  img.src = markerIcon; // 使用现有的标记图标
  console.log('details=======');
  console.log(this.details);
  console.log(this.details);
  span.innerText = this.details.name || '打卡点';
  span.classList.add('check-in-point-name');

  div.classList.add('logo_box');
  div.appendChild(img);

  wrapper.classList.add('check-in-wrapper');
  wrapper.appendChild(span);

  dom.appendChild(div);
  dom.appendChild(wrapper);
  dom.classList.add('cPoint');

  // 点击事件处理
  this.moveValue = 0;
  this.onTouchstart = () => (this.moveValue = 0);
  this.onTouchmove = () => this.moveValue++;
  this.onClick = () => {
    if (this.moveValue < 10) this.emit('click');
  };

  // 添加事件监听
  dom.addEventListener('touchstart', this.onTouchstart);
  dom.addEventListener('touchmove', this.onTouchmove);
  dom.addEventListener('touchend', this.onClick);

  return dom;
};
// 更新 DOM 元素，在地图移动/缩放后执行
CheckInPoint.prototype.updateDOM = function () {
  if (!this.map) {
    return;
  }
  // 经纬度坐标转容器像素坐标
  let pixel = this.map.projectToContainer(this.position);
  // 气泡箭头对齐经纬度坐标点
  let left = pixel.getX() - this.dom.offsetWidth * 0.5 + 'px';
  let top = pixel.getY() - this.dom.offsetWidth * 1.1875 + 'px';
  this.dom.style.transform = `translate(${left}, ${top})`;
};
window.CheckInPoint = CheckInPoint;
