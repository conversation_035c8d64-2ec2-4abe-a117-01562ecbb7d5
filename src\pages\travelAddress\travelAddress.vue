<template>
  <view class="travelAddress">
    <view class="search">
      <image src="@/static/image/travel/seach.svg" mode="aspectFill" />
      <input type="text" placeholder="查找地点" @input="input" />
      <view @click="goPage.back()">取消</view>
    </view>
    <view v-if="addressList.length" class="list">
      <view v-for="(item, index) in addressList" :key="item.title" class="list--item">
        <view class="list--title">{{ item.title }}</view>
        <view class="list--content">
          {{ item.category.split(":")[0] }}｜{{ item.address }}
        </view>
        <view v-if="!addList.find((v) => v.id == item.id)" class="add" @click="add(index)">添加</view>
      </view>
    </view>
    <view v-else class="nullData">
      <image class="icon" src="@/static/image/travel/404.svg" mode="aspectFit" />
      <view class="text">
        {{ seachValue ? '很抱歉，未找到匹配地点' : '请输入关键词搜索' }}
      </view>
    </view>
    <view class="button">
      <picker @change="bindPickerChange" :value="dateIndex" :range="userJourneyDto.journeyDateList?.map(
        (item) => `第${item.dateSortNumber}天`
      )
        ">
        <view class="button--left">
          <view>添加至：第{{
            userJourneyDto.journeyDateList?.[dateIndex].dateSortNumber
          }}天</view>
          <uni-icons type="right" color="#000000E6" size="40rpx" />
        </view>
      </picker>
      <view class="button--right" @click="addAddress">{{
        `添加（${addList.length}）`
      }}</view>
    </view>
  </view>
</template>
<script setup>
import { useTravelStore } from "@/stores/travel";

const { journeyId, index } = Tool.getRoute.params();
const userJourneyDto = ref({});
const dateIndex = ref(index || 0);
const addressList = ref([]);
const addList = ref([]);
const seachValue = ref();
const travel = useTravelStore();

const bindPickerChange = (e) => {
  dateIndex.value = e.detail.value;
};
const input = Tool.debounce((e) => {
  request
    .post("/navigation/line/common", {
      domain: "https://apis.map.qq.com",
      interfacePath: "/ws/place/v1/suggestion",
      parameter: "keyword=" + e.detail.value,
    })
    .then((res) => {
      seachValue.value = e.detail.value
      addressList.value = JSON.parse(res.data).data;
    });
});
const add = (index) => {
  addList.value.push(addressList.value[index]);
};
const addAddress = async () => {
  if (!addList.value.length) {
    return uni.showToast({
      icon: "none",
      title: "请先添加地点",
    });
  }
  if (!userJourneyDto.value.journeyDateList[dateIndex.value].journeyPlaceList) {
    userJourneyDto.value.journeyDateList[dateIndex.value].journeyPlaceList = [];
  }
  const arr =
    userJourneyDto.value.journeyDateList[dateIndex.value].journeyPlaceList;
  const arrLength = arr.length;
  arr.push(
    ...(await Promise.all(
      addList.value.map(async (item, index) => {
        const obj = {
          placeSortNumber: arrLength + index + 1,
          externalId: item.id,
          journeyCityName: item.city,
          journeyPlaceName: item.title,
          latitude: item.location.lat,
          longitude: item.location.lng,
          placeTypeDescription: item.category.split(":")[0],
        };
        if (index || arr.length) {
          try {
            const { longitude: a, latitude: b } = index
              ? {
                longitude: addList.value[index - 1].location.lng,
                latitude: addList.value[index - 1].location.lat,
              }
              : arr.at(-1);
            const { longitude: c, latitude: d } = {
              longitude: addList.value[index].location.lng,
              latitude: addList.value[index].location.lat,
            };
            const O = {};
            const D = TMap.geometry.computeDistance([
              new TMap.LatLng(b, a),
              new TMap.LatLng(d, c),
            ]);
            if (D < 2000) {
              O.type = "walking";
              O.label = "步行";
            } else {
              O.type = "driving";
              O.label = "驾车";
            }
            await request
              .post("/navigation/line/common", {
                domain: "https://apis.map.qq.com",
                interfacePath: "/ws/direction/v1/" + O.type,
                parameter: `from=${b},${a}&to=${d},${c}`,
              })
              .then((res) => {
                const { distance, duration, polyline } = JSON.parse(res.data)
                  .result.routes[0];
                obj.distance = JSON.stringify({
                  O,
                  distance,
                  duration,
                  polyline,
                });
              });
          } catch (error) {
            // console.log(error);
          }
        }
        return obj;
      })
    ))
  );
  travel.userJourneyDto = userJourneyDto.value;
  travel.update = true;
  goPage.back();
  // request
  //   .post("/user/journey/update", userJourneyDto.value)
  //   .then(({ data }) => {
  //     travel.userJourneyDto = data;
  //     uni.showToast({
  //       icon: "none",
  //       title: "添加成功",
  //     });
  //     goPage.back();
  //   });
};

onBeforeMount(() => {
  if (journeyId) {
    userJourneyDto.value = JSON.parse(JSON.stringify(travel.userJourneyDto));
  }
});
</script>
<style lang="scss" scoped>
.travelAddress {
  width: 100vw;
  height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;

  .search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
    padding: 14rpx 30rpx;
    position: relative;

    >image {
      position: absolute;
      top: 50%;
      left: 50rpx;
      transform: translateY(-50%);
      width: 50rpx;
      height: 50rpx;
    }

    >input {
      width: 600rpx;
      height: 60rpx;
      background: #f0f0f0;
      border-radius: 30rpx;
      padding: 0 7rpx 0 77rpx;
    }

    >view {
      font-size: 28rpx;
      color: #000;
    }
  }

  .list {
    flex: 1;
    padding: 0 30rpx;
    overflow: auto;

    .list--item {
      padding: 30rpx 0;
      position: relative;

      .list--title,
      .list--content {
        width: 544rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .list--title {
        font-weight: bold;
        font-size: 34rpx;
        color: #14131f;
        line-height: 34rpx;
      }

      .list--content {
        margin-top: 20rpx;
        font-size: 26rpx;
        color: rgba(20, 19, 31, 0.8);
        letter-spacing: 1px;
      }

      .add {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #349fff;
      }
    }

    .list--item:not(:last-child) {
      border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
    }
  }

  .nullData {
    flex: 1;
    overflow: auto;

    .icon {
      margin: 256rpx 72rpx 0;
      width: 606rpx;
      height: 606rpx;
    }

    .text {
      text-align: center;
    }
  }

  .button {
    width: 100%;
    height: 140rpx;
    display: flex;
    justify-content: space-between;
    padding: 26rpx 30rpx;
    border-top: 1rpx solid rgba(151, 151, 151, 0.23);

    .button--left {
      width: 422rpx;
      height: 88rpx;
      background: #f4f4f4;
      border-radius: 12rpx;
      padding: 0 32rpx 0 53rpx;
      font-size: 36rpx;
      color: #14131f;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .button--right {
      width: 238rpx;
      height: 88rpx;
      background: #349fff;
      border-radius: 12rpx;
      text-align: center;
      line-height: 88rpx;
      font-weight: bold;
      font-size: 36rpx;
      color: #fff;
    }
  }
}
</style>
