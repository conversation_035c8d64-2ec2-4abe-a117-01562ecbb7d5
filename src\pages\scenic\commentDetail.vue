<template>
	<view class="comment-detail">
		<!-- 评论详情内容 -->
		<view class="comment-container" v-if="commentDetail">
			<!-- 用户信息 -->
			<view class="user-info-row flex-row">
				<image class="user-avatar" :src="commentDetail.userAvatar" mode="aspectFill"></image>
				<view class="user-detail flex-col">
					<text class="user-name">{{ commentDetail.userName }}</text>
					<text class="comment-date">{{ commentDetail.createTime }}</text>
				</view>
				<view class="order-divider"></view>
				<text class="order-source">{{ commentDetail.type === 1 ? '来自订单' : '来自游客分享' }}</text>
			</view>

			<!-- 评分信息 -->
			<view class="score-row flex-row">
				<view class="score-badge flex-col">
					<text class="score-value">{{ commentDetail.score }}</text>
				</view>
				<text class="score-level">{{ getScoreLevel(commentDetail.score) }}</text>
				<text class="score-detail">景色 {{ commentDetail.environmentScore }}</text>
				<text class="score-detail">趣味 {{ commentDetail.funScore }}</text>
				<text class="score-detail">性价比 {{ commentDetail.valueScore }}</text>
			</view>

			<!-- 标签 -->
			<view class="review-tags flex-row" v-if="commentDetail.tags && commentDetail.tags.length > 0">
				<view class="review-tag" v-for="(tag, tagIndex) in commentDetail.tags" :key="tagIndex">
					<text class="review-tag-text">{{ tag }}</text>
				</view>
			</view>

			<!-- 评论内容 -->
			<view class="comment-content flex-row">
				<text class="content-text content-text-expanded">{{ commentDetail.content }}</text>
			</view>

			<!-- 图片 -->
			<view class="comment-images-grid" v-if="commentDetail.images && commentDetail.images.length > 0">
				<view class="image-container" v-for="(img, imgIndex) in commentDetail.images" :key="imgIndex"
					@click="previewImage(imgIndex)">
					<view class="side" v-if="isVideo(img)">
						<video :src="img" class="video" :controls="false" :show-play-btn="false" :autoplay="false" :muted="true"
							:enable-play-gesture="false" object-fit="cover"></video>
						<view class="inV"></view>
					</view>
					<image v-else :src="img" mode="aspectFill" class="comment-image"></image>
					<view class="more-images-count" v-if="imgIndex === 8 && commentDetail.images.length > 9">
						<text class="more-images-text">+{{ commentDetail.images.length - 9 }}</text>
					</view>
				</view>
			</view>

			<!-- 出游信息 -->
			<view class="travel-info">
				<text v-if="commentDetail.travelText" class="travel-type"><text style="color: #999999;">出游类型：</text>{{
					commentDetail.travelText }}</text>
				<text v-if="commentDetail.browseDuration" class="travel-duration"><text style="color: #999999;">游览时长：</text>{{
					timeArr[commentDetail.browseDuration] }}</text>
			</view>

			<!-- 商家回复 -->
			<view class="merchant-reply" v-if="commentDetail.merchantReply">
				<view class="reply-header flex-row justify-between">
					<text class="reply-title">商家回复 </text>
					<text class="reply-date">{{ commentDetail.merchantReplyDate || '' }}</text>
				</view>
				<view class="reply-content flex-row">
					<view class="reply-text-container flex-row">
						<text class="reply-text">{{ commentDetail.merchantReply }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载中状态 -->
		<view class="loading-state" v-else-if="isLoading">
			<y-loading-text text="加载中"></y-loading-text>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<y-empty text="未找到该点评信息"></y-empty>
		</view>

		<!-- 图片预览组件 -->
		<y-preview-image :visible="previewVisible" :images="commentDetail?.images || []" :initialIndex="currentPreviewIndex"
			:userInfo="previewUserInfo" @close="previewVisible = false"></y-preview-image>
	</view>
	<uni-popup ref="popup" :mask-click="false" mask-background-color="rgba(0,0,0,0.4)" type="center">
		<view class="commentB">
			<view class="topB"></view>
			<img src="@/static/image/face_icon/commentSuccess.png" alt="" />
			<view class="com_A">点评成功</view>
			<view class="com_B">感谢你的评价，我们会继续为你提供更优质的</view>
			<view class="com_C" @click="close">好的</view>
		</view>
		<view class="icon">
			<uni-icons type="close" size="32" color="#D8D8D8" @click="close"></uni-icons>
		</view>
	</uni-popup>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import request from '@/utils/request.js';
import { Tool } from "@/utils/tools";
import { playArr, timeArr } from '@/utils/constant.js';
import yNavBar from '@/components/y-nav-bar/y-nav-bar.vue';
import yLoadingText from '@/components/y-loading-text/y-loading-text.vue';
import yEmpty from '@/components/y-empty/y-empty.vue';
import yPreviewImage from '@/components/y-preview-image/y-preview-image.vue';
import { desensitization } from "@/utils/tool.js";
import { getEnv } from "@/utils/getEnv";

const defaultAvatar = '/static/image/default-avatar.png';

const emits = defineEmits(["open"]);
const popup = ref(null);
const open = () => {
	popup.value.open();
	emits("open", true);
};
const close = () => {
	sessionStorage.setItem("tabData", "");
	popup.value.close();
	emits("open", false);
};

// 图片服务器地址
const imgHost = getEnv().VITE_IMG_HOST;

// 默认头像


// 路由参数
let commentId = '';

// 加载状态
const isLoading = ref(true);

// 评论详情数据
const commentDetail = ref(null);

// 图片预览相关数据
const previewVisible = ref(false);
const currentPreviewIndex = ref(0);

// 用于图片预览的用户信息
const previewUserInfo = computed(() => {
	if (!commentDetail.value) return null;

	return {
		userName: commentDetail.value.userName,
		score: commentDetail.value.score,
		scoreLevel: getScoreLevel(commentDetail.value.score),
		environmentScore: commentDetail.value.environmentScore,
		funScore: commentDetail.value.funScore,
		valueScore: commentDetail.value.valueScore,
		content: commentDetail.value.content
	};
});

// 获取评论详情
const getCommentDetail = async () => {
	isLoading.value = true;

	try {
		// 获取评论详情接口
		const { data } = await request.get(`/comment/findByCommentInfo/${commentId}`);

		console.log('评论详情：', data);

		if (data) {
			// 格式化评论详情数据，添加脱敏处理
			commentDetail.value = {
				...data,
				userName: data.anonymous === 1 ? desensitization(data.nickname, 'name') : data.nickname || '游客',
				userAvatar: (data.anonymous !== 1 && data.profileUrl ? imgHost + data.profileUrl : defaultAvatar),
				score: data.score?.toFixed(1) || '5.0',
				environmentScore: data.environmentScore?.toFixed(1) || '5.0',
				funScore: data.interestScore?.toFixed(1) || '5.0', // 趣味评分对应接口的 interestScore
				valueScore: data.serviceScore?.toFixed(1) || '5.0', // 服务/性价比评分对应接口的 serviceScore
				content: data.comment || '',
				images: data.msgUrl ? data.msgUrl.map(item => imgHost + item) : [],
				tags: data.labelName || [],
				merchantReply: data.merchantCommentsList && data.merchantCommentsList.length > 0 ?
					data.merchantCommentsList[0].content : '',
				merchantReplyDate: data.merchantCommentsList && data.merchantCommentsList.length > 0 ?
					data.merchantCommentsList[0].createTime : '',
				travelText: data.travelType ? playArr[data.travelType - 1] : '',
				browseDuration: data.browseDuration
			};
		}
	} catch (err) {
		console.error('获取评论详情失败', err);
		uni.showToast({
			title: '获取评论详情失败',
			icon: 'none'
		});
	} finally {
		isLoading.value = false;
	}
};

// 根据分数获取评价级别
const getScoreLevel = (score) => {
	if (!score) return '满意';
	const numScore = parseFloat(score);
	if (numScore >= 5) return '超棒';
	if (numScore >= 4) return '满意';
	if (numScore >= 3) return '不错';
	if (numScore >= 2) return '一般';
	return '不佳';
};

// 预览图片
const previewImage = (index) => {
	if (!commentDetail.value || !commentDetail.value.images) return;

	currentPreviewIndex.value = index;
	previewVisible.value = true;
};
const isVideo = (url) => {
	const videoExtensions = ["mp4", "mov", "avi", "wmv", "flv"];
	const ext = url.split(".").pop().toLowerCase();
	return videoExtensions.includes(ext);
};
// 页面加载
onMounted(() => {
	// 获取路由参数
	const route = Tool.getRoute.params();
	console.log('route', route);

	commentId = route.commentId || '';

	if (commentId) {
		// 获取评论详情
		getCommentDetail();
	} else {
		isLoading.value = false;
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		});
	}

	if (sessionStorage.getItem("tabData")) {
		const res = JSON.parse(sessionStorage.getItem("tabData"));
		if (res.commentType == "1") {
			open();
		}
	}
});
</script>

<style lang="scss" scoped>
.comment-detail {
	min-height: 100vh;
	background-color: #F5F7FA;
	padding-bottom: 40rpx;
	overflow: hidden;

	.comment-container {
		margin: 20rpx;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;

		// 用户信息行
		.user-info-row {
			margin-right: 362rpx;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				flex: none;
			}

			.user-detail {
				margin: 0 0 1rpx 16rpx;

				.user-name {
					color: #17181A;
					font-size: 28rpx;
					font-weight: 500;
					letter-spacing: 0.245rpx;
					line-height: 40rpx;
					white-space: nowrap;
				}

				.comment-date {
					color: #666;
					font-size: 24rpx;
					letter-spacing: 0.21rpx;
					line-height: 33rpx;
					white-space: nowrap;
					margin-top: 6rpx;
				}
			}

			.order-divider {
				flex: none;
				background-color: #D8D8D8;
				width: 2rpx;
				height: 30rpx;
				border-radius: 2rpx;
				margin: 47rpx 0 0 8rpx;
			}

			.order-source {
				color: #666;
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin: 46rpx 0 0 8rpx;
			}
		}

		// 评分行
		.score-row {
			margin: 20rpx 0;

			.score-badge {
				background-color: #349FFF;
				border-radius: 10rpx 10rpx 0rpx 10rpx;
				padding: 4rpx 11rpx;

				.score-value {
					color: #FFFFFF;
					font-size: 24rpx;
					font-weight: 600;
					line-height: 24rpx;
					white-space: nowrap;
				}
			}

			.score-level {
				color: #14131F;
				font-size: 24rpx;
				font-weight: 500;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin-left: 6rpx;
			}

			.score-detail {
				color: #666;
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin-left: 21rpx;
			}
		}

		// 评论标签
		.review-tags {
			margin: 20rpx 0;
			flex-wrap: wrap;
			display: flex;
			gap: 10rpx;

			.review-tag {
				background-color: #EDF7FF;
				border-radius: 8rpx;
				padding: 8rpx 15rpx 8rpx 16rpx;
				margin-right: 20rpx;
				margin-bottom: 16rpx;
				max-width: 200rpx;
				overflow: hidden;

				.review-tag-text {
					color: #349FFF;
					font-size: 24rpx;
					letter-spacing: 0.21rpx;
					line-height: 32rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
				}
			}
		}

		// 评论内容
		.comment-content {
			margin: 20rpx 0rpx 20rpx 0;

			.content-text {
				color: #14131F;
				font-size: 30rpx;
				letter-spacing: 0.263rpx;
				line-height: 44rpx;
				width: 690rpx;
				word-break: break-all; // 添加自动换行，无论是否截断单词
				word-wrap: break-word; // 确保长单词也能换行
				white-space: normal; // 确保文本正常换行

				&.content-text-expanded {
					max-height: none;
				}
			}
		}

		// 评论图片网格
		.comment-images-grid {
			display: flex;
			flex-wrap: wrap;
			margin: 30rpx 0;
			width: 100%;

			.image-container {
				position: relative;
				width: calc((100% - 16rpx * 2) / 3);
				height: 214rpx;
				border-radius: 8rpx;
				overflow: hidden;
				margin-right: 16rpx;
				margin-bottom: 16rpx;

				&:nth-child(3n) {
					margin-right: 0;
				}

				&:nth-child(n+10) {
					display: none;
				}

				.comment-image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}

				.side {
					position: relative;
					width: 100%;
					height: 100%;

					.video {
						position: absolute;
						width: 100%;
						height: 100%;
					}

					.inV {
						opacity: 0;
						position: absolute;
						z-index: 6;
						left: 0;
						right: 0;
						top: 0;
						bottom: 0;
					}
				}

				.more-images-count {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background-color: rgba(0, 0, 0, 0.5);
					display: flex;
					justify-content: center;
					align-items: center;

					.more-images-text {
						color: #FFFFFF;
						font-size: 36rpx;
						font-weight: 500;
					}
				}
			}
		}

		// 出游信息
		.travel-info {
			display: flex;
			align-items: center;
			margin: 16rpx 0 30rpx;
			border-radius: 8rpx;

			.travel-type {
				color: #333333;
				font-size: 28rpx;
				margin-right: 40rpx;
			}

			.travel-duration {
				color: #333333;
				font-size: 28rpx;
			}
		}

		// 商家回复
		.merchant-reply {
			background-color: #F7F8FA;
			border-radius: 16rpx;
			margin: 34rpx 0;
			padding: 24rpx 22rpx 24rpx 30rpx;

			.reply-header {
				margin-right: 380rpx;

				.reply-title {
					color: #17181A;
					font-size: 24rpx;
					font-weight: 500;
					line-height: 33rpx;
					white-space: nowrap;
					margin-right: 10rpx;
				}

				.reply-date {
					color: #999;
					font-size: 24rpx;
					line-height: 33rpx;
					white-space: nowrap;
				}
			}

			.reply-content {
				margin-top: 11rpx;

				.reply-text-container {
					width: 638rpx;

					.reply-text {
						color: #44474D;
						font-size: 24rpx;
						line-height: 34rpx;
					}
				}
			}
		}

		// 商品信息
		.goods-info {
			margin-top: 40rpx;

			.goods-header {
				margin-bottom: 20rpx;

				.goods-line {
					background-color: #4787FB;
					border-radius: 3rpx;
					width: 8rpx;
					height: 36rpx;
					margin-right: 10rpx;
				}

				.goods-title {
					color: #14131F;
					font-size: 32rpx;
					font-weight: 500;
					line-height: 36rpx;
				}
			}

			.goods-content {
				background-color: #F7F8FA;
				border-radius: 16rpx;
				padding: 20rpx;

				.goods-name {
					color: #17181A;
					font-size: 28rpx;
					font-weight: 500;
					line-height: 40rpx;
				}
			}
		}
	}

	// 加载中状态
	.loading-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 300rpx;
	}

	// 空状态
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 300rpx;
	}
}

.commentB {
	width: 540rpx;
	height: 654rpx;
	border-radius: 16rpx;
	box-sizing: border-box;
	text-align: center;
	background: #fff;

	// position: relative;
	.topB {
		border-radius: 16rpx;
		// position: absolute;
		height: 60rpx;
		// left: 0;
		// right: 0;
		background: linear-gradient(180deg, #e3edfe 0%, #ffffff 100%);
	}

	img {
		width: 284rpx;
		height: 256rpx;
	}

	.com_A {
		font-size: 36rpx;
		margin: 24rpx 0 16rpx;
		font-weight: 700;
	}

	.com_B {
		color: #666666;
		width: 376rpx;
		margin: 0 auto;
	}

	.com_C {
		margin: 40rpx auto 0;
		width: 360rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 16rpx;
		border: 2rpx solid #b9b9b9;
	}
}

.icon {
	text-align: center;
	margin-top: 20rpx;
}

// 工具类
.flex-row {
	display: flex;
	flex-direction: row;
}

.flex-col {
	display: flex;
	flex-direction: column;
}

.justify-between {
	justify-content: space-between;
}
</style>
