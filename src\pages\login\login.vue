<template>
	<view v-if="!isAutoLogin" class="login" :style="{
		background: 'url(' + bgImg + ') no-repeat',
		backgroundSize: '100% 100%'
	}">
		<view>
			<view class="logo">
				<image v-if="storeInfo.iconType === 'ICON_CUSTOMIZE'" class="logo_icon" :src="storeInfo.iconUrl"
					mode="aspectFit"></image>
				<image v-else class="logo_icon" src="@/static/logo.png" mode="aspectFit"></image>
			</view>
			<view class="sub_title">
				{{ storeInfo.introduce || '基于联盟链的全场景目的地云服务开创者' }}
			</view>
			<view class="position-relative">
				<view class="main" :style="{
					border: '1px solid ' + hexToRGBA(themeColor, 0.3)
				}">
					<view v-if="loginMode == 'wxLogin'" class="title">绑定手机号</view>
					<view v-else class="login-tab" :class="loginMode">
						<view class="tab" @click="loginMode = 'mobile'">验证码登录</view>
						<view class="tab" @click="loginMode = 'password'">账号登录</view>
					</view>
					<!-- <view class="title">
						{{ loginMode === "mobile" ? "短信登录 / 注册" : "" }}
						{{ loginMode === "password" ? "密码登录" : "" }}
						{{ loginMode === "wxLogin" ? "绑定手机号" : "" }}
					</view> -->
					<view v-show="loginMode === 'mobile' || loginMode === 'wxLogin'">
						<loginInput type="mobile" v-model="loginParams.mobile" @verify="verify => (isMobile = verify)" />
						<loginInput type="verificationCode" v-model="loginParams.secret" :mobile="loginParams.mobile"
							:disable="!isMobile" />
					</view>
					<view v-show="loginMode === 'password'">
						<loginInput type="mobile" v-model="loginParams.mobile" @verify="verify => (isMobile = verify)" />
						<loginInput type="password" v-model="loginParams.secret" />
					</view>
					<view class="addition">
						<view class="left">
							<y-checkbox type="radio" :checked="expire" @onCheck="expire = !expire">
								自动登录
							</y-checkbox>
							<!-- <checkbox-group>
								<label>
									<checkbox value="cb" color="#349FFF" style="transform:scale(0.7)" />自动登录
								</label>
							</checkbox-group> -->
						</view>
						<view v-if="loginMode === 'wxLogin'" class="right" @tap="goPage('/pages/login/login')">
							手机号登录
						</view>
						<view v-if="loginMode === 'password'" class="right" @tap="goPage('/pages/forgetPwd/forgetPwd')">
							忘记密码
						</view>
					</view>
					<y-button :disable="!canSubmit" @tap="onLogin" class="login-btn">{{
						loginMode !== "wxLogin" ? "登录" : "绑定"
					}}</y-button>
				</view>
				<!-- <image
					class="submit_bg"
					src="@/static/image/loginbtn-bg-icon.png"
					mode="widthFix"></image>
				<submitBtn :disable="!canSubmit" @tap="login"></submitBtn> -->
			</view>
			<view v-if="loginMode !== 'wxLogin'" class="order-login">
				<view class="wx">
					<view class="line-left"></view>
					<image @click="wxLogin({ type: 'accredit' })" class="wx-login" src="@/static/image/wx-icon.png"
						mode="widthFix" />
					<view class="line-right"></view>
					<!-- <view class="btn-group">
					<image
						style="margin-right: 74rpx"
						@tap="loginMode = 'mobile'"
						v-show="loginMode === 'password'"
						class="btn"
						src="@/static/image/icon_phonelogin.png"
						mode="widthFix"></image>
					<image
						style="margin-right: 74rpx"
						@tap="loginMode = 'password'"
						v-show="loginMode === 'mobile'"
						class="btn"
						src="@/static/image/icon_accountlogin.png"
						mode="widthFix"></image>
					<image
						@click="wxLogin({ type: 'accredit' })"
						class="btn"
						src="@/static/image/icon_wxlogin.png"
						mode="widthFix"></image>
				</view> -->
				</view>
				<view class="tip">微信授权登录</view>
			</view>
		</view>
		<view class="internet-content-provider">
			<view>&copy;&nbsp;2001-{{ getYear }}
				统一认证中心&copy;&nbsp;环球数科提供技术支持</view>
			<view @click="goRecords">版权所有&nbsp;|&nbsp;粤 ICP 备 ******** 号</view>
		</view>
		<y-confirm-modal :modelVisit="wxBingVisit" @confirm="wxBingConfirm" @cancel="wxBingCancel">
			该微信未绑定手机号，请先绑定手机号再登录
		</y-confirm-modal>
	</view>
	<view v-else class="auto-login">
		<view class="loading-spinner">
			<view v-for="(item, index) in 3" :key="index" class="spinner-dot"
				:style="{ backgroundColor: themeColor, animationDelay: index * 0.15 + 's' }"></view>
		</view>
		<view class="auto-login-text">自动登录中...</view>
	</view>
</template>

<script>
import loginInput from "./component/loginInput.vue"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"
import request from "@/utils/request.js"
import bg1 from "@/static/image/bg_loginpage.png"
import bg2 from "@/static/image/bg_loginpage_2.png"
import bg3 from "@/static/image/bg_loginpage_3.png"
import bg4 from "@/static/image/bg_loginpage_4.png"
import { getRoute, wxAuthorize, setCookie, } from "@/utils/tool.js"
import { getEnv } from "@/utils/getEnv";

export default {
	components: {
		loginInput,
	},

	data() {
		return {
			wxBingVisit: false,
			wxMobile: false,
			loginMode: "mobile",
			loginParams: {
				mobile: "",
				secret: ""
			},
			isMobile: false,
			isAutoLogin: false,
			expire: false,
			unionId: "",
			wxCode: "",
			appInfo: {},
			redirect: "",
			bgImg: "",
			themeColor: "",
			storeInfo: {}
		}
	},
	onLoad(option) {
		const { appId, redirect } = option
		this.redirect = redirect
		uni.setStorageSync("appId", appId)
		// 获取应用信息
		this.getApplicationInfo()
		const { code } = getRoute.params()
		if (code) {
			const params = {
				type: "wxLogin",
				code: code
			}
			this.wxLogin(params)
		}
	},
	async onShow(route) {
		// 判断是否自动登录
		const ck = sessionStorage.getItem("_ck")
		if (ck) {
			this.isAutoLogin = true
			this.goCallback({ ck })
			sessionStorage.removeItem("_ck")
			return
		}

		// 判断是否登录
		if (Tool.getCookie('Authorization')) {
			Tool.goPage.replace('/')
		}
		this.storeInfo = Tool.globalData.get("storeInfo")
		console.log('storeInfo', this.storeInfo)
		document.title = this.storeInfo.name
		// resetThemeColor({
		// 	path: "pages/login/login",
		// 	appId: uni.getStorageSync("appId")
		// })
		uni.showLoading({
			mask: true,
			title: "加载中"
		})
		const config = await Tool.getThemeConfig()
		console.log();
		const { color } = config.shopStyle
		this.themeColor = color
		if (color === '#349FFF') {
			this.bgImg = bg1
		}
		if (color === '#F87054') {
			this.bgImg = bg2
		}
		if (color === '#2DC996') {
			this.bgImg = bg3
		}
		if (color === '#FE9A35') {
			this.bgImg = bg4
		}
		uni.hideLoading()

	},
	computed: {
		canSubmit() {
			let isCan = false
			switch (this.loginMode) {
				case "mobile":
				case "wxLogin":
					if (this.isMobile && this.loginParams.secret) {
						isCan = true
					}
					break
				case "password":
					if (this.isMobile && this.loginParams.secret) {
						isCan = true
					}
					break
			}
			return isCan
		},
		getYear() {
			return dayjs().format("YYYY")
		}
	},
	methods: {
		hexToRGBA(color, alpha) {
			return Tool.hexToRGBA(color, alpha)
		},
		// 获取应用信息
		async getApplicationInfo() {
			const {
				data: { appId }
			} = await request.get(
				`/ticketAgent/noAuth/casApp/${getRoute.params().storeId}`
			)
			uni.setStorageSync("appId", appId)

			const { data: appInfo } = await request.casGet(
				"/application/getApplicationInfo",
				{
					appId: uni.getStorageSync("appId")
				}
			)
			if (!appInfo.url) {
				uni.showToast({
					title: "回调地址参数缺失",
					duration: 2000
				})
			}
			this.appInfo = {
				...appInfo,
				imgUrl: getEnv().VITE_IMG_HOST + appInfo.imgUrl
			}
		},
		wxBingConfirm() {
			this.wxBingVisit = false
			this.loginMode = "wxLogin"
		},
		wxBingCancel() {
			this.wxBingVisit = false
			this.loginMode = "mobile"
		},
		// 获取微信授权回调 code
		getWxCode() {
			const urlArr = location.href.split("/?")
			const leftUrl = urlArr[0] + "/#/"
			const rightUrl = urlArr[1].split("#/")[0]
			const queryObj = {}
			rightUrl
				.split("&")
				.map(item => {
					const splitStr = item.split("=")
					return {
						key: splitStr[0],
						value: splitStr[1]
					}
				})
				.forEach(item => {
					queryObj[item.key] = item.value
				})
			return queryObj
		},
		// 微信登录
		async wxLogin({ type, code }) {
			if (type === "accredit") {
				//授权
				wxAuthorize()
			} else if (type === "wxLogin") {
				//登录
				this.loginMode = "wxLogin"
				const params = {
					code,
					type: 1
				}
				uni.showLoading({
					mask: true,
					title: "登录中"
				})
				request.casGet("/wechat/login", params).then(({ data }) => {
					this.wxCode = code
					this.unionId = data.union_id
					this.onLogin(data.union_id, code)
				})
			}
		},
		goRecords() {
			window.location.href = "https://beian.miit.gov.cn/"
		},
		goPage(url) {
			Tool.goPage.push(url)
		},
		onCheckAutoLogin() {
			this.isAutoLogin = !this.isAutoLogin
		},
		// 跳转回调地址
		async goCallback({ tk, ck }) {
			if (tk) {
				await setCookie(tk)
				localStorage.setItem("tk", tk)
			}
			if (ck) {
				document.cookie = `Authorization=${ck}; path=/`
			}
			const redirectLogin = sessionStorage.getItem("redirectLogin")
			if (redirectLogin && redirectLogin.indexOf("/pages/login/login") === -1) {
				// 跳转回原来的页面
				uni.redirectTo({
					url: redirectLogin
				})
				Tool.goPage.replace(redirectLogin)
			} else {

				Tool.goPage.replace("/")
			}
		},
		//登录
		onLogin(union_id, code) {
			const params = {}
			if (!this.canSubmit && this.loginMode !== "wxLogin") return
			switch (this.loginMode) {
				case "mobile":
					console.log("手机号登录")
					params.appId = uni.getStorageSync("appId")
					params.auto_register = true // 自动注册
					params.credential = this.loginParams.mobile // 手机号
					params.login_type = 4 // 4 手机号验证码类型
					params.secret = this.loginParams.secret // 验证码
					params.register = true
					params.expire = this.expire ? 30 : 0
					break
				case "password":
					console.log("密码登录")
					params.appId = uni.getStorageSync("appId")
					params.auto_register = false // 自动注册
					params.credential = this.loginParams.mobile // 手机号
					params.login_type = 3 //  3 手机号密碼类型
					params.secret = w_md5.hex_md5_32(this.loginParams.secret) // 密码
					params.expire = this.expire ? 30 : 0
					break
				case "wxLogin":
					if (!this.wxMobile) {
						console.log("unionId 登录")
						//unionId 登录
						params.auto_register = true // 自动注册
						params.credential = this.unionId
						params.secret = this.wxCode // 验证码
						params.login_type = 5 // 5 微信
					} else {
						//绑定手机号
						console.log("绑定手机号")
						params.auto_register = true // 自动注册
						params.credential = this.loginParams.mobile // 手机号
						params.secret = this.loginParams.secret // 验证码
						params.login_type = 4 // 4 手机号验证码类型
						params.union_id = this.unionId
						params.code = this.wxCode
					}
					params.appId = uni.getStorageSync("appId")
					params.register = true
					params.expire = this.expire ? 30 : 0
					break
				default:
					console.error("未知的登录类型")
			}
			uni.showLoading({
				mask: true,
				title: "登录中"
			})
			request
				.casPost("/user/login", params, { intercept: false })
				.then(({ data, code, message }) => {
					uni.hideLoading()
					console.log(params)
					console.log(params.login_type)
					if (code === 20000) {
						this.goCallback({ tk: data.tk })
					} else if (params.login_type === 5) {
						//微信登录，用户未注册
						console.log("微信登录，用户未注册")
						this.wxBingVisit = true
						this.wxMobile = true
					} else {
						uni.showToast({
							icon: "none",
							title: message
						})
					}
				})
		}
	}
}
</script>

<style lang="scss" scoped>
page {
	height: 100%;
}

.login {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: fixed;
	// background: linear-gradient(144deg, #ddedff 0%, #f2f4ff 100%);
	//background: url("@/static/image/bg_loginpage.png") no-repeat;
	// background-size:  100% 100%;
	height: 100%;
	width: 100%;
	overflow: scroll;

	.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 70rpx;
		/* #ifdef H5 */
		margin-top: 86rpx;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		margin-top: 217rpx;
		/* #endif */
		// margin-left: 60rpx;
		font-size: 50rpx;

		[data-theme="blue"] & {
			font-size: 48rpx;
		}

		color: #000;

		.logo_icon {
			height: 70rpx;
			// width: 70rpx;
			// margin-right: 20rpx;
		}
	}

	.sub_title {
		text-align: center;
		color: #14131f;
		font-size: 26rpx;
		margin-top: 26rpx;
	}

	.main {
		position: relative;
		margin: 120rpx 55rpx 0;
		background-color: #fff;
		border-radius: 25rpx;
		overflow: hidden;
		height: 600rpx;

		.login-tab {
			display: flex;
			margin: 54rpx 0 48rpx;
			font-weight: 500;
			color: #999999;
			position: relative;

			.tab {
				display: relative;
				flex: 1;
				font-size: 32rpx;
				z-index: 2;

				&:first-child {
					padding-right: 50rpx;
					text-align: right;
				}

				&:last-child {
					padding-left: 50rpx;
					text-align: left;
				}
			}

			&::after {
				content: "";
				position: absolute;
				display: block;
				width: 96rpx;
				height: 8rpx;
				background-color: var(--theme-color);
				border-radius: 5rpx;
				bottom: 0;
				transition: left 0.3s ease;
			}

			&.mobile {
				.tab {
					&:first-child {
						color: #000000;
					}
				}

				&::after {
					left: 142rpx;
				}
			}

			&.password {
				.tab {
					&:last-child {
						color: #000000;
					}
				}

				&::after {
					left: 385rpx;
				}
			}
		}

		.title {
			font-size: 32rpx;
			margin-left: 46rpx;
			margin-top: 60rpx;
			font-weight: 600;
			margin-bottom: 40rpx;
		}

		.addition {
			display: flex;
			justify-content: space-between;
			margin: -15rpx 45rpx 0;
			font-size: 26rpx;

			.left {
				color: #969696 !important;
			}

			.right {
				display: flex;
				align-items: center;
				font-weight: 500;
			}
		}

		.login-btn {
			margin: 34rpx 43rpx 0;
		}
	}

	.submit_bg {
		$m: 55rpx;
		margin: 0rpx $m 0;
		width: calc(100% - ($m * 2));
		// position: absolute;
		// left: 55rpx;
		// right: 55rpx;
		// bottom: -70rpx;
		// left: 50%;
		// background-color: #fff;
		// width: 151rpx;
		// height: 151rpx;
		// border-radius: 50%;
		// background: #fff;
		// transform: translate(-50%,50%);
		// --radius-size: 50rpx;
	}

	// .submit_bg::before,
	// .submit_bg::after{
	// 	content: '';
	// 	display: block;
	// 	position: absolute;
	// 	height: var(--radius-size);
	// 	width: var(--radius-size);
	// 	background-color: #fff;
	// 	bottom: 27rpx;
	// 	background: radial-gradient(
	// 		var(--radius-size) at var(--radius-size) 0px,
	// 		transparent var(--radius-size),
	// 		#fff var(--radius-size)
	// 	);
	// }
	// .submit_bg::before{
	// 	left: calc(-0.7 * var(--radius-size));
	// 	transform: scale(-1);
	// }
	// .submit_bg::after{
	// 	right: calc(-0.7 * var(--radius-size));
	// 	transform: scale(1) rotate(90deg);
	// }
}

.order-login {
	margin-top: 180rpx;

	.wx {
		display: flex;
		align-items: center;
		justify-content: center;
		color: #050505;

		.wx-login {
			width: 80rpx;
			height: 80rpx;
		}

		.line-left,
		.line-right {
			width: 156rpx;
			height: 1rpx;
		}

		.line-left {
			margin-right: 33rpx;
			background-color: rgba(59, 65, 71, 0.46);
			// background: linear-gradient(
			// 	270deg,
			// 	#050505 0%,
			// 	rgba(119, 119, 119, 0) 100%
			// );
		}

		.line-right {
			margin-left: 33rpx;
			// background: linear-gradient(90deg, #050505 0%, rgba(119, 119, 119, 0) 100%);
			background-color: rgba(59, 65, 71, 0.46);
		}

		// .btn-group {
		// 	margin-top: 23rpx;
		// 	display: flex;
		// 	justify-content: center;
		// 	opacity: 0.8;
		// 	.btn {
		// 		width: 76rpx;
		// 		height: 76rpx;
		// 	}
		// }
	}

	.tip {
		text-align: center;
		font-size: 24rpx;

		color: #0e102b;
		margin-top: 12rpx;
	}
}

.internet-content-provider {
	margin-top: 70rpx;
	margin-bottom: 40rpx;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	color: rgb(162, 175, 185);
	font-size: 22rpx;
	line-height: 30rpx;
}

.auto-login {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	width: 100%;
	background-color: #fff;

	.auto-login-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}

	.store-name {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 60rpx;
	}

	.loading-spinner {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 30rpx;

		.spinner-dot {
			width: 20rpx;
			height: 20rpx;
			border-radius: 50%;
			margin: 0 10rpx;
			opacity: 0.7;
			animation: bounce 1.4s infinite ease-in-out both;
		}
	}

	.auto-login-text {
		font-size: 28rpx;
		color: #666;
	}

	@keyframes bounce {

		0%,
		80%,
		100% {
			transform: scale(0);
		}

		40% {
			transform: scale(1.0);
		}
	}
}
</style>
