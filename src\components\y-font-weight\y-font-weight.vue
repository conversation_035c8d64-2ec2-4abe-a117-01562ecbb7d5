<!-- 兼容苹果与安卓 font-weight 不一致问题 -->
<template>
	<!-- 500 -->
	<template v-if="props.fontWeight === 500">
		<template v-if="isIos">
			<text style="font-weight: 600; whiteSpace: 'nowrap'">
				<slot></slot>
			</text>
		</template>
		<template v-else>
			<text style="font-weight: 500;whiteSpace: 'nowrap'">
				<slot></slot>
			</text>
		</template>
	</template>
	
</template>

<script setup>
	import { onMounted, ref } from 'vue'
	
	const props = defineProps({
		fontWeight:{
			type: Number,
			default: 500
		}
	})
	
	
	const isIos = ref(false)
	onMounted(()=>{
		if(uni.getSystemInfoSync().platform == 'ios') isIos.value = true
	})
</script>