<template>
	<view class="loadmore-box" :class="[theme]">
		<image class="icon" v-if="theme === 'dark'" src="@/static/image/loading-dark-icon.png" mode="widthFix"></image>
		<image class="icon" v-if="theme === 'light'" src="@/static/image/loading-light-icon.png" mode="widthFix"></image>
		<view class="tip" v-show="status === 'loadmore'">加载更多...</view>
		<view class="tip" v-show="status === 'loading'">正在加载中...</view>
		<view class="tip" v-show="status === 'nomore'">基于联盟链的全场景目的地云服务开创者</view>
	</view>

</template>

<script setup>
	const props = defineProps({
		/**
		 * @param loadmore	加载前
		 * @param loading 加载中
		 * @param nomore 没有更多了
		*/
		status:{
			type: String,
			default: 'nomore'
		},
		/**
		 * @param light 明亮的
		 * @param dark 黑暗的
		*/
		theme: {
			type: String,
			default: 'dark'
		}
	})
</script>

<style lang="scss" scoped>
	.loadmore-box{
		margin-top: 60rpx;
		font-size: 26rpx;
		text-align: center;
		.icon{
			width: 175rpx;
		}
		.tip{
			margin-top: 12rpx;
			font-size: 24rpx;
			font-weight: 400;
		}
		&.dark .tip{
			color: #C5C5C5;
		}
		&.light .tip{
			color: #fff;
		}
	}
</style>