<style lang="scss" scoped>
@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.icon.spin-animation {
  animation: spin 1s linear infinite;
}
.ticket-nav {
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  .icon {
    margin-left: 10rpx;
  }
  .seach-input {
    margin-left: 20rpx;
    flex: 1;
    display: flex;
    align-items: center;
    // width: 500rpx;
    height: 60rpx;
    padding: 0 23rpx;
    border-radius: 45rpx;
    background: #fff;
    font-size: 28rpx;
    .input {
      color: #333;
      caret-color: #999;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50rpx;
      height: 50rpx;
    }

    .placeholder-style {
      font-size: 28rpx;
      color: #999999;
      margin-left: 8rpx;
    }
  }
  .location {
    display: flex;
    align-items: center;
    .hidd {
      display: inline-block;
      max-width: 60px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
<template>
  <view class="ticket-nav">
    <!-- <view class="location" @click="Tool.goPage.push('/pages/locationSearch/locationSearch')">
			<uni-icons v-if="isLocationing" class="icon spin-animation" type="spinner-cycle" size="15" color="#000"></uni-icons>
			<template v-if="isLocationing">
				<text>定位中</text>
			</template>
			<template v-else>
				<text class="hidd">{{ locationData.cityName }}</text>
				<uni-icons class="icon" type="bottom" color="#000" size="15"></uni-icons>
			</template>
		</view> -->
    <view class="seach-input" @click="toSearch" v-if="pageConfig.pageComponents.length === 0">
      <uni-icons class="icon" type="search" size="25" color="#cacaca"></uni-icons>
      <!-- <input class="input" confirm-type="search" v-model="inputSearch" @confirm="onSearch('confirm')"
				@input="onSearch('input')" @focus="focusSearch" placeholder-class="placeholder-style"
				placeholder="搜索门票·景点" /> -->
      <view style="color: #999">搜索门票·景点·攻略·资讯</view>
    </view>
    <view :style="pageStyle" v-else>
      <template v-for="(com, index) in pageConfig.pageComponents" :key="index">
        <component :config="com.setStyle" :is="componentList[com.component]" />
      </template>
    </view>
  </view>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import useLocation from "@/hooks/useLocation";
// import lSearch from "@/pages/home/<USER>/l-search.vue";
import { getRoute } from "@/utils/tool.js";
const { locationInfo, locationStatus, locationInit } = useLocation({
  geocoder: true,
});
const pageConfig = ref({
  pageSetup: {},
  pageComponents: [],
});

const componentList = reactive({
  globalsearch: defineAsyncComponent(() => import("@/pages/home/<USER>/l-search")),
});

// 页面
// 选中的 tab
const props = defineProps({
  activeTab: {
    type: Number,
    default: 0,
    required: true,
  },
  tabList: {
    type: Array,
    default: () => ["景区门票", "组合套票", "权益卡"],
  },
});
const inputSearch = ref("");
// tabBar 列表
// const tabList = reactive(['景点门票', '组合套票', '权益卡']);
const emits = defineEmits(["getLocationInfo", "onSearch"]);
const locationData = ref({});
const toSearch = () => {
  Tool.goPage.push("/pages/gloabSearch/gloabSearch");
};
onShow(async () => {
  let info = uni.getStorageSync("selectedCity");
  if (info) {
    info = JSON.parse(info);
  } else {
    await locationInit();
    info = locationInfo.value;
  }

  console.log(info);
  locationData.value = info;
  uni.setStorageSync("selectedCity", JSON.stringify(info));
  emits("getLocationInfo", info);
});

const isLocationing = computed(() => {
  return locationStatus.value === "waiting" && locationData.value.cityName === "";
});

const onSearch = () => {
  emits("onSearch", inputSearch.value);
};

// 页面样式
const pageStyle = computed(() => {
  const { pageSetup = {} } = pageConfig.value;
  const { bgColor = "" } = pageSetup;
  return {
    backgroundColor: bgColor,
    width: "100%",
    // paddingTop: "20rpx"
  };
});

onMounted(async () => {
  const { storeId, pageId } = getRoute.params();
  // 活动页
  if (pageId) {
    const {
      data: { pageContent },
    } = await request.get("/store/design/info", { id: pageId });
    let storeConfig = JSON.parse(pageContent);
    pageConfig.value = storeConfig;
    return;
  }
  let storeConfig = await Tool.getStoreConfig();
  if (!Tool.isEmpty(storeConfig)) {
    pageConfig.value = storeConfig;
    console.log(pageConfig.value, 88);
  }
});
</script>
