<template>
  <div class="topimg">
    <div class="imageWrapper">
      <img :src="config.imgUrl || defaultImg" alt="默认图片" draggable="false" />
    </div>
    <div v-if="config.isShowQuestion" class="questionWrapper">
      <span class="leftText">"我想去{{ currentCity }}旅游"</span>
      <div class="confrimBtn" @click="toAi">问一问</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'topimg',
    props: {
      config: Object,
    },
    data() {
      return {
        defaultImg: 'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_ccbfa918ded29e8b46ac5314e114ab59.png', // 有问一问
        popular_cities: [
          '北京',
          '上海',
          '广州',
          '深圳',
          '重庆',
          '成都',
          '杭州',
          '西安',
          '南京',
          '天津',
          '苏州',
          '青岛',
          '大连',
          '厦门',
          '三亚',
          '海口',
          '丽江',
          '大理',
          '昆明',
          '拉萨',
          '乌鲁木齐',
          '阿勒泰',
          '张家界',
          '桂林',
          '贵阳',
          '遵义',
          '长沙',
          '株洲',
          '武汉',
          '洛阳',
          '郑州',
          '开封',
          '哈尔滨',
          '长春',
          '沈阳',
          '呼伦贝尔',
          '包头',
          '银川',
          '西宁',
          '南宁',
          '北海',
          '舟山',
          '台州',
          '香港',
          '澳门',
        ],
        currentCity: '',
      };
    },
    mounted() {
      // 页面加载时随机选取一个城市
      this.getRandomCity();
    },
    methods: {
      getRandomCity() {
        // 生成一个0到数组长度减1之间的随机整数
        const randomIndex = Math.floor(Math.random() * this.popular_cities.length);
        // 根据随机索引获取城市并赋值给currentCity
        this.currentCity = this.popular_cities[randomIndex];
      },
      toAi() {
        const aiUrl = ref(getEnv().VITE_AI_URL);
        const cookie = Tool.getCookie('Authorization');
        const targetPath = `/chat`;
        window.location.href = `${aiUrl.value}login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent(window.location.href)}&targetPath=${encodeURIComponent(targetPath)}&message=${encodeURIComponent(`我想去${this.currentCity}旅游`)}`;
      },
    },
  };
</script>

<style scoped lang="scss">
  .topimg {
    position: relative;
    text-align: center;
    padding: 0;
    background: #fff;
    .imageWrapper {
      height: 210px;
      width: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .questionWrapper {
      padding: 0 6px 0 12px;
      position: absolute;
      top: 70%;
      left: 5%;
      width: 85%;
      height: 47px;
      background: rgba(115, 147, 255, 0.43);
      border-radius: 24px;
      border: 1px solid #ffffff;
      backdrop-filter: blur(1.612px);
      display: flex;
      align-items: center;
      justify-content: space-between;
      .leftText {
        color: #ffffff;
        font-size: 15px;
      }
      .confrimBtn {
        width: 69px;
        height: 36px;
        line-height: 36px;
        background: #ffffff;
        border-radius: 20px;
        opacity: 0.95;
        color: #3c6bff;
      }
    }
  }
</style>
