<style lang="scss" scoped>
.y-list {
	margin-bottom: 20rpx;
	background-color: #fff;

	.y-list-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-left: 40rpx;
		padding-right: 40rpx;
		height: 100rpx;

		&:not(:last-child) {
			border-bottom: 1rpx solid rgba(177, 177, 177, 0.39);
		}

		.y-list-item-left {
			flex: none;
			margin-right: 10rpx;
			font-size: 34rpx;
			font-weight: 400;
			color: #14131f;
		}

		.y-list-item-input {
			text-align: right;
		}

		.y-list-item-placeholder {
			text-align: right;
		}

		.y-list-item-right {}

		.y-list-item-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}
	}
}
</style>

<template>
	<view class="y-list">
		<view class="y-list-item">
			<view class="y-list-item-left">123</view>
			<view class="y-list-item-right">wet</view>
		</view>
	</view>
</template>

<script setup>
import { ref, toRefs } from "vue"
import { getRandomColor } from "@/utils/tool.js"
const props = defineProps({
	ticket: {
		type: Object,
		default: {},
		required: true
	},
	mold: {
		type: Number,
		default: 0,
		required: false
	}
})

</script>
