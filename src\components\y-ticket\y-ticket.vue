<template>
	<view class="ticket">
		<view class="img-box">
			<template v-if="ticket.picUrl">
				<image class="image" mode="aspectFill" :src="`${imgHost}${ticket.picUrl.split(',')[0]}`" v-if="mold == 0">
				</image>
				<image class="image" mode="aspectFill" :src="imgHost + ticket.picUrl.split(',')[0]" v-if="mold == 1"></image>
			</template>
			<template v-else>
				<image class="image" mode="aspectFill"
					src="https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png">
				</image>
			</template>
		</view>
		<!-- 单票 -->
		<view class="content" v-if="mold == 0">
			<view class="title">{{ ticket.scenicName
			}}<text class="level" v-if="ticket.scenicGradle != '0'">{{ ticket.scenicGradle == 1 ? "" : ticket.scenicGradle
				}}A</text></view>
			<view class="info" style="margin-bottom: 6rpx;">{{ ticket.address }}</view>
			<view class="info flex-row">
				<view v-if="ticket.scoreAverage" class="score-wrapper">
					{{ ticket.scoreAverage?.toFixed(1) }}
				</view>
				<text class="comment-count" v-if="ticket.commentNumber">{{ ticket.commentNumber }}条点评</text>
				<text class="comment-count" v-else>暂无点评</text>
			</view>
			<view class="pic" style="text-align: right;"><text class="unit">¥</text><text class="num">{{ ticket.price
			}}</text><text class="up">起</text></view>
		</view>
		<!-- 组合 -->
		<view class="content" v-if="mold == 1">
			<view class="title">{{ ticket.name
			}}<text v-if="ticket.isRealName == 1" class="level">实名制</text></view>
			<view class="info">{{ productName }}</view>
			<view class="pic"><text class="unit">¥</text><text class="num">{{ ticket.storeGoodsPrice }}</text><text
					class="up">起</text></view>
		</view>
	</view>
</template>

<script setup>
import { ref, toRefs, watch } from "vue"
import { getEnv } from "@/utils/getEnv";
import { getRandomColor } from "@/utils/tool.js"
const props = defineProps({
	ticket: {
		type: Object,
		default: () => ({}),
		required: true
	},
	mold: {
		type: Number,
		default: 0,
		required: false
	}
})
const imgHost = ref(getEnv().VITE_IMG_HOST)
const productName = ref("")
watch(
	() => props.ticket,
	val => {
		if (val.detailList) {
			productName.value = val.detailList.map(item => item.productName).join("+")
		}
	},
	{
		immediate: true
	}
)
</script>

<style lang="scss" scoped>
.ticket {
	--radius-size: 16rpx;
	margin-bottom: 20rpx;
	width: 100%;
	border-radius: var(--radius-size);
	background-color: #fff;
	display: flex;
	padding: 16rpx;
	box-shadow: 0px 4px 12px 1px rgba(188, 217, 255, 0.38);

	>.img-box {
		border-radius: 10rpx;
		margin-right: 20rpx;
		width: 210rpx;
		height: 164rpx;

		.image {
			width: 100%;
			height: 100%;
			border-radius: 10rpx;
		}
	}

	.center {
		padding: 30rpx 0;
		$w: 35rpx;
		display: flex;
		flex: none;
		width: $w;
		height: 100%;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: transparent;
		background: radial-gradient(circle at top, transparent 16.5rpx, #fff 0) top,
			radial-gradient(circle at bottom, transparent 16.5rpx, #fff 0) bottom;
		background-size: 100% 50%;
		background-repeat: no-repeat;

		.line {
			height: 100% !important;
			width: 3rpx;
		}
	}

	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		width: 0;
		background-color: #fff;
		border-top-right-radius: 10rpx;
		border-bottom-right-radius: 10rpx;

		.title {
			margin-bottom: 6rpx;
			font-size: 32rpx;
			font-weight: 600;
			line-height: 45rpx;
			display: flex;
			align-items: center;

			.level {
				flex: none;
				color: #ff772f;
				background-color: #ffe7ca;
				display: inline-block;
				border-radius: 6rpx;
				margin-left: 14rpx;
				padding: 6rpx 12rpx;
				font-size: 22rpx;
				text-align: center;
				font-weight: 400;
				line-height: 22rpx;
			}
		}

		.info {
			padding-right: 7px;
			color: #868686;
			font-size: 26rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			flex: 1;
			line-height: 37rpx;
		}

		.pic {
			margin-top: -15rpx;
			color: #f43636;
			line-height: 42rpx;

			.unit {
				font-size: 28rpx;
			}

			.num {
				margin: 0 4rpx;
				font-size: 42rpx;
				font-weight: 600;
			}

			.up {
				color: #6b6b6b;
				font-size: 26rpx;
			}
		}
	}
}

.flex-row {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.score-wrapper {
	margin-right: 10rpx;
	background-color: rgba(52, 159, 255, 1);
	border-radius: 10rpx 10rpx 0rpx 10rpx;
	height: 32rpx;
	width: 58rpx;
	padding: 4rpx 11rpx;
	color: #fff;
	font-size: 24rpx;
	font-weight: 600;
	line-height: 24rpx;
}

.comment-count {

	overflow-wrap: break-word;
	color: rgba(102, 102, 102, 1);
	font-size: 24rpx;
	letter-spacing: 0.20999999344348907rpx;
	font-weight: normal;
	text-align: left;
	white-space: nowrap;
	line-height: 33rpx;
}
</style>
