<template>
	<view class="order-detail-footer">
		<!-- 待支付 -->
		<template v-if="footerType === 'unpaid'">
			<view class="total">
				<text class="unit">¥</text>{{ props.order.totalAmount }}
			</view>
			<view class="cancel" @tap="onSub">取消订单</view>
			<view class="pay" @tap="toPay">去支付</view>
		</template>
		<!-- 已取消 -->
		<template v-if="footerType === 'canceled'">
			<view class="total">
				<text class="unit">¥</text>{{ props.order.totalAmount }}
			</view>
		</template>
		<!-- 已出票 -->
		<template v-if="footerType === 'issued'">
			<view v-if="!props.order.isTravelCard && canRefund" class="pay" :class="{ disabled: refundCheckLoading }"
				@tap="upRefund">
				{{ refundCheckLoading ? '检验中...' : '退票' }}
			</view>
			<view class="go-home" @click="goHome">店铺首页</view>
		</template>
		<!-- 出票/出库失败 -->
		<template v-if="footerType === 'lose'">
			<view class="total">
				<text class="unit">¥</text>{{ order.totalAmount }}
			</view>
			<view class="go-home" @click="goHome"> 店铺首页 </view>
			<view class="pay" v-if="order.isRefund" @tap="reimburse">退款</view>
		</template>
		<!-- 退款中 -->
		<template v-if="footerType === 'refunding'">
			<view class="total refunding">
				<view class="price">
					<text>退单金额&ensp;&ensp;</text>
					<labele>¥{{ order.refundAmount }}</labele>
				</view>
				<text class="hint">(扣除手续费￥{{ order.refundFee }})</text>
			</view>
		</template>
		<!-- 退款成功 -->
		<template v-if="footerType === 'refundSuccess'">
			<view class="go-home" @click="goHome"> 店铺首页 </view>
		</template>
		<!-- 退款 - 提交申请 -->
		<template v-if="footerType === 'issueding'">
			<view class="issueding">
				<checkbox-group class="checkbox" @change="checkboxChange">
					<checkbox :value="true" :checked="isCheckAll" color="#61B4FC" style="transform: scale(0.8)" />
					<text>全选</text>
				</checkbox-group>
				<view class="group">
					<view class="cancel" @tap="onClose">取消</view>
					<view class="pay" @tap="toRefund">提交申请</view>
				</view>
			</view>
		</template>
		<!-- 已退款 -->
		<template v-if="footerType === 'refunded'">
			<view class="total"> <text class="unit">¥</text>120 </view>
			<view class="cancel" @tap="onSub">取消订单</view>
			<view class="pay" @tap="toPay">去支付</view>
		</template>
	</view>
</template>
<script setup>
import request from "@/utils/request.js"
import Big from "big.js"
import { onMounted, reactive, ref, watch } from "vue"
import { getEnv } from "@/utils/getEnv";

const { VITE_MERCHANTS_HOST } = getEnv()

const props = defineProps({
	order: {
		type: Object,
		default: () => ({})
	},
	// 退票列表
	refundTicketList: {
		type: Array,
		default: () => []
	},
	modelValue: {
		type: Array,
		default: () => []
	},
	isCheckAll: {
		type: Boolean,
		default: true
	}
})

const showRefundBtn = ref(false)
const footerType = ref("unpaid")
const canRefund = ref(true) // 新增：是否可以退票
const refundCheckLoading = ref(false) // 新增：退票检验加载状态

// 新增：检验退票资格
const checkRefundEligibility = async () => {
	if (refundCheckLoading.value) return

	try {
		refundCheckLoading.value = true

		// 先触发获取所有票 ID
		emits("onCheck", { type: "all" })

		// 等待一个事件循环，确保父组件更新了 refundTicketList
		await new Promise(resolve => setTimeout(resolve, 0))

		if (props.refundTicketList.length === 0) {
			canRefund.value = false
			return
		}

		const { code, data } = await request.post(
			`/ticketRetreat/checkRetreat`,
			props.refundTicketList
		)

		// 只有当所有票都不可退票时，才隐藏退票按钮
		const allCannotRefund = data.every(item => item.isRetreat === 0)
		canRefund.value = !allCannotRefund

	} catch (err) {
		console.log('退票检验失败：', err)
		canRefund.value = true // 检验失败时默认显示退票按钮
	} finally {
		refundCheckLoading.value = false
	}
}

onMounted(() => {
	console.log(props.type)
})

// 监听订单状态变化和退票检验
watch(
	() => [props.order.orderStatus, props.order.orderGroupId],
	async (newValues, prevValues) => {
		const { order } = props
		const [orderStatus] = newValues

		switch (orderStatus) {
			case "13":
				footerType.value = "canceled"
				break
			case "20":
				footerType.value = "unpaid"
				break
			case "21":
				if (order.isTravelCard) {
					footerType.value = "refundSuccess"
				} else {
					footerType.value = "issued"
					// 已出票状态，检验退票资格
					await checkRefundEligibility()
				}
				break
			case "30":
				if (order.isTravelCard) {
					footerType.value = "refundSuccess"
				} else if (!order.isRefund) {
					footerType.value = "refunding"
				} else {
					footerType.value = "issued"
					// 已出票状态，检验退票资格
					await checkRefundEligibility()
				}
				break
			case "32":
				footerType.value = "lose"
				break
			case "33":
				footerType.value = "lose"
				break
			case "51":
				footerType.value = "refundSuccess"
				break
			case 10:
				break
			default:
				footerType.value = "refundSuccess"
				break
		}
	},
	{
		deep: true,
		immediate: true
	}
)

const state = reactive({
	ticketList: []
})
const goHome = () => {
	Tool.goPage.tab("home")
}
const refundAmount = {
	amount: 0,
	refundFee: 0,
	refundAmount: 0
}

//监听退票后票的勾选 普通单票
// watch(
// 	() => props.order.touristInfo,
// 	(newValues, prevValues) => {
// 		console.log("order===============================")
// 		console.log(props.order)
// 		console.log("footer", newValues)
// 		state.ticketList = []
// 		let bool = true
// 		newValues.map(k => {
// 			if (!k.disabled) {
// 				if (k.showRefund) {
// 					state.ticketList.push(k.ticketNumber)
// 				} else {
// 					bool = false
// 				}
// 			}
// 		})
// 		isCheckAll.value = bool
// 	},
// 	{
// 		deep: true
// 	}
// )

const emits = defineEmits(["reload", "onCheck"]) //退票更改 type
//退票
const upRefund = () => {
	if (props.order.isCompose) {
		// 组合票直接退
		emits("onCheck", { type: "all" })
		setTimeout(() => {
			toRefund()
		}, 0)
	} else {
		// 一张票直接退，无需勾选
		console.log(props.order.touristInfo)
		if (props.order.touristInfo.length == 1) {
			emits("onCheck", { type: "all" })
			setTimeout(() => {
				toRefund()
			}, 0)
		} else {
			emits("onCheck", { type: "all" })
			footerType.value = "issueding"
		}
	}
}
const onClose = () => {
	emits("onCheck", { type: "none" })
}
// watch(
// 	() => props.isCheckAll,
// 	(newValues, prevValues) => {
// 		if (!newValues) {
// 			footerType.value = "issued"
// 		}
// 	},
// 	{
// 		deep: true
// 	}
// )
//点击全选
const checkboxChange = e => {
	if (props.isCheckAll) {
		emits("onCheck", { type: "none" })
	} else {
		emits("onCheck", { type: "all" })
	}
	// if (e.detail.value[0] == true || e.detail.value[0] == "true") {
	// 	isCheckAll.value = true
	// } else {
	// 	isCheckAll.value = false
	// }
	// state.ticketList = []
	// props.order.touristInfo.map(k => {
	// 	if (Array.isArray(k)) {
	// 		// 组合票
	// 		k.forEach(item => {
	// 			if (isCheckAll.value) {
	// 				if (!item.disabled) {
	// 					item.check = true
	// 				} else {
	// 					item.check = false
	// 				}
	// 			} else {
	// 				item.check = false
	// 				state.ticketList = []
	// 			}
	// 		})
	// 	} else {
	// 		// 普通票
	// 		if (isCheckAll.value) {
	// 			console.log("disabled", !k.disabled, k.disabled)
	// 			if (!k.disabled) {
	// 				k.showRefund = true
	// 			} else {
	// 				k.showRefund = false
	// 			}
	// 		} else {
	// 			k.showRefund = false
	// 			state.ticketList = []
	// 		}
	// 	}
	// })
	// // console.log(props.order.touristInfo);
}

//订单失败退款
const reimburse = () => {
	const p = {
		isFail: 1,
		refundAmount: props.order.totalAmount,
		orderId: props.order.orderGroupId
	}
	uni.setStorageSync("refund_data", JSON.stringify(p))
	Tool.goPage.push(`/pages/refund/refund`)
}
//检验退票
const toRefund = async () => {
	const { refundTicketList, order } = props
	console.log(refundTicketList)
	if (refundTicketList.length == 0) return
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.post(
			`/ticketRetreat/checkRetreat`,
			refundTicketList
		)
		data.map((n, i) => {
			if (n.isRetreat == 0) {
				uni.showToast({
					title: "该票不符合退票规则",
					icon: "none",
					duration: 2000
				})
				return
			}
		})
		const amountData = {
			refundOrder: 0,
			refundFee: 0,
			refundAmount: 0
		}
		console.log("===data======")
		console.log(data)
		// 计算金额
		if (order.isCompose) {
			// 组合票
			console.log(order.ticketPackageInfo)
			order.composeTicketList.forEach(item => {
				item?.orderTicketInfoList?.forEach(e => {
					const tNumber = e.ticketNumber
					const onePri = data.find(s => s.ticketId === tNumber)

					let num = 1

					if (e.issueTicketType === 1) {
						// 一票多人
						num = e.playerNum
					}

					amountData.refundOrder = new Big(num * onePri.amount).plus(
						amountData.refundOrder
					)
					amountData.refundFee = new Big(num * onePri.refundFee).plus(
						amountData.refundFee
					)
					amountData.refundAmount = new Big(num * onePri.refundAmount).plus(
						amountData.refundAmount
					)
				})
			})
		} else {
			// 普通票
			const { ticketNumber } = order
			const num = 1
			if (
				props.order.touristInfo &&
				props.order.touristInfo[0].issueTicketType === 1
			) {
				// 一票多人
				const tNumber = data[0].returnableQuantity * 1 // 可退数量
				for (let i = 0; i < tNumber; i++) {
					amountData.refundOrder = new Big(data[0].amount).plus(
						amountData.refundOrder
					)
					amountData.refundFee = new Big(data[0].refundFee).plus(
						amountData.refundFee
					)
					amountData.refundAmount = new Big(data[0].refundAmount).plus(
						amountData.refundAmount
					)
				}
			} else {
				// 一票一人
				data.forEach(item => {
					amountData.refundOrder = new Big(item.amount).plus(
						amountData.refundOrder
					)
					amountData.refundFee = new Big(item.refundFee).plus(
						amountData.refundFee
					)
					amountData.refundAmount = new Big(item.refundAmount).plus(
						amountData.refundAmount
					)
				})
			}
			console.log(props.order.touristInfo)
		}
		const p = {
			payType: order.payType,
			orderId: order.orderGroupId,
			ticketList: refundTicketList,
			...amountData
		}
		console.log(p)
		uni.setStorageSync("refund_data", JSON.stringify(p))
		Tool.goPage.push(`/pages/refund/refund`)
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//取消订单
const onSub = () => {
	uni.showModal({
		title: "确定取消订单吗？",
		//   content: i.tip,
		confirmText: "确定取消",
		cancelText: "暂不取消",
		success: async function (res) {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: "易旅宝",
						mask: true
					})
					const { code, data } = await request.put(`/order/orderCancel`, {
						orderId: props.order.orderGroupId
					})
					uni.showToast({
						title: "订单取消成功！"
					})
					//刷新页面
					Tool.goPage.tab("order")
				} catch (err) {
					console.log(err)
				}
				uni.hideLoading()
			}
		}
	})
}
// defineExpose({ state });
//去支付
const toPay = async () => {
	console.log(props.order)
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const orderStatusRes = await request.get(
		`/order/orderStatus/${props.order.orderGroupId}`
	)
	// TODO: 两个接口应该同步请求
	if (orderStatusRes.data !== "20") {
		uni.showToast({
			icon: "error",
			title: "订单异常！"
		})
		Tool.goPage.replace()
		return
	}
	// 实名票验证
	const {
		touristInfo,
		ticketPackageInfo,
		isSingle,
		isCompose,
		isTravelCard,
		composeInfo,
		travelCardInfo,
		ticketInfo
	} = props.order
	const realNameCheckList = []
	if (isSingle) {
		// 单票
		touristInfo.forEach(item => {
			item.realNameList.forEach(e => {
				realNameCheckList.push({
					checkType: 1,
					goodsName: ticketInfo.productSkuName,
					enterTime: ticketInfo.day,
					goodsId: ticketInfo.productSkuId,
					idCard: e.identity
				})
			})
		})
	} else if (isCompose) {
		console.log(ticketPackageInfo)
		// 组合票
		ticketPackageInfo.forEach(item => {
			item.forEach(e => {
				if (!e.realNameList) return
				e.realNameList.forEach(i => {
					realNameCheckList.push({
						checkType: 1,
						goodsName: e.productName,
						enterTime: composeInfo.day,
						goodsId: e.productSkuId,
						idCard: i.identity
					})
				})
			})
		})
	} else if (isTravelCard) {
		realNameCheckList.push({
			// enterTime: ticketInfo.day,
			checkType: 2,
			goodsName: travelCardInfo.productSkuName,
			goodsId: travelCardInfo.productSkuId,
			idCard: travelCardInfo.identity
		})
	}

	if (realNameCheckList.length > 0) {
		const { data } = await request.post(`/order/pay/realName/check`, {
			list: realNameCheckList
		})
		console.log(data)
		if (!data) {
			uni.showModal({
				content: "该身份证已购买",
				showCancel: false
			})
			return
		}
	}
	try {
		// #ifdef H5
		window.location.href = `${VITE_MERCHANTS_HOST
			}/business-platform.payment-channel.web/hqpay/payment-gateway/h5?tradeNo=${props.order.tradeNo}`
		// #endif
		// #ifdef MP-WEIXIN
		uni.login({
			success(res) {
				let requestUrl = `${VITE_MERCHANTS_HOST
					}/business-platform.payment-channel.web/hqpay/gateway/lite`
				requestUrl += "?channelPayType=WX_LITE"
				requestUrl += "&outTradeNo=" + props.order.orderGroupId
				requestUrl += "&merchantId=" + props.order.merchantId
				requestUrl += "&code=" + res.code
				uni.request({
					method: "GET",
					url: requestUrl,
					// 请求成功
					success: res => {
						uni.requestPayment({
							timeStamp: res.data.timeStamp,
							nonceStr: res.data.nonceStr,
							package: res.data.package,
							signType: res.data.signType,
							paySign: res.data.paySign,
							success: payRes => {
								uni.showToast({
									title: "支付成功！"
								})
								emits("reload")
								Tool.goPage.tab("order")
							},
							fail: payFail => {
								uni.showToast({
									title: "支付失败！"
								})
							}
						})
					},
					// 请求失败
					error: res => {
						uni.showToast({
							title: "请求失败！请检查网络配置！",
							icon: "none"
						})
					}
				})
			}
		})
		// #endif
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
</script>
<style lang="scss" scoped>
.order-detail-footer {
	position: fixed;
	bottom: 0;
	left: 0%;
	right: 0%;
	display: flex;
	justify-content: flex-end;
	padding-left: 40rpx;
	height: 140rpx;
	border-top: 1px solid #ececec;
	display: flex;
	align-items: center;
	background-color: #fff;

	.go-home {
		display: flex;
		align-items: center;
		padding: 0 57rpx;
		margin-right: 40rpx;
		height: 72rpx;
		border-radius: 36rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #050505;
		line-height: 45rpx;
		letter-spacing: 1rpx;
		border: 2rpx solid #b9b9b9;
	}

	.total {
		margin-right: auto;
		font-size: 54rpx;
		font-weight: 600;
		color: #f43636;
		line-height: 59rpx;

		.unit {
			font-size: 42rpx;
		}
	}

	.issueding {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.group {
			display: flex;
		}
	}

	.refunding {
		color: #050505;
		display: flex;
		flex-direction: column;

		.price {
			font-weight: 600;
			font-size: 28rpx;
			line-height: 58rpx;

			labele {
				color: #f43636;
				font-size: 42rpx;
			}
		}

		.hint {
			font-weight: 500;
			font-size: 24rpx;
			line-height: 24rpx;
		}
	}

	.checkbox {
		display: flex;
		align-items: center;

		text {
			margin-left: 13rpx;
		}
	}

	.cancel {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
		padding: 0 40rpx;
		height: 72rpx;
		border-radius: 36rpx;
		border: 2rpx solid rgb(185 185 185 / 50%);
		font-size: 32rpx;
		font-weight: 500;
		color: #050505;
		letter-spacing: 1rpx;
	}

	.pay {
		display: flex;
		align-items: center;
		padding: 0 57rpx;
		margin-right: 40rpx;
		height: 72rpx;
		background: #ff9201;
		border-radius: 36rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		line-height: 45rpx;
		letter-spacing: 1rpx;
	}

	.pay.disabled {
		background-color: #ccc !important;
		color: #999 !important;
		pointer-events: none;
	}
}
</style>
