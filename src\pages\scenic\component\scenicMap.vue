<template>
	<view class="scenic-map">
		<!-- 地图容器 -->
		<view id="container"></view>
		<!-- 可以在这里添加加载指示器 -->
		<view v-if="loading" class="loading-indicator">
			<text>加载中...</text>
		</view>
	</view>
</template>

<script setup>
import request from "@/utils/request.js";
import { onHide, onUnload, onShow } from "@dcloudio/uni-app";
import { onMounted, ref } from "vue";
// 导入工具函数
import { debounce, pointDistance } from "@/utils/tool.js";

// 接收父组件传递的属性
const props = defineProps({
	guideId: {
		type: String,
		default: ''
	}
});

// 定义事件
const emit = defineEmits(['pointSelected']);

const loading = ref(true); // 添加加载状态
const lastPoint = ref(null); // 当前选中的点位
let map = null; // 地图实例
let bounds = null; // 地图范围
let center = null; // 中心点坐标（景区）
let markerCluster = null; // 聚合对象
let clusterBubbleList = []; // 聚合气泡集
let clusterPointList = []; // 聚合点位集
let isClickPoint = false; // 点位是否被点击
let pointList = []; // 点位列表

// 点击地图点位
const clusterPointClick = (clusterPoint) => {
	// 移除点击事件处理逻辑，使点击点位时不执行任何操作
	return;
};

// 关闭点位弹窗
const closePointPop = () => {
	if (!isClickPoint && lastPoint.value) {
		lastPoint.value.dom.classList.remove("pointBig");
		// 聚合点位不存在时销毁
		if (
			!markerCluster
				.getClusters()
				.find(
					(v) =>
						v.geometries.length == 1 && v.geometries[0].id == lastPoint.value.id
				)
		) {
			lastPoint.value.destroy();
			// 从聚合点位列表中移除
			const index = clusterPointList.findIndex(
				(item) => item.id == lastPoint.value.id
			);
			if (index != -1) clusterPointList.splice(index, 1);
		}
		lastPoint.value = null;
	} else {
		isClickPoint = false;
	}
};

// 导航到点位
const nav = (id) => {
	if (window.navFrom && bounds.contains(new TMap.LatLng(window.navFrom[0], window.navFrom[1]))) {
		uni.navigateTo({
			url: `/pages/tour/tour?guideId=${props.guideId}&navPointId=${id}`
		});
	} else {
		uni.showToast({
			title: "您当前不在景区范围内",
			icon: "none",
		});
	}
};

// 监听聚合变化
const clusterChanged = debounce((clusters) => {
	console.log('聚合变化：', clusters);
	// 销毁旧聚合簇生成的覆盖物
	if (clusterBubbleList.length) {
		clusterBubbleList.forEach((item) => item.destroy());
		clusterBubbleList = [];
	}
	if (clusterPointList.length) {
		// 移除不存在点位
		const sum = [];
		clusterPointList.forEach((item) => {
			const notNew =
				clusters.findIndex(
					(v) => v.geometries.length == 1 && v.geometries[0].id == item.id
				) == -1;
			const notLast = item.id != lastPoint.value?.id;
			if (notNew && notLast) {
				item.destroy();
			} else {
				sum.push(item);
			}
		});
		clusterPointList = sum;
	}
	
	// 根据新的聚合簇数组生成新的覆盖物和点标记图层
	clusters.forEach(function (item) {
		if (item.geometries.length > 1) {
			// 聚合气泡
			// 创建聚合气泡
			let clusterBubble = new ClusterBubble({
				map,
				position: item.center,
				content: item.geometries.length,
			});
			// 气泡点击事件已禁用
			// clusterBubble.on("click", () => {
			// 	isClickPoint = true;
			// 	map.fitBounds(item.bounds);
			// });
			clusterBubbleList.push(clusterBubble);
		} else {
			// 聚合点位
			// 添加新点位
			if (
				clusterPointList.findIndex((v) => v.id == item.geometries[0].id) == -1
			) {
				let clusterPoint = new Point({
					map,
					...item.geometries[0],
				});
				// 移除点位点击事件绑定
				// clusterPoint.on("click", pointClick);
				clusterPointList.push(clusterPoint);
			}
		}
	});
}, 200);

// 设置点位列表
const setPointList = (list, id, notUpdateView, keepCurrentView = true) => {
	pointList = list;
	console.log('设置点位列表：', pointList);
	
	// 激活点位
	let centerPoint = null;
	// 点位数组
	const paintArr = list.map((item) => {
		const obj = {
			id: item.id,
			type: item.pointType,
			content: item.pointName,
			position: new TMap.LatLng(item.latitude, item.longitude),
			details: item,
		};
		if (obj.id == id) centerPoint = obj.position;
		return obj;
	});
	
	// 地图自适应
	if (id) {
		// 拦截已激活点位列表渲染
		if (lastPoint.value?.id == id) return map.panTo(lastPoint.value.position);
		// 拦截已存在点位列表渲染
		for (const item of clusterPointList) {
			if (item.id == id) {
				map.panTo(item.position);
				item.onClick();
				return;
			}
		}
		// 携带点位弹窗
		map.easeTo({
			center: centerPoint,
			zoom: 18,
		});
	} else if (!keepCurrentView) {
		// 只有在明确指定不保持当前视图时才自适应
		self(paintArr);
	}
	
	// 关闭弹窗
	closePointPop();
	// 设置聚合点
	markerCluster.setGeometries(paintArr);
};

// 地图自适应点位范围
const self = (arr) => {
	const bounds = new TMap.LatLngBounds();
	// 扩大 bounds 范围
	arr.forEach(({ position }) => {
		bounds.extend(position);
	});
	// 设置地图可视范围
	map.fitBounds(bounds, {
		padding: { top: 100, bottom: 100, left: 50, right: 50 },
		maxZoom: 18,
	});
};

// 初始化地图（执行同步任务）
const initMap = async () => {
	try {
		loading.value = true;
		console.log('初始化地图，获取景区数据，scenicId:', props.guideId);
		
		// 销毁已存在的地图实例
		if (map) {
			map.destroy();
			map = null;
		}
		
		// 确保有有效的 guideId
		if (!props.guideId) {
			console.error('缺少有效的 guideId');
			loading.value = false;
			return;
		}

		// 获取景区数据 - 修改请求参数为 scenicId
		const { data, code } = await request.get(`/scenic/address/info/${props.guideId}`);
		
		console.log('获取到景区数据：', data, '状态码：', code);
		
		// 如果没有获取到数据，直接创建一个空地图
		if (!data || !data.latitude || !data.longitude) {
			console.error('获取景区数据失败或数据不完整：', data);
			// 创建默认地图
			map = new TMap.Map("container", {
				center: new TMap.LatLng(39.908823, 116.397470), // 默认中心点坐标 (北京)
				viewMode: "2D",
				zoom: 12,
				showControl: false,
			});
			loading.value = false;
			return;
		}
		
		const { latitude, longitude, scenicRange, scenicName: title } = data;
		console.log('景区边界数据：', scenicRange);
		console.log('景区坐标：', latitude, longitude);
		
		// 创建中心点坐标（景区）
		center = new TMap.LatLng(latitude, longitude);
		
		// 检查地图 API 是否可用
		if (!window.TMap) {
			console.error('TMap API 未加载');
			loading.value = false;
			return;
		}
		
		// 创建地图
		map = new TMap.Map("container", {
			center, // 中心点坐标
			viewMode: "2D", // 视图模式
			showControl: false, // 控件
			zoom: 15, // 默认缩放级别
		});
		
		// 安全处理景区范围数据
		console.log('景区范围数据：', scenicRange);
		if (scenicRange) {
			try {
				// 创建地图范围（景区）
				const paths = [];
				bounds = new TMap.LatLngBounds();
				
				// 解析并处理边界数据
				let rangeData;
				try {
					rangeData = JSON.parse(scenicRange);
					console.log('解析后的边界数据：', rangeData);
				} catch (parseError) {
					console.error('边界数据解析失败：', parseError);
					// 尝试不解析，直接使用
					if (Array.isArray(scenicRange)) {
						rangeData = scenicRange;
						console.log('使用原始边界数据：', rangeData);
					} else {
						throw parseError;
					}
				}
				
				if (Array.isArray(rangeData) && rangeData.length > 0) {
					rangeData.forEach((item) => {
						if (Array.isArray(item) && item.length >= 2) {
							const latLng = new TMap.LatLng(item[0], item[1]);
							paths.push(latLng);
							bounds.extend(latLng);
						}
					});
					
					console.log('有效边界点数：', paths.length);
					
					// 只有当 paths 有效时才设置区域高亮和描边
					if (paths.length > 2) {  // 至少需要 3 个点才能构成多边形
						console.log('绘制景区边界，点数：', paths.length);
						
						// 区域高亮
						try {
							map.enableAreaHighlight({ 
								paths, 
								shadeColor: "rgba(41,91,255,0.08)"
							});
							console.log('区域高亮设置成功');
						} catch (highlightError) {
							console.error('设置区域高亮失败：', highlightError);
						}
						
						// 区域描边
						try {
							new TMap.MultiPolygon({
								map,
								styles: {
									polygon: new TMap.PolygonStyle({
										color: "rgba(0,0,0,0)",
										showBorder: true,
										borderColor: "rgba(41,91,255,1)",
										borderWidth: 2,
										borderDashArray: [10, 5],
									}),
								},
								geometries: [
									{
										id: "polygon",
										styleId: "polygon",
										paths,
									},
								],
							});
							console.log('区域描边设置成功');
						} catch (polygonError) {
							console.error('创建多边形失败：', polygonError);
						}
						
						// 自适应显示区域
						try {
							map.fitBounds(bounds, { padding: 50 });
							console.log('地图自适应边界设置成功');
						} catch (fitError) {
							console.error('设置地图自适应边界失败：', fitError);
						}
					} else {
						console.warn('景区边界点数不足，无法绘制多边形');
					}
				} else {
					console.warn('景区边界数据格式不正确或为空：', rangeData);
				}
			} catch (e) {
				console.error('处理景区边界数据失败：', e);
			}
		} else {
			console.warn('没有景区边界数据');
			
			// 即使没有边界数据，也要适当缩放地图
			map.setZoom(15);
			map.panTo(center);
		}
		
		// 初始化位置监听
		await initLocationListener();
		
		// 初始化点聚合功能
		initPointCluster();
		
		// 获取景区点位数据
		fetchScenicPoints();
		
		loading.value = false;
	} catch (error) {
		console.error('初始化地图出错：', error);
		// 创建默认地图作为备选
		try {
			map = new TMap.Map("container", {
				center: new TMap.LatLng(39.908823, 116.397470), // 默认中心点坐标 (北京)
				viewMode: "2D",
				zoom: 12,
				showControl: false,
			});
		} catch (mapError) {
			console.error('创建默认地图失败：', mapError);
		}
		loading.value = false;
	}
};

// 初始化位置监听
const initLocationListener = () => {
	return new Promise((resolve, reject) => {
		// 初始化位置
		if (window.navFrom) {
			console.log('已有位置信息：', window.navFrom);
			resolve();
		} else {
			console.log('尝试获取位置信息');
			uni.getLocation({
				type: "gcj02",
				isHighAccuracy: true, // 高精度
				highAccuracyExpireTime: 5000, // 超时
				success: (res) => {
					console.log('高精度定位成功：', res);
					window.navFrom = [res.latitude, res.longitude];
					resolve();
				},
				fail: (err) => {
					console.error('高精度定位失败：', err);
					// 使用默认位置或景区中心
					if (center) {
						window.navFrom = [center.lat, center.lng];
					}
					resolve(); // 即使失败也继续
				},
			});
		}
		
		// 更新位置监听
		uni.onLocationChange((res) => {
			console.log('位置变化：', res);
			window.navFrom = [res.latitude, res.longitude];
			// 更新点位距离
			updatePointDistances();
		});
		
		// 开启位置监听
		uni.startLocationUpdate({
			type: "gcj02",
			success: (res) => {
				console.log('开启位置监听成功', res);
			},
			fail: (err) => {
				console.error('开启位置监听失败：', err);
			},
		});
	});
};

// 更新点位距离
const updatePointDistances = () => {
	if (pointList.length && window.navFrom) {
		pointList.forEach(item => {
			item.distance = pointDistance([item.latitude, item.longitude]);
		});
		
		// 如果有当前选中的点位，更新其距离显示
		if (lastPoint.value) {
			lastPoint.value.details.distance = pointDistance([
				lastPoint.value.details.latitude,
				lastPoint.value.details.longitude,
			]);
		}
	}
};

// 初始化点聚合功能
const initPointCluster = () => {
	// 销毁已存在的聚合对象，避免重复创建
	if (markerCluster) {
		markerCluster.setMap(null);
		markerCluster = null;
	}
	
	// 创建点聚合对象
	markerCluster = new TMap.MarkerCluster({
		id: "cluster", // 图层 id
		map, // 设置点聚合显示在哪个 map 对象中
		enableDefaultStyle: false, // 使用默认样式
		minimumClusterSize: 2, // 最小聚合点数：2 个
		geometries: [],
		zoomOnClick: true, // 点击聚合数字放大展开
		averageCenter: true, // 每个聚和簇的中心是否应该是聚类中所有标记的平均值
		maxZoom: 17, // 采用聚合策略的最大缩放级别，若地图缩放级别大于该值，则不进行聚合，标点将全部被展开
	});
	
	// 监听聚合簇变化
	markerCluster.on("cluster_changed", () =>
		clusterChanged(markerCluster.getClusters())
	);
	
	// 地图点击事件
	map.on("click", closePointPop);
};

// 获取景区点位数据
const fetchScenicPoints = async () => {
	try {
		console.log('开始获取景区点位数据，景区 ID:', props.guideId);
		
		// 使用与 tour.vue 相同的接口参数
		const { data } = await request.get("/navigation/point/list", { 
			scenicId: props.guideId,
			sort: 1
		});
		
		console.log('获取到景区点位数据：', data);
		
		if (Array.isArray(data)) {
			// 处理点位数据
			const points = data.map(item => {
				// 计算距离
				if (window.navFrom) {
					item.distance = pointDistance([item.latitude, item.longitude]);
				} else {
					item.distance = 0;
				}
				return item;
			});
			
			// 设置点位，保持当前视图
			setPointList(points, null, false, true);
		} else {
			console.warn('景区点位数据格式不符合预期：', data);
		}
	} catch (error) {
		console.error('获取景区点位数据失败：', error);
		
		// 尝试获取所有点位类型的数据
		try {
			console.log('尝试获取点位类型列表');
			const { data: typeList } = await request.get("/navigation/point/type/list", { 
				scenicId: props.guideId 
			});
			
			if (Array.isArray(typeList) && typeList.length > 0) {
				console.log('获取到点位类型列表：', typeList);
				const firstType = typeList.sort((a, b) => a - b)[0];
				
				console.log('尝试获取类型为', firstType, '的点位数据');
				const { data } = await request.get("/navigation/point/list", { 
					pointType: firstType,
					scenicId: props.guideId,
					sort: 1
				});
				
				console.log('获取到特定类型的点位数据：', data);
				
				if (Array.isArray(data)) {
					const points = data.map(item => {
						if (window.navFrom) {
							item.distance = pointDistance([item.latitude, item.longitude]);
						} else {
							item.distance = 0;
						}
						return item;
					});
					
					// 设置点位，保持当前视图
					setPointList(points, null, false, true);
				}
			}
		} catch (backupError) {
			console.error('备用方法也失败了：', backupError);
		}
	}
};

// 加载地图 API 完成
const loadMap = () => {
	console.log('地图 API 加载完成，开始初始化地图');
	// 清理全局变量
	markerCluster = null;
	clusterBubbleList = [];
	clusterPointList = [];
	lastPoint.value = null;
	
	// 加载自定义点位类
	window.ClusterBubble || import("./clusterBubble.js");
	window.Point || import("./clusterPoint.js");
	initMap();
};

// 加载地图 API
const loadScript = () => {
	console.log('开始加载地图 API');
	window.loadMap = loadMap;
	const script = document.createElement("script");
	script.type = "text/javascript";
	script.src =
		"https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=geometry&callback=loadMap";
	script.onerror = () => {
		console.error('地图 API 加载失败');
		loading.value = false;
	};
	document.body.appendChild(script);
};

// 暴露给父组件的方法
defineExpose({
	/**
	 * 设置点位列表
	 * @param {Array} list - 点位列表数据
	 * @param {String|null} id - 要聚焦的点位 ID，如果为 null 则不聚焦
	 * @param {Boolean} notUpdateView - 是否不更新视图
	 * @param {Boolean} keepCurrentView - 是否保持当前视图，默认为 true
	 */
	setPointList
});

// 生命周期（dom 加载）
onMounted(() => {
	console.log('组件挂载，准备初始化地图');
	if (window.TMap) {
		console.log('TMap 已存在，直接初始化');
		loadMap();
	} else {
		console.log('TMap 不存在，加载脚本');
		loadScript();
	}
});

// 页面显示时重新初始化地图
onShow(() => {
	console.log('页面显示，检查是否需要重新初始化地图');
	if (!map) {
		console.log('地图不存在，重新初始化');
		if (window.TMap) {
			loadMap();
		} else {
			loadScript();
		}
	} else {
		// 地图存在但可能需要重新初始化聚合点
		if (markerCluster) {
			// 重新初始化聚合点以避免 ID 冲突
			initPointCluster();
			// 如果有点位数据，重新设置
			if (pointList.length > 0) {
				setPointList(pointList, null, false, true);
			}
		}
	}
});

// 生命周期（页面卸载）
onUnload(() => {
	console.log('页面卸载，销毁地图');
	// 停止位置监听
	uni.stopLocationUpdate();
	// 销毁地图
	if (map) {
		map.destroy();
		map = null;
	}
});

// 当页面隐藏时不销毁地图，只在完全卸载时销毁
onHide(() => {
	console.log('页面隐藏，保留地图实例');
	// 不再销毁地图，只停止位置更新
	uni.stopLocationUpdate();
});
</script>

<style lang="scss" scoped>
.scenic-map {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: calc(100vh - 400rpx); /* 调整高度计算方式，减少减去的高度 */
	z-index: 1; /* 提高 z-index 确保显示 */
	#container {
		width: 100%;
		height: 100%;
	}
	.loading-indicator {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba(0, 0, 0, 0.6);
		color: white;
		padding: 20rpx 30rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	.painted,
	.painted * {
		pointer-events: painted;
	}
	
	.point_pop {
		position: absolute;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 690rpx;
		background: #fff;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		transition: 0.3s;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		opacity: 0;
		
		&.point_show {
			opacity: 1;
		}
		
		.point_pop--top {
			display: flex;
			justify-content: space-between;
			.point_pop--top--left {
				flex: 1;
				width: 0;
				padding: 45rpx 30rpx 35rpx;
				display: flex;
				flex-direction: column;
				.title {
					font-size: 36rpx;
					font-weight: bold;
					color: #090909;
					line-height: 36rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.distance {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #535353;
					line-height: 33rpx;
				}
				.content {
					height: 111rpx;
					margin-top: 13rpx;
					font-size: 26rpx;
					color: #535353;
					line-height: 37rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 3;
					-webkit-box-orient: vertical;
				}
			}
		}
		.point_pop--bottom {
			height: 88rpx;
			border-top: 1rpx solid rgba(151, 151, 151, 0.23);
			display: flex;
			align-items: center;
			> view {
				font-size: 30rpx;
			}
			.point_pop--bottom--left {
				flex: 1;
				height: 100%;
				line-height: 87rpx;
				text-align: center;
				color: #14131f;
			}
			.point_pop--bottom--center {
				width: 1rpx;
				height: 58rpx;
				background-color: rgba(151, 151, 151, 0.23);
			}
			.point_pop--bottom--right {
				flex: 1;
				height: 100%;
				line-height: 87rpx;
				text-align: center;
				color: #349fff;
			}
		}
	}
}
</style>

<style>
/* 点位和聚合气泡样式 */
.clusterPoint {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	transform-origin: center bottom;
	transition: all 0.2s;
}

.clusterPoint .logo_box {
	width: 60rpx;
	height: 60rpx;
	background: #fff;
	border-radius: 50%;
	box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
}

.clusterPoint .logo_box div {
	width: 40rpx;
	height: 40rpx;
	position: relative;
}

.clusterPoint .logo_box div span {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
}

.clusterPoint .logo_box div img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
}

.clusterPoint > span {
	margin-top: -5rpx;
	padding: 0 10rpx;
	height: 34rpx;
	background: #fff;
	box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.5);
	border-radius: 17rpx;
	font-size: 22rpx;
	color: #14131f;
	line-height: 34rpx;
	white-space: nowrap;
}

.clusterPoint.pointBig {
	z-index: 9;
	transform: scale(1.2);
}

.clusterBubble {
	position: absolute;
	transform-origin: center bottom;
}

.clusterBubble .logo_box {
	width: 60rpx;
	height: 60rpx;
	background: #349fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.clusterBubble .logo_box span {
	font-size: 24rpx;
	font-weight: bold;
	color: #fff;
	text-align: center;
}
</style>
