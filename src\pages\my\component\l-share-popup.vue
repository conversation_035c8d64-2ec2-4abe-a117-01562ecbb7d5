<template>
	<uni-popup ref="popup" type="center" :is-mask-click="true" @maskClick="closePopup">
		<view class="popup-container">
			<view class="close-wrapper" @click="closePopup">
				<image class="close-icon" src="@/static/svg/close-icon.svg"></image>
			</view>
			<view class="popup-content">
				<view class="share-card-container" id="poster-container">
					<y-base64-img v-if="shareData.footprintImage" :src="shareData.footprintImage" class="share-bg-image"
						mode="aspectFill" @base64-success="handleBgImageBase64Success"
						@base64-error="handleBgImageBase64Error" />
					<view class="share-card">
						<view class="summary-background">
							<view class="header">
								<image class="header-icon" src="@/static/image/check-in/share-title-side.webp" />
								<text class="header-title">我的打卡足迹</text>
								<image class="header-icon" src="@/static/image/check-in/share-title-side.webp" />
							</view>
							<view class="footprint-summary">
								<view class="user-info">
									<y-base64-img :src="shareData.userInfo.avatar" :img-host="imgHost"
										class="user-avatar" mode="aspectFill"
										@base64-success="handleAvatarBase64Success"
										@base64-error="handleAvatarBase64Error" />
									<text class="user-name">{{ shareData.userInfo.nickname }}</text>
								</view>
								<view class="summary-text">
									<text>你的打卡分布在</text>
									<text class="highlight">&nbsp;{{ shareData.footprintData.cityCount }}&nbsp;</text>
									<text>个城市</text>
								</view>
								<view class="summary-text">
									<text>共在</text>
									<text class="highlight">&nbsp;{{ shareData.footprintData.placeCount }}&nbsp;</text>
									<text>个地点打卡</text>
									<text class="highlight">&nbsp;{{ shareData.footprintData.checkInCount
									}}&nbsp;</text>
									<text>次</text>
								</view>
							</view>
						</view>

						<view class="footer">
							<image class="footer-logo" src="@/static/yilvbao_logo.png" />
							<text class="qr-prompt">
								长按识别二维码
								<br />
								开启你的旅程
							</text>
							<qrcode-vue class="qr-code" :value="url" :size="50" level="M" />
						</view>
					</view>
				</view>
				<view :class="['save-button-wrapper', { 'disabled': !canSave }]" @click="handleSaveImage">
					<text class="save-button-text">{{ saveButtonText }}</text>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
import { ref, onMounted, computed, defineProps, nextTick, watch } from 'vue';
import html2canvas from 'html2canvas';
import QrcodeVue from 'qrcode.vue';
import YBase64Img from '@/components/y-base64-img/y-base64-img.vue';
import { Tool } from '@/utils/tools.ts';

const imgHost = ref(getEnv().VITE_IMG_HOST)

// 定义 props
const props = defineProps({
	shareData: {
		type: Object,
		default: () => ({
			title: '',
			description: '',
			footprintImage: '',
			url: '',
			footprintData: {
				cityCount: 0,
				placeCount: 0,
				checkInCount: 0,
			},
			userInfo: {
				avatar: '',
				nickname: '游客'
			},
		})
	}
});

// 定义 emits
const emit = defineEmits(['close']);

// 弹窗引用
const popup = ref(null);

// 弹窗开关状态
const isPopupOpen = ref(false);

// 图片转换状态
const avatarConverted = ref(false);
const bgImageConverted = ref(false);
// 图片 DOM 替换状态
const imageReplaced = ref(false);

// 计算属性：所有图片是否准备好
const allImagesReady = computed(() => {
	// 如果没有图片，则认为已转换
	const avatarStatus = props.shareData.userInfo.avatar ? avatarConverted.value : true;
	const bgImageStatus = props.shareData.footprintImage ? bgImageConverted.value : true;

	// 只有在图片转换完成后才能进行 DOM 替换
	return avatarStatus && bgImageStatus;
});


// 计算属性：是否可以保存
const canSave = computed(() => {
	// 只有在图片转换完成且 DOM 替换完成后才能保存
	return allImagesReady.value && imageReplaced.value;
});

// 保存按钮文本
const saveButtonText = computed(() => {
	if (!allImagesReady.value) {
		uni.showLoading({ title: '初始化中' });	
		return '初始化中';
	}
	return imageReplaced.value ? '长按图片进行保存' : '保存图片';
});

// 处理头像转 base64 成功
const handleAvatarBase64Success = (data) => {
	console.log('头像转换成功', data);
	avatarConverted.value = true;
};

// 处理头像转 base64 失败
const handleAvatarBase64Error = (data) => {
	console.log('头像转换失败', data);
	// 即使失败也设为 true，避免用户无法保存
	avatarConverted.value = true;
};

// 处理背景图转 base64 成功
const handleBgImageBase64Success = (data) => {
	console.log('背景图转换成功', data);
	bgImageConverted.value = true;
};

// 处理背景图转 base64 失败
const handleBgImageBase64Error = (data) => {
	console.log('背景图转换失败', data);
	// 即使失败也设为 true，避免用户无法保存
	bgImageConverted.value = true;
};

// 自动转换图片为 DOM 元素
const convertToImageElement = async () => {
	try {
		const element = document.querySelector('#poster-container');
		if (!element) {
			console.error('找不到分享卡片元素');
			return;
		}

		// 获取原始容器的样式
		const elementStyles = window.getComputedStyle(element);

		// 使用 html2canvas 转换为图片
		const canvas = await html2canvas(element, {
			useCORS: true,
			allowTaint: true,
			backgroundColor: '#FFFFFF',
			scale: window.devicePixelRatio * 2,
		});

		// 创建图片元素
		const imgElement = document.createElement('img');
		imgElement.src = canvas.toDataURL('image/png');
		imgElement.id = 'poster-container'; // 保持原有的 ID

		// 复制原容器的所有样式
		imgElement.style.backgroundColor = elementStyles.backgroundColor;
		// imgElement.style.padding = elementStyles.padding;
		imgElement.style.borderRadius = elementStyles.borderRadius;
		imgElement.style.width = elementStyles.width;
		imgElement.style.height = elementStyles.height;
		imgElement.style.display = elementStyles.display;
		imgElement.style.boxSizing = elementStyles.boxSizing;

		// 确保图片完全填充
		imgElement.style.objectFit = 'fill';
		imgElement.style.objectPosition = 'center';

		// 替换整个 poster-container 元素
		element.parentNode.replaceChild(imgElement, element);
		imageReplaced.value = true;
		console.log('图片 DOM 替换成功');
		uni.hideLoading();
	} catch (error) {
		console.error('图片转换失败：', error);
		// 即使失败也设置为 true，避免用户无法保存
		imageReplaced.value = true;
	}
};

// 监听图片是否准备完成，完成后执行截图
watch(allImagesReady, async (ready) => {
	// 当图片准备好、弹窗已打开且 DOM 未被替换时，执行转换
	if (ready && isPopupOpen.value && !imageReplaced.value) {
		// 等待 DOM 更新
		await nextTick();
		// 添加一个小的延迟，以确保万无一失
		setTimeout(async () => {
			await convertToImageElement();
		}, 100);
	}
});


// 打开弹窗
const openPopup = async () => {
	// 重置转换状态
	avatarConverted.value = false;
	bgImageConverted.value = false;
	imageReplaced.value = false;
	isPopupOpen.value = true;

	if (popup.value) {
		popup.value.open();
	}
};

// 关闭弹窗
const closePopup = () => {
	isPopupOpen.value = false;
	if (popup.value) {
		popup.value.close();
	}
	emit('close');
};

const handleSaveImage = () => {
	// 如果不能保存，则直接返回
	if (!canSave.value) {
		if (!allImagesReady.value) {
			uni.showToast({
				title: '图片初始化中，请稍候...',
				icon: 'none'
			});
		}
		return;
	}

	// H5 环境
	// #ifdef H5
	// 如果图片已经替换为 DOM，提示用户长按保存
	if (imageReplaced.value) {
		uni.showToast({
			title: '请长按图片进行保存',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	const element = document.querySelector('#poster-container');
	if (!element) {
		uni.showToast({ title: '找不到分享卡片元素', icon: 'none' });
		return;
	}

	uni.showLoading({ title: '正在生成图片...' });

	html2canvas(element, {
		useCORS: true, // 允许跨域加载图片
		allowTaint: true,
		backgroundColor: '#FFFFFF', // 设置背景色，避免透明
		scale: window.devicePixelRatio * 2, // 提高清晰度
	}).then(canvas => {
		// 在 H5 中，我们可以直接创建一个链接来下载
		const link = document.createElement('a');
		link.download = '我的打卡足迹.png';
		link.href = canvas.toDataURL('image/png');
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		uni.hideLoading();
		uni.showToast({ title: '图片已保存', icon: 'success' });
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({ title: '图片生成失败', icon: 'none' });
		console.error('html2canvas error:', err);
	});
	// #endif

	// #ifndef H5
	uni.showToast({
		title: '当前环境不支持此功能，请在 H5 环境下体验',
		icon: 'none'
	});
	// #endif
}

const url = ref('');

onMounted(() => {
	// 获取 # 号前的链接部分
	const href = window.location.href.split('#')[0];
	const storeId = Tool.getRoute.params().storeId
	url.value = `${href}#/?storeId=${storeId}`;
	console.log('获取二维码链接 str')
	console.log(url.value)
});


// 暴露方法给父组件
defineExpose({
	open: openPopup,
	close: closePopup
});
</script>

<style lang="scss" scoped>
.popup-container {
	position: relative;
}

.close-wrapper {
	position: absolute;
	top: -80rpx;
	right: 0;
	display: flex;
	align-items: center;
	padding: 10rpx;
	z-index: 10;

	.close-icon {
		width: 48rpx;
		height: 48rpx;
	}
}

.popup-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.share-card-container {
	background-color: #fff;
	position: relative;
	width: 626rpx;
	height: 1148rpx;
}

.share-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	height: 100%;
	padding-bottom: 46rpx;
	box-sizing: border-box;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;

	.header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;

		.header-icon {
			width: 31rpx;
			height: 28rpx;
			margin: 0 11rpx;
		}

		.header-title {
			color: #14131f;
			font-size: 28rpx;
			letter-spacing: 0.25rpx;
			font-family: PingFangSC-Medium, sans-serif;
			font-weight: 500;
			white-space: nowrap;
		}
	}

	.footprint-summary {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		width: 100%;
		padding: 0 50rpx;
		box-sizing: border-box;
		margin-top: 34rpx;

		.user-info {
			display: flex;
			align-items: center;
			gap: 12rpx;

			.user-avatar {
				width: 42rpx;
				height: 42rpx;
				border-radius: 50%;
			}

			.user-name {
				color: #14131f;
				font-size: 28rpx;
				font-weight: 500;
			}
		}

		.summary-text {
			font-size: 28rpx;
			color: #14131f;
			font-weight: 500;
			line-height: 40rpx;
			margin-top: 11rpx;

			.highlight {
				font-size: 40rpx;
				font-family: Helvetica-Bold, sans-serif;
				font-weight: 700;
				color: #349fff;
			}

			&:last-child {
				margin-top: 20rpx;
				font-size: 24rpx;
				line-height: 33rpx;

				.highlight {
					font-size: 24rpx;
				}
			}
		}
	}

	.footer {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		width: 100%;
		padding: 50rpx 42rpx;
		box-sizing: border-box;
		position: absolute;
		bottom: 0;
		left: 0;

		background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 40%, #FFFFFF 100%);

		.footer-logo {
			width: 200rpx;
			height: 60rpx;
			margin-right: auto;
		}

		.qr-prompt {
			color: #14131F;
			font-size: 20rpx;
			text-align: right;
			line-height: 1.4;
			margin-right: 20rpx;
		}

		.qr-code {
			width: 88rpx;
			height: 88rpx;
		}
	}
}

.summary-background {
	width: 100%;
	height: 50%;
	background: linear-gradient(180deg, #F6F7FD 0%, rgba(241, 242, 245, 0.8) 30%, rgba(237, 237, 237, 0) 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 46rpx;
	box-sizing: border-box;
}

.share-bg-image {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	object-fit: cover;
}

.save-button-wrapper {
	margin-top: 40rpx;
	background-color: #349fff;
	border-radius: 40rpx;
	padding: 20rpx 93rpx;

	&.disabled {
		background-color: #ccc;
		opacity: 0.7;
	}
}

.save-button-text {
	color: #fff;
	font-size: 28rpx;
	font-family: PingFangSC-Medium,
		sans-serif;
	font-weight: 500;
	white-space: nowrap;
	line-height: 40rpx;
}
</style>
