<style lang="scss" scoped>
@keyframes spin {
	0% {
		transform: rotate(0);
	}

	100% {
		transform: rotate(360deg);
	}
}

.icon.spin-animation {
	animation: spin 1s linear infinite;
}

.location-list {
	// display: none;
	height: calc(100vh - 90rpx);
	background-color: #fff;
	border-top-left-radius: 60rpx;
	$transition-time: 0.2s;
	transition: all $transition-time;
	.shortcut {
		padding: 30rpx 40rpx;
		.title {
			display: flex;
			justify-content: space-between;
			margin-top: 10rpx;
			margin-bottom: 20rpx;

			.left {
				font-size: 30rpx;
				font-weight: 500;
				color: #0d0d0d;
			}

			.right {
				font-size: 24rpx;
				font-weight: 400;
				color: #1c78e9;
			}
		}

		.shortcut-box {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -17rpx;

			.city-box {
				flex: none;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 200rpx;
				height: 70rpx;
				margin: 0 17rpx 30rpx;
				font-size: 30rpx;
				color: #0d0d0d;
				background-color: #f4f4f4;
				border-radius: 12rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				min-width: 0;
				.icon {
					width: 32rpx;
					height: 32rpx;
					flex: none;
					margin-right:8rpx;
				}
				.city-name{
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}

	.city-group {
		padding-right: 20rpx;

		.letter {
			display: flex;
			align-items: center;
			margin: 0 -40rpx;
			padding-left: 40rpx;
			height: 50rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #0d0d0d;
			background-color: #f4f4f4;
		}

		.city-item {
			display: flex;
			align-items: center;
			height: 94rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #040404;

			&:not(:last-child) {
				border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);
			}
		}
	}
}

.indexed-list {
	position: fixed;
	right: 30rpx;
	bottom: 50%;
	transform: translateY(50%);
	z-index: 200;
	line-height: 37rpx;
	font-size: 26rpx;
	font-weight: 500;
	color: #7ed3da;
	transition: 0.2s;
}

.active-letter {
	color: #1c78e9;
	font-weight: bold;
}

.search-box {
	position: relative;
	top: 0;
	padding: 14rpx 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
	.search-input {
		margin-left: 20rpx;
		flex: 1;
		display: flex;
		align-items: center;
		// width: 500rpx;
		height: 60rpx;
		padding: 0 23rpx;
		border-radius: 45rpx;
		background: #f0f0f0;

		.input {
			color: #333;
			caret-color: #999;
		}

		.icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50rpx;
			height: 50rpx;
		}

		.placeholder-style {
			font-size: 28rpx;
			color: #999999;
			margin-left: 8rpx;
		}
	}
	.srarch-cancel {
		font-size: 28rpx;
		margin-left: 35rpx;
		color: #000;
	}
}
</style>

<template>
	<view class="search-box">
		<view class="search-input">
			<uni-icons class="icon" type="search" size="25" color="#cacaca"></uni-icons>
			<view class="input-wrapper" style="display: flex; flex: 1; align-items: center;">
				<input class="input" :focus="isInputFocus" @blur="isInputFocus = false" confirm-type="search"
					v-model="inputSearch" @confirm="onSearch" @input="onSearch" placeholder-class="placeholder-style"
					placeholder="查找地点" style="flex: 1;" />
				<uni-icons v-if="inputSearch" class="clear-icon" type="clear" size="20" color="#cacaca"
					@click="onClearInput"></uni-icons>
			</view>
		</view>
		<view @click="Tool.goPage.back()" class="srarch-cancel">取消</view>
	</view>
	<template v-if="!isFilter">
		<scroll-view scroll-y="true" scroll-with-animation="true" :scroll-into-view="activeLetter"
			class="location-list">
			<view class="shortcut">
				<view class="title">
					<text class="left">当前定位</text>
				</view>
				<view class="shortcut-box">
					<view class="city-box" @click="onChangeCity(locationInfo)">
						<uni-icons v-if="locationStatus === 'waiting'" class="icon spin-animation" type="spinner-cycle"
							size="22"></uni-icons>
						<image v-else class="icon" src="@/static/image/check-in/dingwei-cion.webp" mode=""></image>
						<text class="city-name">{{ locationInfo.cityName }}</text>
					</view>
				</view>
				<view class="title">
					<text class="left">热门城市</text>
				</view>
				<view class="shortcut-box">
					<view class="city-box" v-for="(item, index) in hotCity" :key="index" @click="onChangeCity(item)">
						{{ item.cityName }}</view>
				</view>
				<view class="city-group" v-for="(list, letter, i) in cityList" :key="i">
					<view class="letter" :id="letter">{{ letter.toUpperCase() }}</view>
					<template v-for="(item, index) in list.data" :key="index">
						<view class="city-item" @click="onChangeCity(item)">{{
							item.cityName
							}}</view>
					</template>
				</view>
			</view>
		</scroll-view>
	</template>
	<template v-if="isFilter">
		<view class="location-list" style="padding: 0rpx 40rpx;">
			<view class="city-group">
				<template v-for="(item, index) in filterList" :key="index">
					<view class="city-item" @click="onChangeCity(item)">
						<rich-text :nodes="highlight(item.cityName)"></rich-text>
					</view>
				</template>
			</view>
		</view>
	</template>
	<view class="indexed-list" @touchstart.stop.prevent="onTouchStart" @touchmove.stop.prevent="onTouchMove"
		@touchend.stop.prevent="onTouchEnd" v-if="!isFilter && cityLetter.length">
		<view v-for="(item, index) in cityLetter" :key="index" @click.stop="onLetterClick(item)"
			:class="{ 'active-letter': activeLetter === item }">
			{{ item.toUpperCase() }}
		</view>
	</view>
</template>
<script setup>
import { reactive, ref, onBeforeMount, watch, computed, getCurrentInstance, nextTick } from "vue"
import request from "@/utils/request.js"
import useLocation from "@/hooks/useLocation"
import { Tool } from "@/utils/tools"
const { locationInfo, locationStatus, locationInit } = useLocation({
	geocoder: true
})
//热门城市
const hotCity = reactive([
	{
		cityName: "全国",
		cityCode: ""
	},
	{
		cityName: "香港",
		cityCode: 810100
	},
	{
		cityName: "北京市",
		cityCode: 110100
	},
	{
		cityName: "上海市",
		cityCode: 310100
	},
	{
		cityName: "广州市",
		cityCode: 440100
	},
	{
		cityName: "杭州市",
		cityCode: 330100
	},
	{
		cityName: "厦门市",
		cityCode: 350200
	},
	{
		cityName: "武汉市",
		cityCode: 420100
	},
	{
		cityName: "长沙市",
		cityCode: 430100
	},
	{
		cityName: "重庆市",
		cityCode: 500100
	},
	{
		cityName: "深圳市",
		cityCode: 440300
	}
])
const isFilter = ref(false)
const filterList = ref([])
const inputSearch = ref("")
const isInputFocus = ref(false)

const props = defineProps({
	//显示隐藏
	modelValue: {
		type: Boolean,
		default: false
	},
	//选中的城市
	city: {
		type: Object,
		default: () => ({})
	},
	cityKey: {
		type: String,
		default: ""
	}
})
const emits = defineEmits(["update:modelValue", "update:city"])

const cityList = ref([])
const activeLetter = ref("")

const instance = getCurrentInstance()
const touchState = reactive({
	isTouching: false,
	itemHeight: 0,
	listTop: 0,
})

const cityLetter = computed(() => {
	return Object.keys(cityList.value)
})

watch(
	() => cityList.value,
	() => {
		if (cityLetter.value.length === 0) return
		nextTick(() => {
			const query = uni.createSelectorQuery().in(instance)
			query
				.select(".indexed-list")
				.boundingClientRect((rect) => {
					if (rect) {
						touchState.itemHeight = rect.height / cityLetter.value.length
						touchState.listTop = rect.top
					}
				})
				.exec()
		})
	},
	{ deep: true }
)

//选择城市
const onChangeCity = city => {
	const cityParams = {
		cityName: city.cityName,
		cityCode: city.cityCode
	}
	uni.setStorageSync("selectedCity", JSON.stringify(cityParams))
	Tool.goPage.back()
}

// 索引事件
const onTouchLetter = (e) => {
	const touchY = e.touches[0].clientY
	const index = Math.floor((touchY - touchState.listTop) / touchState.itemHeight)
	if (index >= 0 && index < cityLetter.value.length) {
		const letter = cityLetter.value[index]
		if (letter !== activeLetter.value) {
			activeLetter.value = letter
		}
	}
}

const onTouchStart = (e) => {
	touchState.isTouching = true
	onTouchLetter(e)
}

const onTouchMove = (e) => {
	if (!touchState.isTouching) return
	onTouchLetter(e)
}

const onTouchEnd = () => {
	touchState.isTouching = false
}

const onLetterClick = (letter) => {
	activeLetter.value = letter
}

onBeforeMount(async () => {
	try {
		await getCityList()
		locationInit()
	} catch (e) {
		console.error(e)
		//TODO handle the exception
	}
})
const highlight = (name) => {
	const keyword = inputSearch.value
	if (keyword) {
		return name.replace(
			new RegExp(keyword, "g"),
			`<span style="color: #1c78e9;">${keyword}</span>`
		)
	}
	return name
}
const onClearInput = () => {
	inputSearch.value = ""
	isFilter.value = false
	filterList.value = []
	isInputFocus.value = false
	nextTick(() => {
		isInputFocus.value = true
	})
}
const onSearch = () => {
	if (inputSearch.value) {
		isFilter.value = true
		let resCity = []
		const city = cityList.value
		const val = inputSearch.value
		for (let key in city) {
			const list = city[key].data.filter(
				e => e.cityName.indexOf(val) !== -1
			)
			resCity = [...resCity, ...list]
		}
		filterList.value = resCity
	} else {
		isFilter.value = false
		filterList.value = []
	}
}
//获取城市
const getCityList = async () => {
	try {
		const { code, data } = await request.get(`/address/city`)
		// const {code1,data1} =await request.get(`/address/info`)
		const cityArr = data.value
		Object.keys(cityArr).forEach(key => {
			cityArr[key].data = cityArr[key].data.map(item => {
				return {
					cityName: item.addressName,
					cityCode: item.addressId,
					parentId: item.parentId
				}
			})
		})
		cityList.value = cityArr
		console.log(cityArr)
	} catch (err) {
		console.info(err)
	}
}
</script>
