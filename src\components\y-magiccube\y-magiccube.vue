<template>
	<view>
		<view style="overflow: hidden">
			<view v-if="conf.rubiksStyle" class="rubikes-cube" :style="{
				height: `${conf.rubiksStyle.height}`
				// margin: `-${conf.imgMargin / 2}`
			}">
				<view v-for="(item, index) in conf.rubiksStyle.list" :key="item.src" class="rubikes-cube-item" :style="{
					top: `${item.top}`,
					left: `${item.left}`,
					width: `${item.width}`,
					height: `${item.height}`,
					paddingTop: `${item.paddingTop}`,
					paddingLeft: `${item.paddingLeft}`,
					paddingRight: `${item.paddingRight}`,
					paddingBottom: `${item.paddingBottom}`,
					margin: `${item.margin}`
				}">
					<view :style="{
						width: '100%',
						height: '100%',
						borderRadius: `${conf.borderRadius}`,
						backgroundSize: 'cover',
						backgroundImage: `url(${item.src})`,
						backgroundPosition: 'center'
					}" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, toRefs, computed } from "vue"
import { pxToRpx } from "@/utils/tool.js"
const props = defineProps({
	config: {
		type: Object,
		default: () => ({
			rubiksStyle: {}
		})
	}
})

const conf = computed(() => {
	const style = {}
	const { imgMargin, pageMargin, rubiksStyle, borderRadius } = props.config
	// // 页面间距
	// if (pageMargin) style.pageMargin = `${pxToRpx(pageMargin)}`
	// // 图片间隙
	// if (imgMargin) style.imgMargin = `${pxToRpx(imgMargin)}`

	// 魔方样式
	if (rubiksStyle) {
		const { height, list } = rubiksStyle
		const boxWidth = 375
		style.rubiksStyle = {
			height: `${pxToRpx(height)}`,
			list: list?.map(item => {
				// 问题点：直接修改原始 item 对象
				// 使用对象展开创建新对象代替直接修改
				const newItem = { ...item }

				// 图片间距逻辑改为操作 newItem
				if (newItem.top === 0) {
					newItem.paddingTop = 0
				} else {
					newItem.paddingTop = pxToRpx(imgMargin / 2)
				}
				if (newItem.left === 0) {
					newItem.paddingLeft = 0
				} else {
					newItem.paddingLeft = pxToRpx(imgMargin / 2)
				}
				if (newItem.left + newItem.width === boxWidth) {
					newItem.paddingRight = 0
				} else {
					newItem.paddingRight = pxToRpx(imgMargin / 2)
				}
				if (newItem.top + newItem.height === rubiksStyle.height) {
					newItem.paddingBottom = 0
				} else {
					newItem.paddingBottom = pxToRpx(imgMargin / 2)
				}

				// 页面间隔
				if (newItem.left === 0) {
					newItem.paddingLeft = pxToRpx(pageMargin)
				}
				if (newItem.left + newItem.width === boxWidth) {
					newItem.paddingRight = pxToRpx(pageMargin)
				}

				return {
					...newItem, // 使用修改后的新对象
					top: `${pxToRpx(newItem.top)}`,
					left: `${pxToRpx(newItem.left)}`,
					width: `${pxToRpx(newItem.width)}`,
					height: `${pxToRpx(newItem.height)}`,
					src: newItem.src
				}
			})
		}
	}

	return {
		...props.config,
		...style,
		borderRadius: `${pxToRpx(borderRadius)}`
	}
})
</script>

<style lang="scss" scoped>
.rubikes-cube {
	// border: 1px solid rgba(0, 0, 0, 0.15);
	position: relative;

	.rubikes-cube-item {
		font-size: 14rpx;
		position: absolute;
		color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		background-size: cover;
	}
}
</style>
