import { ref, onMounted } from "vue"
import { getAddressByTx } from "@/utils/tool.js"

// 定位信息
const locationInfo = ref({
	addressName: "",
	addressId: "",
	latitude: "",
	longitude: "",
	cityName: "",
	cityCode: ""
})

/**
 *
 * @param {Boolean} geocoder 是否解析 ip 为实际地址
 * @returns
 */
export default function useLocation({ geocoder }) {
	// 定位状态 waiting | success | fail
	const locationStatus = ref("success")
	// 初始化
	const locationInit = async () => {
		locationStatus.value = "waiting"

		const getLocationPromise = new Promise<any>((resolve, reject) => {
			uni.getLocation({
				type: "wgs84",
				// highAccuracyExpireTime: -1,
				success: res => {
					resolve(res)
				},
				fail: err => {
					reject(err)
				}
			})
		})

		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => {
				reject(new Error("定位超时"))
			}, 15000)
		})

		try {
			const res = await Promise.race([getLocationPromise, timeoutPromise])

			console.log("res-----000", res)
			locationInfo.value.latitude = String(res.latitude)
			locationInfo.value.longitude = String(res.longitude)
			if (geocoder) {
				try {
					// 解析 ip 为实际地址
					const params = {
						lat: res.latitude,
						lng: res.longitude
					}
					const adData = await getAddressByTx(params)
					console.log("adData-----")
					console.log(adData)
					const addressInfo = adData.ad_info
					addressInfo.city_code =
						addressInfo.city_code.split("156").join("") * 1
					locationInfo.value.cityName = addressInfo.city || adData.address
					locationInfo.value.cityCode = addressInfo.city_code
				} catch (e) {
					//TODO handle the exception
					console.error("解析地址失败")
					console.error(e)
					locationInfo.value.cityName = "全国"
					locationInfo.value.cityCode = ""
				}
			}
			locationStatus.value = "success"
		} catch (err) {
			console.log("error:")
			console.log(err)
			locationStatus.value = "fail"
			locationInfo.value.cityName = "全国"
			locationInfo.value.cityCode = ""
		}
	}
	return { locationInfo, locationStatus, locationInit }
}
