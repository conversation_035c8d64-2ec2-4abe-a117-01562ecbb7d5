<template>
	<view>
		<view v-if="orderType === 'single'">
			<singlePage />
		</view>
		<view v-if="orderType === 'compose'">
			<composePage />
		</view>
		<view v-if="orderType === 'travel'">
			<singlePage />
		</view>
	</view>
</template>
<script setup>
import singlePage from './single.vue'
import composePage from './compose.vue'
const orderType = ref('')
onLoad(option => {
	orderType.value = option.orderType
	console.log('当前 orderType:', orderType.value)
})
</script>

<style lang="scss" scoped></style>
