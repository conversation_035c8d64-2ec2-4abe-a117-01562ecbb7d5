<template>
  <view class="swiper-main" @touchstart="touchstart" @touchend="touchend" @touchmove="touchmove">
    <view class="swiper-list">
      <template v-if="props.imgList.length">
        <view v-for="(item, index) in props.imgList" :key="item.img" class="swiper-item" :class="[statusList[index]]"
          @click="goLink(item)" :style="{ backgroundImage: `url(${item.img})` }"></view>
      </template>
      <view v-else class="swiper-item-default"></view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch, onUnmounted } from "vue";
import { goLink } from "@/utils/tool.js";

const props = defineProps({
  //图片列表
  imgList: {
    type: [],
    default: () => [],
  },
  //滑块自动切换时间间隔，ms
  interval: {
    type: Number,
    default: 3000,
  },
  // //滑块切换过程所需时间 ms
  // duration: {
  // 	type: Number,
  // 	default: 1000
  // }
});

const swiperStatus = reactive({
  one: "swiper-status-one",
  two: "swiper-status-two",
  three: "swiper-status-three",
  hidden: "swiper-status-hidden",
});

const statusList = ref([]);

watch(
  () => props.imgList,
  (newList) => {
    newList.forEach((e, i, arr) => {
      switch (i) {
        case 0:
          statusList.value.push(swiperStatus.one);
          break;
        case 1:
          statusList.value.push(swiperStatus.two);
          break;
        case 2:
          statusList.value.push(swiperStatus.three);
          break;
        default:
          statusList.value.push("");
      }
    });
  },
  { deep: true }
);

let timer = null;
const goSwiper = () => {
  timer = setTimeout(() => {
    let lastItem = statusList.value.pop();
    if (lastItem === "") lastItem = swiperStatus.hidden; //解决第一次就切最后一张的问题
    statusList.value = [lastItem, ...statusList.value];
    goSwiper();
  }, props.interval);
};
goSwiper();

onUnmounted(() => {
  clearTimeout(timer);
});
</script>

<style lang="scss" scoped>
.swiper-main {
  .swiper-list {
    position: relative;
    height: 422rpx;

    .swiper-item {
      position: absolute;
      // top:0;
      // height: 577rpx;
      // background-color: #7ED3DA;
      border-radius: 0 0 68rpx 68rpx;
      animation-fill-mode: forwards;
      animation-duration: 2s;
      animation-iteration-count: 1;
      transition-timing-function: ease-in;
      background-size: cover;
      overflow: hidden;
      transition: all 2s;
    }

    .swiper-item-default {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 577rpx;
      border-radius: 0 0 68rpx 68rpx;
      background-color: #7ed3da;
      z-index: 10;
      opacity: 1;
    }
  }
}

@keyframes swiper-hidden {
  100% {
    top: 400rpx;
    left: 30%;
    right: 30%;
    height: 100rpx;
    background-color: transparent;
    z-index: -1;
    opacity: 0;
  }

  0% {
    top: 0;
    left: 0;
    right: 0;
    height: 577rpx;
    background-color: #7ed3da;
    z-index: 10;
    opacity: 1;
  }
}

.swiper-status-hidden {
  animation-name: swiper-hidden;
}

@keyframes swiper-three {
  from {
    top: 200rpx;
    left: 30%;
    right: 30%;
    height: 100rpx;
    background-color: transparent;
    z-index: -1;
    opacity: 0;
  }

  to {
    top: 257rpx;
    left: 140rpx;
    right: 140rpx;
    height: 383rpx;
    opacity: 0.6;
    background-image: url("@/static/image/home/<USER>");
    z-index: 8;
  }
}

.swiper-status-three {
  animation-name: swiper-three;
}

@keyframes swiper-two {
  from {
    top: 257rpx;
    left: 140rpx;
    right: 140rpx;
    height: 383rpx;
    background-image: url("@/static/image/home/<USER>");
    opacity: 0.3;
    z-index: 8;
  }

  to {
    top: 169rpx;
    left: 62rpx;
    right: 62rpx;
    height: 442rpx;
    background-image: url("@/static/image/home/<USER>");
    z-index: 9;
    opacity: 0.5;
  }
}

.swiper-status-two {
  animation-name: swiper-two;
}

@keyframes swiper-one {
  from {
    top: 169rpx;
    left: 62rpx;
    right: 62rpx;
    height: 442rpx;
    z-index: 9;
    opacity: 0.5;
  }

  to {
    top: 0;
    left: 0;
    right: 0;
    height: 577rpx;
    z-index: 10;
    opacity: 1;
  }
}

.swiper-status-one {
  animation-name: swiper-one;
}
</style>
