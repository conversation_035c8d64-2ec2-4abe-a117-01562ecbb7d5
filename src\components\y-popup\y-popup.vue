<template>
	<view class="pop-bg" v-show="modelValue" @click="onCloseWindow"></view>
	<view class="pop-up" :class="[modelValue ? 'show' : 'hide']" @click.stop="">
		<slot name="header">
			<view class="header" :class="[type]">
				<view class="left">
					<y-font-weight>{{ title }}</y-font-weight>
				</view>
				<view class="right" @click="onCloseWindow">
					<view class="one"></view>
					<view class="two"></view>
				</view>
			</view>
		</slot>

		<scroll-view :scroll-top="0" scroll-y="true" class="scroll-Y" :class="[type]">
			<slot></slot>
		</scroll-view>
	</view>
</template>
<script>
import { toRefs, reactive, ref } from "vue"
export default {
	name: "pop-up",
	props: {
		modelValue: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: ""
		},
		type: {
			type: String,
			default: ""
		}
	},
	setup(props, context) {
		const onCloseWindow = e => {
			console.log(e)
			e.stopPropagation()
			context.emit("update:modelValue", false)
		}
		return {
			onCloseWindow,
			...toRefs(props)
		}
	}
}
</script>
<style lang="scss" scoped>
@use "sass:math";

.pop-bg {
	position: fixed;
	left: 0%;
	right: 0%;
	bottom: 0;
	top: 0%;
	background-color: rgba(5, 5, 5, 0.3);
	z-index: 100;
	transition: all $transition-time;
}

.pop-up {
	position: fixed;
	left: 0%;
	right: 0%;
	bottom: 0;
	// padding: 54rpx 40rpx;
	background-color: #fff;
	border-radius: 36rpx 36rpx 0rpx 0rpx;
	z-index: 101;
	transition: all $transition-time;

	&.show {
		top: 20%;
	}

	&.hide {
		top: 100%;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		// margin-bottom: 20rpx;
		padding: 44rpx 46rpx;
		border-radius: 36rpx 36rpx 0rpx 0rpx;

		&.reserve {
			background: linear-gradient(180deg, #e5fdff 0%, #ffffff 100%);
		}

		.left {
			font-size: 40rpx;
			font-weight: 500;
			color: #000000;
		}

		.right {
			position: relative;
			$w: 33rpx;
			width: $w;
			height: $w;
			$childW: 46.67rpx;
			$childH: 4rpx;
			$choldTranslate: ($w/2)-($childW/2),($w/2);

		.one {
			position: absolute;
			width: $childW;
			height: $childH;
			transform: translate($choldTranslate) rotate(45deg);
			background-color: #050505;
			border-radius: 2rpx;
		}

		.two {
			position: absolute;
			width: $childW;
			height: $childH;
			transform: translate($choldTranslate) rotate(-45deg);
			background-color: #050505;
			border-radius: 2rpx;
		}
	}
}
}

.scroll-Y {
	height: 100%;

	&.reserve {
		padding: 0 44rpx;
	}
}
</style>
