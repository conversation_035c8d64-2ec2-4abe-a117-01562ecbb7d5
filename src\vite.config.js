import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'  
export default defineConfig({
	// optimizeDeps: {
	// 	include: [
	// 		'vue',
	// 		'vue-router'
	// 	]
	// },
	plugins: [uni()],
	server: {
		// port: 3000,
		proxy: {
			"/baseApi": {
				target: "https://canary.shukeyun.com/common/common-gateway",

				changeOrigin: true,
				rewrite: path => path.replace(/^\/baseApi/, "")
			},
			"/casApi": {
				target: "https://canary.shukeyun.com/cas/api/v1",
				// target: 'https://prod.shukeyun.com/cas/api/v1',
				changeOrigin: true,
				rewrite: path => path.replace(/^\/casApi/, "")
			},
			"/mapApi": {
				target: "https://apis.map.qq.com",
				changeOrigin: true,
				rewrite: path => path.replace(/^\/mapApi/, "")
			}
		}
	}
})