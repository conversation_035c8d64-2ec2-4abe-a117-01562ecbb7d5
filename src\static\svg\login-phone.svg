<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 3@3x</title>
    <g id="导览" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="登录-账号备份-3" transform="translate(-126.000000, -632.000000)">
            <g id="编组-3" transform="translate(126.000000, 632.000000)">
                <rect id="矩形" fill="var(--theme-color, black)" opacity="0" x="0" y="0" width="42" height="42"></rect>
                <path d="M28,5 C31.3137085,5 34,7.6862915 34,11 L34,32 C34,35.3137085 31.3137085,38 28,38 L14,38 C10.6862915,38 8,35.3137085 8,32 L8,11 C8,7.6862915 10.6862915,5 14,5 L28,5 Z M21,25 C18.790861,25 17,26.790861 17,29 C17,31.209139 18.790861,33 21,33 C23.209139,33 25,31.209139 25,29 C25,26.790861 23.209139,25 21,25 Z" id="形状结合" fill="var(--theme-color, black)"></path>
            </g>
        </g>
    </g>
</svg>