<style lang="scss" scoped>
.contact-us {
	height: 100%;
	background: #f1f1f1;
	padding: 30rpx;

	.uuu-icon {
		margin-left: 20rpx;
		width: 30rpx;
		height: 30rpx;
	}

	.pop-box {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 444rpx;
		height: 543rpx;

		.pop-bg {
			text-align: center;
			background-color: #fff;
			padding: 60rpx;
			border-radius: 12rpx;
		}

		.qr-code {
			width: 324rpx;
			height: 324rpx;
		}

		.wx-name {
			margin-top: 22rpx;
			font-size: 30rpx;
			color: #14131f;
		}

		.close {
			margin-top: 50rpx;
		}
	}
}
</style>
<template>
	<view class="contact-us">
		<template v-for="(item, index) in btnList" :key="index">
			<view class="y-cell" @click="openPop(item)">
				<view class="y-cell-title">{{ item.type }}</view>
				<view class="y-cell-right">
					{{ item.contractWay }}
					<uni-icons v-if="item.attachment" type="right" color="#000" size="20" />
					<image v-else @click="copyText(item.contractWay)" class="uuu-icon" src="@/static/image/my/copy2-icon.png"
						mode="scaleToFill" />
				</view>
			</view>
			<!-- <view class="y-cell" v-if="item.type === '邮箱'">
				<view class="y-cell-title">邮箱</view>
				<view class="y-cell-right">
					{{ item.contractWay }}
					<image
						@click="copyText('dddd')"
						class="uuu-icon"
						src="@/static/image/my/copy2-icon.png"
						mode="scaleToFill" />
				</view>
			</view>
			<view class="y-cell" v-if="item.type === '微信'" @click="openPop(item)">
				<view class="y-cell-title">微信</view>
				<view class="y-cell-right">
					<uni-icons type="right" color="#000" size="20" />
				</view>
			</view> -->
		</template>

		<!-- <view class="y-cell" @click="goPage.push('/pages/myCollect/myCollect')">
			<view class="y-cell-title">QQ</view>
			<view class="y-cell-right">
				<uni-icons type="right" color="#000" size="20" />
			</view>
		</view> -->

		<uni-popup ref="popupRef" @change="change">
			<view class="pop-box">
				<view class="pop-bg">
					<image class="qr-code" :src="popContent.url" mode="scaleToFill" />
					<view class="wx-name">{{ popContent.name }}</view>
				</view>

				<uni-icons @click="popupRef.close()" type="close" class="close" color="#fff" size="48"></uni-icons>
			</view>
		</uni-popup>
	</view>
</template>
<script setup>
import { copyText, getRoute } from "@/utils/tool.js"
import request from "@/utils/request.js"
import { ref, onMounted } from "vue"
import { getEnv } from "@/utils/getEnv";

const popupRef = ref(null)
const btnList = ref("")
onMounted(async () => {
	const storeId = getRoute.params().storeId

	const { code, data } = await request.get(`/contractStaff/list/${storeId}`)
	btnList.value = data
})

const popContent = ref({})
const openPop = item => {
	console.log(item.attachment)
	if (!item.attachment) return
	popContent.value = {
		name: item.contractWay,
		url: getEnv().VITE_IMG_HOST + item.attachment
	}
	popupRef.value.open("center")
}
</script>
