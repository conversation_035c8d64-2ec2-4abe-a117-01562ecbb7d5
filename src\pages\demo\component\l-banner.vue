<template>
  <view class="banner" :style="{ marginBottom: conf.moduleMargin }">
    <!-- 一行一个 -->
    <template v-if="conf.swiperType === '0'">
      <view v-for="(item, index) in conf.imageList" :key="item.src">
        <view style="display: flex">
          <image :src="item.src" class="swiper-image" style="height: 422rpx" :style="{
            boxShadow: conf.pictureType,
            borderRadius: conf.borderRadius,
            marginBottom: conf.imageMargin,
            marginLeft: conf.pageMargin,
            marginRight: conf.pageMargin,
          }" @click="goLink(item)" :mode="conf.aspectType" />
        </view>
      </view>
    </template>
    <!-- 轮播海报 -->

    <view v-else-if="conf.swiperType === '1'" :class="conf.indicatorType" class="position-relative">
      <swiper class="swiper-box" style="height: 422rpx" circular @change="change" :current="swiperIndex"
        :indicator-dots="true" :autoplay="conf.autoSwitch" :interval="conf.durationType" :duration="500">
        <block v-for="(item, index) in conf.imageList" :key="item.src">
          <swiper-item class="swiper-item">
            <image :style="{
              boxShadow: conf.pictureType,
              borderRadius: conf.borderRadius,
              marginBottom: conf.imageMargin,
              marginLeft: conf.pageMargin,
              marginRight: conf.pageMargin,
            }" class="swiper-image" :src="item.src" @click="goLink(item)" :mode="conf.aspectType" />
          </swiper-item>
        </block>
      </swiper>
      <view v-if="['point-3', 'point-4'].includes(conf.indicatorType)" class="image-total" :style="{
        marginRight: conf.pageMargin,
      }">{{ swiperIndex + 1 }}/{{ conf.imageList.length }}</view>
    </view>

    <!-- 双层轮播 -->
    <view v-else-if="conf.swiperType === '2'" class="position-relative" :class="conf.indicatorType" :style="{
      marginLeft: conf.pageMargin,
      marginRight: conf.pageMargin,
    }">
      <view class="double-swiper-bg">
        <image v-for="(item, index) in conf.imageList" :key="item.srcBg" class="double-img" style="height: 422rpx"
          :style="{
            opacity: swiperIndex === index ? 1 : 0,
            borderRadius: conf.borderRadius,
          }" :src="item.srcBg" :mode="conf.aspectType" />
      </view>
      <swiper class="double-swiper swiper-box" circular style="height: 422rpx" @change="change" :current="swiperIndex"
        :indicator-dots="true" :autoplay="conf.autoSwitch" :interval="conf.durationType" :duration="500">
        <block v-for="(item, index) in conf.imageList" :key="item.src">
          <swiper-item class="swiper-item">
            <image class="double-swiper-img" :style="{ borderRadius: conf.borderRadius }" style="height: 422rpx"
              :src="item.src" @click="goLink(item)" mode="aspectFit" />
          </swiper-item>
        </block>
      </swiper>
      <view v-if="['point-3', 'point-4'].includes(conf.indicatorType)" class="image-total" :style="{
        marginRight: conf.pageMargin,
      }">{{ swiperIndex + 1 }}/{{ conf.imageList.length }}</view>
    </view>
    <!-- 横向滚动 -->
    <template v-else-if="conf.swiperType === '3'">
      <view :style="{
        height: conf.imageHeight,
        marginLeft: conf.pageMargin,
      }" class="corridor-out">
        <view class="corridor-swiper">
          <image v-for="(item, index) in conf.imageList" :key="item.src" :src="item.src" class="corridor-img"
            style="height: 422rpx" :style="{
              height: conf.imageHeight,
              width: conf.imageWidth,
              boxShadow: conf.pictureType,
              borderRadius: conf.borderRadius,
              marginLeft: index !== 0 ? conf.imageMargin : '0rpx',
            }" @click="goLink(item)" mode="aspectFill" />
        </view>
      </view>
    </template>
  </view>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import request from "@/utils/request.js";
import { getRoute, pxToRpx, goLink } from "@/utils/tool.js";
import { getEnv } from "@/utils/getEnv";

const { VITE_IMG_HOST } = getEnv();
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      imageList: [],
    }),
  },
});

const swiperIndex = ref(0);

const conf = computed(() => {
  const style: any = {};
  const {
    pictureType,
    durationType,
    borderRadius,
    pageMargin,
    imageMargin,
    moduleMargin,
    aspectType,
    screenType,
    indicatorType,
  } = props.config;
  // 图片样式
  if (pictureType === "projection") {
    style.pictureType = `0rpx 3rpx 6rpx 3rpx rgba(209,209,209,0.5)`;
  } else if (pictureType === "routine") {
    style.pictureType = `initial`;
  }
  // 边角样式
  if (borderRadius) {
    style.borderRadius = `${pxToRpx(borderRadius)}`;
  }
  // 页面间距
  if (pageMargin) {
    style.pageMargin = `${pxToRpx(pageMargin)}`;
  }
  // 图片间距
  if (imageMargin) {
    style.imageMargin = `${pxToRpx(imageMargin)}`;
  }
  // 重叠高度
  if (moduleMargin) {
    style.moduleMargin = `${pxToRpx(moduleMargin)}`;
  }
  // 轮播图切换间隔
  if (durationType === "0") style.durationType = 3000;
  if (durationType === "1") style.durationType = 4000;
  if (durationType === "2") style.durationType = 5000;
  // 图片填充样式
  if (aspectType === "fill") style.aspectType = "aspectFill";
  if (aspectType === "fit") style.aspectType = "aspectFit";
  // 轮播提示
  if (indicatorType === "0") style.indicatorType = "point-1";
  if (indicatorType === "1") style.indicatorType = "point-2";
  if (indicatorType === "2") style.indicatorType = "point-3";
  if (indicatorType === "3") style.indicatorType = "point-4";
  // 横向滚动 - 图片高度
  if (screenType === "0") {
    // 一屏一个
    style.imageHeight = `422rpx`;
    style.imageWidth = `${pxToRpx(350)}`;
  }
  if (screenType === "1") {
    // 一屏两个
    style.imageHeight = `211rpx`;
    style.imageWidth = `${pxToRpx(180)}`;
  }

  return {
    ...props.config,
    ...style,
  };
});

// banner 相关
const banners = ref<{ pic: string; link: string }[]>([]);

const change = (e: any) => {
  swiperIndex.value = e.detail.current;
};

const getBanners = async () => {
  try {
    const { data = [] } = await request.get(`/banner/list`, {
      storeId: getRoute.params().storeId,
      type: 1, //查询包含 banner：默认所有，1 启用
    });
    banners.value = (data ?? [])
      .map((i: any) => ({
        pic: `${VITE_IMG_HOST}${i.pic}`,
        link: i.link,
        sort: i.sort || -1,
      }))
      .sort((a: { sort: number }, b: { sort: number }) => a.sort - b.sort);
  } catch (err) {
    console.log(err);
  }
};

// 搜索值
const search = ref("");
//初始化
const isIos = ref(false);

onMounted(() => {
  if (uni.getSystemInfoSync().platform == "ios") {
    isIos.value = true;
  } else {
    isIos.value = false;
  }
  getBanners();
});
</script>
<style lang="scss" scoped>
.banner {
  position: relative;

  .swiper-image {
    width: 100%;
    height: 100%;
  }

  .swiper-image-cg {
    box-shadow: 0px 3rpx 6rpx 3rpx rgba(209, 209, 209, 0.5);
    border-radius: 12rpx;
  }

  .image-total,
  ::v-deep .uni-swiper-dot,
  ::v-deep .uni-swiper-dot-active {
    opacity: 0;
  }

  .point-1 {
    .swiper-box ::v-deep .uni-swiper-dot {
      opacity: 1;
      width: 8rpx;
      height: 8rpx;
      margin-right: 6rpx !important;
      background-color: #d8d8d8;
    }

    .swiper-box ::v-deep .uni-swiper-dot-active {
      opacity: 1;
      transition: 0.3s;
      width: 16rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background-color: #ffffff;
    }
  }

  .point-2 {
    .swiper-box ::v-deep .uni-swiper-dot {
      opacity: 1;
      width: 17rpx;
      height: 8rpx;
      margin-right: 6rpx !important;
      border-radius: 0rpx;
      background-color: #d8d8d8;
    }

    .swiper-box ::v-deep .uni-swiper-dot-active {
      opacity: 1;
      transition: 0.3s;
      background-color: #ffffff;
    }
  }

  .point-3 {
    .image-total {
      opacity: 1;
      position: absolute;
      bottom: 14rpx;
      right: 20rpx;
      font-size: 22rpx;
      color: #fff;
    }
  }

  .point-4 {
    .image-total {
      opacity: 1;
      position: absolute;
      bottom: 14rpx;
      right: 20rpx;
      font-size: 22rpx;
      color: #fff;
      padding: 0rpx 10rpx;
      background: rgba(2, 2, 2, 0.52);

      border-radius: 15rpx;
    }
  }
}

.double-swiper-bg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;

  .double-img {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    transition: 1s;
  }
}

.double-swiper {
  .double-swiper-img {
    width: 100%;
    height: 100%;
  }
}

.corridor-out {
  height: 300rpx;
  overflow: hidden;

  .corridor-swiper {
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;

    .corridor-img {
      height: 300rpx;
      width: 700rpx;
    }
  }
}

.swiper-item {
  display: flex;
}
</style>
