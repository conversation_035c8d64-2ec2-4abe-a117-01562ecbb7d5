<style lang="scss" scoped>
.panel-box {
	flex: none; // 不随父元素的大小改变
	margin-bottom: 20rpx;

	padding: 20rpx;

	color: #14131f;
	font-size: 28rpx;
	font-size: 32rpx;
	background: #ffffff;
	border-radius: 12px;

	.tip__content {
		display: inline-block;

		max-width: 600rpx;
		margin: 10rpx 0;
		padding: 6rpx 20rpx;
		overflow: hidden;

		color: #349fff;
		font-size: 26rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		background: #e5f3ff;
		border-radius: 24rpx;
	}

	.title {
    display: flex;
    justify-content: space-between;
		color: #14131f;

		font-weight: 500;

		font-size: 32rpx;
	}

	.item {
		display: flex;

		align-items: center;

		justify-content: space-between;
		padding: 20rpx 0;

		font-size: 28rpx;

		.right {
			display: flex;

			align-items: center;

			.circle {
				position: relative;

				width: 44rpx;

				height: 44rpx;

				border: 4rpx solid #349fff;

				border-radius: 50%;

				.hen {
					position: absolute;

					top: 50%;

					left: 50%;

					width: 20rpx;

					height: 4rpx;

					background: #349fff;

					transform: translate(-50%, -50%);
				}

				.shu {
					position: absolute;

					top: 50%;

					left: 50%;

					width: 4rpx;

					height: 20rpx;

					background: #349fff;

					transform: translate(-50%, -50%);
				}
			}

			.num {
				width: 100rpx;
					text-align: center;
				font-size: 36rpx;
			}
		}
	}

	.buttom {
		display: flex;

		gap: 30rpx;
		justify-content: space-between;
		margin-top: 50rpx;

		div {
			width: 50%;
			padding: 20rpx 0;

			color: #349fff;
			font-weight: 500;
			font-size: 36rpx;
			text-align: center;
			border-radius: 12px;
		}

		.left {
			color: #349fff;
			background-color: rgba(52, 159, 255, 0.17);
		}

		.right {
			color: #fff;
			background-color: #349fff;
		}
	}
}

.panel-box__item {
  margin-top: 20rpx;
}

.multiple {
	.multiple-title {
		margin-bottom: 20rpx;

		color: #14131f;

		font-weight: 500;

		font-size: 28rpx;
	}

	.multiple-content {
		display: flex;

		flex-wrap: wrap;

		div {
			margin: 10rpx 20rpx 10rpx 0;

			padding: 6rpx 20rpx;

			color: #14131f;
			font-size: 28rpx;
			background: #f6f6f6;
			border: 1rpx solid #bfbfbf;
			border-radius: 6rpx;
		}

		.active {
			color: #fff;
			background: #349fff;
			border: 1rpx solid #349fff;
		}
	}
}
</style>
<template>
	<div v-if="type === 'planTip'" class="panel-box">
		很高兴为您服务，关于行程规划您可以试试问我一下问题！
		<div style="margin: 10rpx 0">
			<div @click="emits('onSubmit', item)" class="tip__content" v-for="(item, index) in config.tipList"
				:key="index">
				{{ item }}
			</div>
		</div>
	</div>
	<div v-if="type === 'tripPreference'" class="panel-box">
		<div class="title"><span>请选择您的行程偏好</span><uni-icons :type="isFold ? 'arrowup' : 'arrowdown'" color="#000" size="24" @click="onFold" /></div>
		<div :style="{ display: isFold ? 'none' : 'block' }" class="panel-box__item">
      <div class="item" v-if="params.show.days">
        出游天数
        <div class="right">
          <div @click="onsub('days')" class="circle">
            <div class="hen" />
          </div>
          <div class="num">{{ params.days }}</div>
          <div @click="onAdd('days')" class="circle">
            <div class="hen" />
            <div class="shu" />
          </div>
        </div>
      </div>
      <div class="item" v-if="params.show.number">
        出游人数
        <div class="right">
          <div @click="onsub('number')" class="circle">
            <div class="hen" />
          </div>
          <div class="num">{{ params.number }}</div>
          <div @click="onAdd('number')" class="circle">
            <div class="hen" />
            <div class="shu" />
          </div>
        </div>
      </div>
      <div class="multiple" v-if="params.show.peopleList">
        <div class="multiple-title">人群类型（可多选）</div>
        <div class="multiple-content">
          <div :class="{ active: item.active }" @click="onSelect('peopleList', index)"
            v-for="(item, index) in params.peopleList">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="multiple" v-if="params.show.journeyList">
        <div class="multiple-title">行程期待（可多选）</div>
        <div class="multiple-content">
          <div :class="{ active: item.active }" @click="onSelect('journeyList', index)"
            v-for="(item, index) in params.journeyList">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="buttom">
        <div @click="onSubmit(false)" class="left">跳过</div>
        <div @click="onSubmit(true)" class="right">确定</div>
      </div>
    </div>
	</div>
</template>

<script setup>
const props = defineProps({
	type: {
		type: String,
		default: "planTip"
	},
	config: {
		type: Object,
		default: () => ({})
	}
})
const tipList = ref([
	"深圳一人游，有哪些必去景点？",
	// autocorrect: false
	"对于深圳3日游，有哪些必去的景点和推荐行程安排？",
	// autocorrect: false
	"深圳2日游，有什么必去的路线？"
])

const isFold = ref(false)

// 展开收起
const onFold = () => {
  isFold.value = !isFold.value
}

const onSelect = (type, index) => {
	params[type][index].active = !params[type][index].active
}

const emits = defineEmits(["onSubmit"])

const onSubmit = isSubmit => {
	if (isSubmit) {
		let peopleStr
		let journeyStr
		if (params.peopleList) {
			peopleStr = params.peopleList.filter(item => item.active).map(item => item.name).join("、")
		}
		if (params.journeyList) {
			journeyStr = params.journeyList.filter(item => item.active).map(item => item.name).join("、")
		}

		let text = '行程偏好如下'
		if (params?.days) text += `，出游天数：${params.days}天`
		if (params?.number) text += `，出游人数：${params.number}人`
		if (peopleStr) text += `，人群类型：${peopleStr}`
		if (journeyStr) text += `，行程期待：${journeyStr}`
		emits("onSubmit", text)
    onFold(false);
	}
	emits("onSubmit", {
		type: "closeTripPreference"
	})
}

const params = reactive({
	days: 1,
	number: 1,
	peopleList: [
		{
			name: "亲子",
			active: false
		},
		{
			name: "带爸妈",
			active: false
		},
		{
			name: "好友结伴",
			active: false
		},
		{
			name: "情侣/夫妻",
			active: false
		},
		{
			name: "蜜月",
			active: false
		}
	],
	journeyList: [
		{
			name: "行程紧凑",
			active: false
		},
		{
			name: "行程宽松",
			active: false
		},
		{
			name: "地标打卡",
			active: false
		},
		{
			name: "地道美食",
			active: false
		}
	],
	show: {
		peopleList: true,
		journeyList: true,
		days: true,
		number: true
	}
})
const onAdd = key => {
	params[key]++
}
const onsub = key => {
	if (params[key] > 1) {
		params[key]--
	}
}


watch(
	() => props.config,
	(val) => {
		if (val) {
			if (val.days) {
				params.days = val.days
				params.show.days = false
			} else {
				params.show.days = true
			}
			if (val.number) {
				params.number = val.number
				params.show.number = false
			} else {
				params.show.number = true
			}
			if (val.people_type) {
				params.peopleList.forEach(item => {
					if (val.people_type.includes(item.name)) {
						item.active = true
					}
				})
				params.show.peopleList = false
			} else {
				params.show.peopleList = true
			}
			if (val.travle_type) {
				params.journeyList.forEach(item => {
					if (val.travle_type.includes(item.name)) {
						item.active = true
					}
				})
				params.show.journeyList = false
			} else {
				params.show.journeyList = true
			}
		}
	},
	{
		immediate: true
	}
)

</script>
