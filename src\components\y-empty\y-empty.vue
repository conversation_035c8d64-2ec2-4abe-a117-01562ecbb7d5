<template>
	<view
		class="default-box"
		:style="{
			paddingTop: props.margin + 'rpx',
			paddingBottom: props.margin + 'rpx'
		}">
		<image
			v-if="props.type === 'scenic'"
			class="icon"
			src="@/static/image/default-icon.png"
			mode="widthFix"></image>
		<image
			v-else-if="props.type === 'order'"
			class="icon"
			src="@/static/image/order/order-default-icon.png"
			mode="widthFix"></image>
		<image
			v-else
			class="icon"
			src="@/static/image/default-icon.png"
			mode="widthFix"></image>
		<view class="text">
			<slot />
		</view>
	</view>
</template>

<script setup>
import { ref, toRefs } from "vue"
const props = defineProps({
	type: {
		type: String,
		default: ""
	},
	tip: {
		type: String,
		default: "暂无内容"
	},
	margin: {
		type: String,
		default: "100"
	}
})
</script>

<style lang="scss" scoped>
.default-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.icon {
		width: 400rpx;
		margin-bottom: 22rpx;
	}
	.text {
		font-size: 26rpx;
		font-weight: 400;
		color: #14131f;
		line-height: 26rpx;
		opacity: 0.8;
	}
}
</style>
