<style lang="scss" scoped>
.tutu {
	position: fixed;
	top: 0;
	z-index: 10001;
	will-change: transform;
	transition: transform 0.1s;
	.tutu__img {
		width: 200rpx;
	}
	.tutu__dialogue {
		position: absolute;
    min-width: 200rpx;
		max-width: 354rpx;
		transform: translate(0%, -100%);
	}
}
</style>
<template>
	<div
		class="tutu"
		:style="{
			transform: `translate(${posX}px, ${posY}px)`
		}"
		@touchstart="startDrag"
		@touchmove="onDragging"
		@touchend="endDrag"
		@click="openDialogue">
		<image
			class="tutu__img"
			src="@/static/image/ai/happy_talk.gif"
			mode="widthFix" />
		<uni-transition :mode-class="['fade', 'zoom-in']" :show="isSay">
			<div class="tutu__dialogue"
        :style="{
          left: isDialoguePosLeft ? '' : `calc(190rpx - 10%)`,
          right: isDialoguePosLeft ? `calc(190rpx - 10%)` : '',
          top: posY < 50 ? `calc(${posY}px + 100%)` : '-130rpx',
        }"
      >
        <dialogue :content="sayContent" isTip :posX="posX" :posY="posY" />
      </div>
		</uni-transition>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue"
import dialogue from "@/pages/ai/components/dialogue.vue"
import useDraggable from "@/hooks/useDraggable"
import { isInViewport } from "@/utils/tool.js"
import eventBus from "@/utils/eventBus.js"

const props = defineProps({})

const showDialogue = ref(false)
const isDialoguePosLeft = ref(true)

// tutu 说话
const sayContent = ref()
const isSay = ref(false)

// 设置 tutu 说话内容
const setSayContent = content => {
	isSay.value = false
	setTimeout(() => {
		sayContent.value = content
		isSay.value = true
	}, 400)
}

// 初始欢迎语
setTimeout(() => {
	setSayContent("你好呀，我是 TUTU")
	setTimeout(() => {
		setSayContent("可以点击和我聊天喔")
		setTimeout(() => {
			isSay.value = false
		}, 1500)
	}, 2000)
}, 2000)

// 监听文本更新事件
onMounted(() => {
  eventBus.on('textUpdate', (data) => {
    // 如果收到隐藏气泡的指令，则隐藏气泡
    if (data.hide) {
      isSay.value = false;
      return;
    }
    // 显示文本内容
    setSayContent(data.text);
  });
})

// 组件卸载时取消订阅
onUnmounted(() => {
  eventBus.off('textUpdate');
})

// 计算位置
const computedDialoguePos = (x, y) => {
  const isLeft = window.innerWidth / x <= 2
  isDialoguePosLeft.value = isLeft
}

let tutuPos = localStorage.getItem("tutuPos")

if (tutuPos) {
	tutuPos = JSON.parse(tutuPos)
  computedDialoguePos(tutuPos.x, tutuPos.y)
  } else {
	tutuPos = { x: 289, y: 502 }
  computedDialoguePos(tutuPos.x, tutuPos.y)
}
const { posX, posY, startDrag, onDragging, endDrag } = useDraggable({
	initPos: {
		x: tutuPos ? tutuPos.x : 0,
		y: tutuPos ? tutuPos.y : 0
	},
	endCallback: () => {
		localStorage.setItem(
			"tutuPos",
			JSON.stringify({ x: posX.value, y: posY.value })
		)
    computedDialoguePos(posX.value, posY.value)
	}
})

// 打开对话框
const openDialogue = () => {
  // 实现打开对话的逻辑
}
</script>
