<template>
	<view :class="{ tab: true, [colorMap[activeKey]]: true }" :style="style">
		<view class="bar">
			<view v-for="(item, index) in tabList" class="list" :key="item.text">
				<view :class="{ title: true, [colorMap[index]]: index === activeKey }" @click="handleTabClick(index)">{{
					item.text }}</view>
				<!-- <view class="divider" v-if="index !== tabList.length - 1"></view> -->
			</view>
		</view>
		<view class="content">
			<slot></slot>
		</view>
	</view>
</template>
<script lang="ts" setup>

import { ref, type CSSProperties } from "vue"

interface TabControlProps {

	tabList: {
		text: string
		goodsList: any
	}[]
	onChange?: (key: string | number) => void
	defaultKey?: string
	style?: CSSProperties
}

const { onChange, tabList = [], defaultKey, style = {} } = defineProps<TabControlProps>()

const colorMap = computed(() => {
	const map: any = {}
	tabList.forEach((item, index) => {
		map[index] = ["blue", "orange", "green"][index]
	})
	return map
})

const activeKey = ref(defaultKey || 0)

const handleTabClick = (key: number) => {
	if (activeKey.value !== key) {
		activeKey.value = key
		onChange?.(key)
	}
}
</script>

<style lang="scss" scoped>
@mixin tab-suffix($color) {
	position: relative;
	font-size: 30rpx;
	line-height: 42rpx;

	&::before {
		position: absolute;
		width: 35rpx;
		height: 6rpx;
		content: " ";
		top: -10rpx;
		left: 50%;
		background-color: $color;
		border-radius: 11rpx;
		transform: translateX(-50%);
	}
}

.tab {
	// height: 282rpx;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;

	.bar {
		display: flex;
		padding: 20rpx 0 10rpx 0;
		justify-content: space-around;

		.title {
			font-size: 28rpx;
			font-weight: 500;
			line-height: 40rpx;
			transition: 0.3s;
			position: relative;
			background: none !important;

			&::after {
				position: absolute;
				width: 2rpx;
				height: 30rpx;
				content: " ";
				right: -62rpx;
				top: 8rpx;
				background-color: rgba(151, 151, 151, 0.24);
			}
		}

		// 去掉最后一个
		.list:last-child .title::after {
			display: none;
		}

		.blue {
			color: #349fff;
			@include tab-suffix(#349fff);
		}

		.orange {
			color: #fa7d00;
			@include tab-suffix(#fa7d00);
		}

		.green {
			color: #149aad;
			@include tab-suffix(#149aad);
		}
	}

	.content {
		flex: 1;
	}
}

.blue {
	background: linear-gradient(180deg,
			#bbd9ff 0%,
			rgba(238, 244, 251, 0.54) 34%,
			rgba(217, 242, 255, 0.43) 100%);
}

.orange {
	background: linear-gradient(180deg,
			#ffdcd5 0%,
			rgba(251, 243, 238, 0.63) 58%,
			rgba(253, 243, 242, 0.72) 100%);
}

.green {
	background: linear-gradient(180deg,
			#d5fff5 0%,
			rgba(238, 250, 251, 0.63) 58%,
			#f2fffd 100%);
}
</style>
