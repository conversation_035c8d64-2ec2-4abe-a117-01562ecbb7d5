<template>
	<scroll-view scroll-y="true" scroll-with-animation="true" class="location-list"
		:class="[props.modelValue?'show':'hide']">
		<view class="shortcut" v-for="(item, index) in labelList" :key="index">
			<view class="title">
				<text class="left">{{item.name}}</text>
			</view>
			<view class="shortcut-box">
				<view class="city-box" v-for="(e,i) in item.list" :key="i" @click="onChangeLabel(e)">{{e.name}}</view>
			</view>
		</view>
	</scroll-view>
</template>
<script setup>
	import {
		reactive,
		ref,
		onMounted
	} from 'vue'
	import request from '@/utils/request.js'
	import {
		getRoute
	} from '@/utils/tool.js'

	const labelList = ref([])
	onMounted(async () => {
		//获取标签
		const {
			data
		} = await request.get(`/label/list`, {
			storeId: getRoute.params().storeId
		});
		labelList.value = data
	});

	const props = defineProps({
		//显示隐藏
		modelValue: {
			type: Boolean,
			default: false
		},
		//选中的label
		label: {
			type: String,
			default: ''
		}
	})
	const emits = defineEmits(["update:modelValue", "search"])

	//选择标签
	const onChangeLabel = (label) => {
		emits('search', label)
		emits('update:modelValue', false)
	}
</script>
<style lang="scss" scoped>
	.location-list {
		// display: none;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		z-index: 100;
		border-top-left-radius: 60rpx;
		$transition-time: 0.2s;
		transition: all $transition-time;

		&.show {
			height: calc(100vh - 131rpx);
		}

		&.hide {
			height: 0;
		}

		.shortcut {
			padding: 30rpx 40rpx;

			.title {
				display: flex;
				justify-content: space-between;
				margin-top: 10rpx;
				margin-bottom: 20rpx;

				.left {
					font-size: 30rpx;
					font-weight: 500;
					color: #0D0D0D;
				}

				.right {
					font-size: 24rpx;
					font-weight: 400;
					color: #1C78E9;
				}
			}

			.shortcut-box {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -17rpx;

				.city-box {
					flex: none;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 200rpx;
					height: 70rpx;
					margin: 0 17rpx 30rpx;
					font-size: 30rpx;
					color: #0D0D0D;
					background-color: #F4F4F4;
					border-radius: 12rpx;

					.icon {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}

		}
	}
</style>