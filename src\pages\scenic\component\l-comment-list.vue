<template>
	<view class="comment-list">
		<!-- 使用吸顶筛选栏组件 -->
		<l-sticky-filter :filter-tags="filterTags" :sort-type="sortType" :is-visible="showStickyFilter"
			@tag-select="selectTag" @sort-change="changeSortType">
		</l-sticky-filter>

		<!-- 标题栏 -->
		<view class="section-header flex-row justify-between" v-if="!isPage">
			<view class="header-title flex-row">
				<view class="header-line"></view>
				<text class="header-text">用户点评</text>
			</view>
			<view class="write-review-btn flex-row" @click="goToWriteReview">
				<image class="write-icon" src="/static/image/write-comment.webp" mode="aspectFit"></image>
				<text class="write-text">写点评</text>
			</view>
		</view>

		<!-- 有初始数据时显示评分总览和标签筛选 -->
		<block v-if="hasInitialData">
			<!-- 评分总览 -->
			<view class="rating-overview flex-row">
				<text class="rating-overall">{{ scenicRating.overall }}</text>
				<view class="rating-item flex-col">
					<text class="rating-score">{{ scenicRating.level }}</text>
					<text class="rating-label">{{ scenicRating.countTool }}条点评</text>
				</view>
				<view class="rating-item flex-col">
					<text class="rating-score">{{ scenicRating.environment }}</text>
					<text class="rating-label">景色/环境</text>
				</view>
				<view class="rating-item flex-col">
					<text class="rating-score">{{ scenicRating.fun }}</text>
					<text class="rating-label">趣味</text>
				</view>
				<view class="rating-item flex-col">
					<text class="rating-score">{{ scenicRating.service }}</text>
					<text class="rating-label">服务/性价比</text>
				</view>
			</view>

			<!-- 标签筛选 -->
			<view class="filter-tags-container">
				<view class="filter-tags flex-row" :class="{ 'expanded': isTagsExpanded }">
					<view v-for="(tag, index) in filterTags" :key="index" class="tag-item"
						:class="[tag.active ? 'tag-active' : '']" @click="selectTag(index)">
						<text class="tag-text" :class="[tag.active ? 'tag-text-active' : '']">{{ tag.text }}</text>
					</view>
				</view>
				<view class="expand-arrow" v-if="needExpand" @click="toggleTagsExpand">
					<y-svg name="arrow-down" class="arrow-icon" :class="{ 'is-expanded': isTagsExpanded }"></y-svg>
				</view>
			</view>

			<view class="sort-options flex-row" v-if="isPage" id="sort-options-placeholder">
				<text class="sort-option" :class="{ 'sort-option-active': sortType === 'recommend' }"
					@click="changeSortType('recommend')">推荐</text>
				<text class="sort-option" :class="{ 'sort-option-active': sortType === 'latest' }"
					@click="changeSortType('latest')">最新</text>
			</view>


			<!-- 评论列表 -->
			<block v-if="commentState.comments.length > 0">
				<view class="comment-container" v-for="(item, index) in commentState.comments" :key="index"
					@click="goToCommentDetail(item)">
					<!-- 用户信息 -->
					<view class="user-info-row flex-row">
						<image class="user-avatar" :src="item.userAvatar" mode="aspectFill"></image>
						<view class="user-detail flex-col">
							<text class="user-name">{{ item.userName }}</text>
							<text class="comment-date">{{ item.createTime }}</text>
						</view>
						<view class="order-divider"></view>
						<text class="order-source">{{ item.type === 1 ? '来自订单' : '来自游客分享' }}</text>
					</view>

					<!-- 评分信息 -->
					<view class="score-row flex-row">
						<view class="score-badge flex-col">
							<text class="score-value">{{ item.score }}</text>
						</view>
						<text class="score-level">{{ getScoreLevel(item.score) }}</text>
						<text class="score-detail">景色 {{ item.environmentScore }}</text>
						<text class="score-detail">趣味 {{ item.funScore }}</text>
						<text class="score-detail">性价比 {{ item.valueScore }}</text>
					</view>

					<!-- 标签 -->
					<view class="review-tags flex-row" v-if="item.tags && item.tags.length > 0">
						<view class="review-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">
							<text class="review-tag-text">{{ tag }}</text>
						</view>
					</view>

					<!-- 评论内容 -->
					<view class="comment-content flex-row">
						<text class="content-text" :class="{ 'content-text-expanded': item.expanded }">{{ item.content
						}}</text>
						<view class="read-more-container" v-if="item.content && item.content.length > 100"
							@click.stop="item.expanded = !item.expanded">
							<text class="read-more">{{ item.expanded ? '收起' : '全文' }}</text>
							<y-svg name="arrow-down" class="content-arrow-icon" :class="{ 'is-expanded': item.expanded }"></y-svg>
						</view>
					</view>

					<!-- 图片 -->
					<view class="comment-images" v-if="item.images && item.images.length > 0">
						<view class="image-container"
							v-for="(img, imgIndex) in (item.images.length > 3 ? item.images.slice(0, 3) : item.images)"
							:key="imgIndex" @click.stop="previewImage(item, imgIndex)">
							<view class="side" v-if="isVideo(img)">
								<video :src="img" class="video" :controls="false" :show-play-btn="false" :autoplay="false" :muted="true"
									:enable-play-gesture="false" object-fit="cover"></video>
								<view class="inV"></view>
							</view>
							<image :src="img" mode="aspectFill" class="comment-image"></image>
							<view class="more-images-count" v-if="imgIndex === 2 && item.images.length > 3">
								<text class="more-images-text">+{{ item.images.length - 3 }}</text>
							</view>
						</view>
					</view>

					<!-- 商家回复 -->
					<view class="merchant-reply" v-if="item.merchantCommentsList && item.merchantCommentsList.length > 0">
						<view v-for="(reply, replyIndex) in item.merchantCommentsList" :key="replyIndex"
							class="merchant-reply-item">
							<view class="reply-header flex-row justify-between">
								<text class="reply-title">商家回复 </text>
								<text class="reply-date">{{ reply.createTime || '2023-07-15' }}</text>
							</view>
							<view class="reply-content flex-row">
								<view class="reply-text-container flex-row">
									<text class="reply-text" :style="{ maxHeight: reply.maxHeight || '102rpx' }">{{
										reply.content }}</text>
									<view class="expand-btn-container" v-if="reply.content && reply.content.length > 80"
										@click.stop="expandReply(reply)">
										<text class="expand-btn">{{ reply.expanded ? '收起' : '展开' }}</text>
										<y-svg name="arrow-down" class="expand-arrow-icon"
											:class="{ 'is-expanded': reply.expanded }"></y-svg>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 查看全部 -->
				<view class="view-all-container" v-if="!isPage && commentState.comments.length >= commentState.pageSize"
					@click="goToCommentList">
					<text class="view-all">查看全部点评</text>
					<y-svg name="arrow-right" class="arrow-right-icon"></y-svg>
				</view>

				<!-- 加载更多 -->
				<view class="load-more" v-if="commentState.hasMore">
					<y-loadmore :status="loadMoreStatus" @loadmore="loadMore"></y-loadmore>
				</view>
			</block>
			<view v-else class="no-comments-tip">
				<image class="empty-image" src="/static/image/comment-deault.webp" mode="aspectFit"></image>
				<text class="empty-text">暂无符合条件的点评</text>
			</view>
		</block>

		<view v-else class="empty-comment">
			<image class="empty-image" src="/static/image/comment-deault.webp" mode="aspectFit"></image>
			<text class="empty-text">暂无任何点评，欢迎发表你的点评</text>
		</view>
	</view>
	<view class="block_7 flex-row justify-between" v-if="isPage" :class="{ 'hidden': isScrolling }">
		<view class="text-wrapper_15 flex-col" @click="goToWriteReview">
			<text class="text_38">写点评</text>
		</view>
		<view class="text-wrapper_16 flex-col" @click="goToTicketList">
			<text class="text_39">查看门票</text>
		</view>
	</view>

	<!-- 图片预览组件 -->
	<y-preview-image :visible="previewVisible" :images="currentPreviewImages" :initialIndex="currentPreviewIndex"
		:userInfo="currentPreviewUserInfo" @close="previewVisible = false"></y-preview-image>
	<check-in-pop ref="checkInPopup"></check-in-pop>
</template>

<script setup>
import { ref, reactive, computed, onMounted, defineProps, onBeforeUnmount } from 'vue';
import request from "@/utils/request.js";
import checkInPop from "./check-in-pop.vue"
import { Tool } from "@/utils/tools";
import ySvg from '@/components/y-svg/y-svg.vue';
import lStickyFilter from './l-sticky-filter.vue';
import yPreviewImage from '@/components/y-preview-image/y-preview-image.vue';
import { getEnv } from "@/utils/getEnv";


const imgHost = getEnv().VITE_IMG_HOST;
// 定义组件属性
const props = defineProps({
	isPage: {
		type: Boolean,
		default: false
	},
	scenicName: {
		type: String,
		default: ''
	},
	scrollTop: {
		type: Number,
		default: 0
	},
	initialTag: {
		type: Object,
		default: null
	}
});

// 图片预览相关
const previewVisible = ref(false);
const currentPreviewImages = ref([]);
const currentPreviewIndex = ref(0);
const currentPreviewUserInfo = ref(null);

// 控制底部按钮的显示和隐藏
const isScrolling = ref(false);
let scrollTimer = null;

// 控制吸顶筛选栏的显示与隐藏
const showStickyFilter = ref(false);
// 记录排序栏的位置
let sortOptionsTop = 0;
watch(() => props.scrollTop, (newVal) => {
	// 仅当 sortOptionsTop 有一个有效的正值时，才进行判断
	if (sortOptionsTop > 10) {
		// 根据滚动位置判断是否显示吸顶筛选栏
		if (newVal > sortOptionsTop) {
			showStickyFilter.value = true;
		} else {
			showStickyFilter.value = false;
		}
	}
	// 如果是页面模式，则处理滚动事件来控制底部按钮显隐
	if (props.isPage) {
		isScrolling.value = true;
		if (scrollTimer) {
			clearTimeout(scrollTimer);
		}
		scrollTimer = setTimeout(() => {
			isScrolling.value = false;
		}, 300);
	}
})

// 排序类型：推荐/最新
const sortType = ref('recommend'); // 默认是推荐排序

// 是否已加载初始数据
const hasInitialData = ref(false);

// 默认头像
const defaultAvatar = '/static/image/default-avatar.png';

// 控制标签展开收起
const isTagsExpanded = ref(false);
const needExpand = ref(false);

// 切换标签展开/收起状态
const toggleTagsExpand = () => {
	isTagsExpanded.value = !isTagsExpanded.value;
};

// 跳转到评论列表页
const goToCommentList = () => {
	const baseUrl = `/pages/scenic/commentList?scenicName=${props.scenicName}&scenicId=${scenicId}`;
	const url = isTravelPage && travelGoodsId ? `${baseUrl}&travelGoodsId=${travelGoodsId}` : baseUrl;
	Tool.goPage.push(url);
};

// 评论数据
const commentState = reactive({
	comments: [],
	hasMore: false,
	loading: false,
	page: 1,
	pageSize: props.isPage ? 20 : 4,
	total: 0,
	filterParams: {} // 存储当前过滤参数
});

// 景区评分概览
const scenicRating = reactive({
	overall: '-',
	level: '-',
	environment: '-',
	fun: '-',
	service: '-',
	countTool: 0
});

const tagEnum = ref({})

// 内置标签类型定义
const TAG_TYPES = {
	ALL: 'all',
	POSITIVE: 'score', // 好评
	CONSUMPTION: 'comment', // 消费后评价
	PHOTO: 'picture', // 带图评价
	VIDEO: 'video', // 带视频评价
	NEGATIVE: 'score', // 差评
	LABEL: 'label' // 动态标签
};

// 获取景区评分概览
const getScenicRating = async () => {
	try {
		const params = {
			scenicId,
			storeId
		}
		if (isTravelPage) params.type = 3
		const { data } = await request.post('/comment/statisticsScore', params);

		console.log('评分概览数据：', data);

		if (data) {
			// 更新评分数据
			scenicRating.overall = data.scoreAll.toFixed(1)
			scenicRating.countTool = data.allNumber;
			// 根据评分确定满意度级别
			scenicRating.level = getScoreLevel(data.scoreAll);
			scenicRating.environment = data.environmentScoreAll?.toFixed(1) || '-';
			scenicRating.fun = data.interestScore?.toFixed(1) || '-'; // 趣味评分
			scenicRating.service = data.serviceScoreAll?.toFixed(1) || '-'; // 服务评分

			// 处理标签数据
			await processFilterTags(data);
			// 处理动态标签枚举
			if (data.data && data.data.length > 0) {
				data.data.forEach(item => {
					tagEnum.value[item.id] = item.labelName;
				});
			}
			if (data.allNumber > 0) {
				hasInitialData.value = true;
			}
		}
	} catch (err) {
		console.error('获取景区评分失败', err);
	}
};

// 筛选标签
const filterTags = reactive([]);

// 处理筛选标签数据
const processFilterTags = async (data) => {
	try {
		if (data) {
			// 创建内置标签
			const tags = [];

			// 全部标签
			tags.push({
				text: `全部 ${data.allNumber || 0}`,
				active: true,
				type: TAG_TYPES.ALL,
				tagId: ''
			});

			// 好评标签 (4 分及以上为好评)
			if (data.positiveReviews && data.positiveReviews > 0) {
				tags.push({
					text: `好评 ${data.positiveReviews}`,
					active: false,
					type: TAG_TYPES.POSITIVE,
					tagId: '1' // 好评对应 score=1
				});
			}

			// 消费后评价
			if (data.reviewsAfterConsumption && data.reviewsAfterConsumption > 0) {
				tags.push({
					text: `消费后评价 ${data.reviewsAfterConsumption}`,
					active: false,
					type: TAG_TYPES.CONSUMPTION,
					tagId: '1' // 消费后评价对应 commentType=1
				});
			}

			// 带图评价
			if (data.reviewsWithPhotos && data.reviewsWithPhotos > 0) {
				tags.push({
					text: `带图评价 ${data.reviewsWithPhotos}`,
					active: false,
					type: TAG_TYPES.PHOTO,
					tagId: '1' // 带图评价对应 pictureType=1
				});
			}

			// 带视频评价
			if (data.reviewsWithVideos && data.reviewsWithVideos > 0) {
				tags.push({
					text: `带视频评价 ${data.reviewsWithVideos}`,
					active: false,
					type: TAG_TYPES.VIDEO,
					tagId: '1' // 带视频评价对应 videoType=1
				});
			}

			// 差评 (2 分及以下为差评)
			if (data.negativeReviews && data.negativeReviews > 0) {
				tags.push({
					text: `差评 ${data.negativeReviews}`,
					active: false,
					type: TAG_TYPES.NEGATIVE,
					tagId: '3' // 差评对应 score=3
				});
			}

			// 添加动态标签 (来自接口返回的 data 字段)
			if (data.data && data.data.length > 0) {
				data.data.forEach(tag => {
					if (tag.labelNumber && tag.labelNumber > 0) {
						tags.push({
							text: `${tag.labelName} ${tag.labelNumber}`,
							active: false,
							type: TAG_TYPES.LABEL,
							tagId: tag.id
						});
					}
				});
			}

			// 清空原数组并添加新数据
			filterTags.splice(0, filterTags.length, ...tags);

			// 检查标签是否超过两行
			setTimeout(() => {
				checkTagsOverflow();
			}, 300);
		}
	} catch (err) {
		console.error('处理筛选标签失败', err);
	}
};

// 选择标签
const selectTag = (index) => {
	const selectedTag = filterTags[index];
	if (!props.isPage) {
		// 当不是独立页面时，点击标签跳转到评论列表页并传递标签信息
		uni.setStorageSync('selectedCommentTag', JSON.stringify({
			type: selectedTag.type,
			tagId: selectedTag.tagId
		}));
		goToCommentList();
		return;
	}

	// 单选模式，先清除所有选中状态
	filterTags.forEach((tag, i) => {
		tag.active = i === index;
	});

	// 更新当前筛选条件
	const activeTag = filterTags[index];
	commentState.currentFilter = activeTag.text;
	commentState.currentTagType = activeTag.type;
	commentState.currentTagId = activeTag.tagId;

	// 重置筛选参数
	commentState.filterParams = {};

	// 重置页码并重新加载评论
	commentState.page = 1;
	loadComments().then(() => {
		if (showStickyFilter.value) scrollToTop();
	});
};

// 根据分数获取评价级别
const getScoreLevel = (score) => {
	if (!score) return '满意';
	const numScore = parseFloat(score);
	if (numScore >= 5) return '超棒';
	if (numScore >= 4) return '满意';
	if (numScore >= 3) return '不错';
	if (numScore >= 2) return '一般';
	return '不佳';
};

const checkInPopup = ref(null)

// 跳转到写评论页面
const goToWriteReview = async () => {
	const userData = await Tool.getUserInfo()
	console.log(userData)
	const { data } = await request.post(`/comment/canLabel`, {
		scenicId,
		userId: userData.userInfo.userId
	});
	if (data) {
		checkInPopup.value.open('reviewed')
		return
	} else {
		if (isTravelPage) {
			// 使用 Tool.goPage 进行跳转
			Tool.goPage.push(`/pages/comment/postEvaluation?goodName=${props.scenicName}&scenicId=${scenicId}&type=3`);
		} else {
			// 使用 Tool.goPage 进行跳转
			Tool.goPage.push(`/pages/comment/postEvaluation?goodName=${props.scenicName}&scenicId=${scenicId}`);
		}
	}
};

// 跳转到门票列表页面
const goToTicketList = () => {
	// 使用 Tool.goPage 进行跳转
	Tool.goPage.push(`/pages/scenic/scenic?scenicId=${scenicId}`);
};

// 展开商家回复
const expandReply = (reply) => {
	// 切换回复展开状态
	reply.expanded = !reply.expanded;

	// 如果展开，增加最大高度
	if (reply.expanded) {
		reply.maxHeight = 'none';
	} else {
		reply.maxHeight = '102rpx';
	}
};

// 获取路由参数
let scenicId = '';
let storeId = '';
let isTravelPage = false
let travelGoodsId = ''
// 在页面加载时获取路由参数
onMounted(async () => {
	// 获取路由参数中的景区 ID
	const route = Tool.getRoute.params();
	console.log('route', route)
	if (route.travelGoodsId) {
		// 权益景区详情页
		isTravelPage = true
	}

	// 保存 travelGoodsId 以便用于拼接路由
	travelGoodsId = route.travelGoodsId || ''

	scenicId = route.scenicId || '';
	storeId = route.storeId || '';
	// 获取景区评分概览和标签数据
	await getScenicRating();

	// 如果是独立页面且有初始标签，则设置该标签为选中状态
	if (props.isPage && props.initialTag) {
		const tagIndex = filterTags.findIndex(tag => tag.type === props.initialTag.type && tag.tagId === props.initialTag.tagId);
		if (tagIndex !== -1) {
			selectTag(tagIndex); // 这会触发加载评论
		} else {
			await loadComments(); // 未找到匹配标签，加载默认评论
		}
	} else {
		// 否则，正常加载评论
		await loadComments();
	}

	// 延迟获取排序栏位置，确保 DOM 渲染完成
	setTimeout(() => {
		if (!props.isPage) return;
		const query = uni.createSelectorQuery();
		query.select('#sort-options-placeholder').boundingClientRect();
		query.selectViewport().scrollOffset();
		query.exec((res) => {
			// res[0] 是 boundingClientRect 的结果，res[1] 是 scrollOffset 的结果
			if (res && res[0]) {
				const currentScrollTop = (res[1] && res[1].scrollTop) || 0;
				sortOptionsTop = currentScrollTop + res[0].top;
				console.log('排序栏位置已计算：', sortOptionsTop);

				// 在位置计算后，立即进行一次检查，以处理页面加载时已滚动的情况
				if (props.scrollTop > sortOptionsTop && sortOptionsTop > 0) {
					showStickyFilter.value = true;
				} else {
					showStickyFilter.value = false;
				}
			} else {
				console.warn('获取 #sort-options-placeholder 位置失败');
			}
		});
	}, 500);
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
	// 清除定时器
	if (scrollTimer) {
		clearTimeout(scrollTimer);
	}
});

// 检查标签是否超过两行
const checkTagsOverflow = () => {
	// 在小程序环境中获取标签容器的实际高度比较复杂
	// 这里使用标签数量作为判断依据
	// 移动端常见宽度下，一行大约能放 4 个标签
	needExpand.value = filterTags.length > 8;
};

// 切换排序类型
const changeSortType = (type) => {
	if (sortType.value === type) return;
	sortType.value = type;
	// 重置页码并重新加载评论
	commentState.page = 1;
	if (showStickyFilter.value) {
		loadComments().then(() => {
			scrollToTop();
		});
	}

};

const scrollToTop = () => {
	if (!props.isPage) return;
	// 延迟确保 DOM 更新
	setTimeout(() => {
		const query = uni.createSelectorQuery();
		query.select('#sort-options-placeholder').boundingClientRect();
		query.selectViewport().scrollOffset();
		query.exec((res) => {
			if (res && res[0] && res[1]) {
				const currentScrollTop = res[1].scrollTop;
				const elementTop = res[0].top;
				uni.pageScrollTo({
					scrollTop: currentScrollTop + elementTop,
					duration: 200,
				});
			}
		});
	}, 100);
};
const isVideo = (url) => {
	const videoExtensions = ["mp4", "mov", "avi", "wmv", "flv"];
	const ext = url.split(".").pop().toLowerCase();
	return videoExtensions.includes(ext);
};
// 统一处理评论加载逻辑
const fetchComments = async (isReset = false) => {
	if (commentState.loading) return;

	commentState.loading = true;

	// 重置页码或递增
	if (isReset) {
		commentState.page = 1;
	} else {
		commentState.page += 1;
	}

	try {
		// 当 isPage 为 true 时，加载更多评论，否则只加载少量评论
		const pageSize = commentState.pageSize;

		// 根据接口文档构建请求参数
		const params = {
			scenicId: scenicId,
			storeId: storeId,
			current: commentState.page,
			pageSize: pageSize,
			// 添加排序类型
			recommend: sortType.value === 'recommend' ? 1 : 0 // 推荐=1，最新=0
		};

		// 根据选中的标签类型添加筛选条件
		if (commentState.currentTagType) {
			switch (commentState.currentTagType) {
				case TAG_TYPES.POSITIVE:
				case TAG_TYPES.NEGATIVE:
					params.score = commentState.currentTagId;
					break;
				case TAG_TYPES.PHOTO:
					params.evaluationContent = 2; // 带图评价
					break;
				case TAG_TYPES.VIDEO:
					params.evaluationContent = 3; // 带视频评价
					break;
				case TAG_TYPES.CONSUMPTION:
					params.type = 1; // 消费后评价
					break;
				case TAG_TYPES.LABEL:
					params.labelId = commentState.currentTagId; // 标签 ID
					break;
				default:
					// 不添加额外筛选条件 (全部标签)
					break;
			}
		}

		// 权益景区详情页，添加参数
		if (isTravelPage) params.type = 3
		// 请求接口获取评论数据
		const { data } = await request.post('/comment/getCommentPage', params);
		console.log('评论数据：', data);

		const commentList = data.data || [];
		// 处理接口返回的数据
		if (commentList && commentList.length > 0) {
			// 将接口返回的数据格式转换为组件使用的格式
			const formattedComments = commentList.map(item => ({
				...item,
				userName: item.anonymous === 1 ? desensitization(item.nickname, 'name') : item.nickname || '游客',
				userAvatar: (item.anonymous !== 1 && item.profileUrl ? imgHost + item.profileUrl : defaultAvatar),
				score: item.score.toFixed(1),
				environmentScore: item.environmentScore.toFixed(1),
				funScore: item.interestScore.toFixed(1), // 趣味评分对应接口的 interestScore
				valueScore: item.serviceScore.toFixed(1), // 服务/性价比评分对应接口的 serviceScore
				content: item.comment || '',
				images: item.msgUrl.map(item => imgHost + item) || [],
				tags: item.labelId.map(item => tagEnum.value[item]),
				merchantCommentsList: item.merchantCommentsList || [],
				expanded: false,
				orderId: item.orderId
			}));

			// 是否重置列表或追加数据
			if (isReset) {
				commentState.comments = formattedComments;
			} else {
				commentState.comments = [...commentState.comments, ...formattedComments];
			}

			// 更新总数和是否还有更多
			commentState.total = data.total || 0;
			commentState.hasMore = commentState.total > commentState.comments.length;
		} else {
			// 如果返回数据为空
			if (isReset) {
				// 重置时清空数据
				commentState.comments = [];
				commentState.total = 0;
				commentState.hasMore = false;
			} else {
				// 加载更多时，没有新数据
				commentState.hasMore = false;
			}
		}
	} catch (err) {
		console.error(`${isReset ? '加载' : '加载更多'}评论失败`, err);

		if (isReset) {
			// 初次加载错误，清空数据
			commentState.comments = [];
			commentState.total = 0;
			commentState.hasMore = false;
		} else {
			// 加载更多出错，恢复页码
			commentState.page -= 1;
		}
	} finally {
		commentState.loading = false;
	}
};

// 加载评论（重置并加载第一页）
const loadComments = async () => {
	await fetchComments(true);
};

// 加载更多评论（追加下一页）
const loadMore = async () => {
	if (!commentState.hasMore) return;
	await fetchComments(false);
};

// 计算加载更多状态
const loadMoreStatus = computed(() => {
	if (commentState.loading) return 'loading';
	if (commentState.hasMore) return 'more';
	return 'noMore';
});

// 预览图片
const previewImage = (item, index) => {
	currentPreviewImages.value = item.images;
	currentPreviewIndex.value = index;

	// 设置用户信息以在预览中显示
	currentPreviewUserInfo.value = {
		userName: item.userName,
		score: item.score,
		scoreLevel: getScoreLevel(item.score),
		environmentScore: item.environmentScore,
		funScore: item.funScore,
		valueScore: item.valueScore,
		content: item.content
	};

	previewVisible.value = true;
};

// 跳转到评论详情页
const goToCommentDetail = (item) => {
	// 使用 Tool.goPage 进行跳转，传递必要参数
	Tool.goPage.push(`/pages/scenic/commentDetail?commentId=${item.id}&scenicName=${props.scenicName}&scenicId=${scenicId}`);
};
</script>

<style lang="scss" scoped>
.comment-list {
	padding: 28rpx 0 138rpx 30rpx;
	background-color: #fff;
	position: relative;

	// 标题栏
	.section-header {
		margin-left: 2rpx;
		width: 718rpx;
		margin-bottom: 38rpx;

		.header-title {
			margin: 6rpx 0 8rpx 0;

			.header-line {
				background-color: #4787FB;
				border-radius: 3rpx;
				width: 8rpx;
				height: 36rpx;
				margin-right: 10rpx;
			}

			.header-text {
				color: #14131F;
				font-size: 36rpx;
				font-weight: 500;
				line-height: 36rpx;
				white-space: nowrap;
			}
		}

		.write-review-btn {
			background-color: #349FFF;
			border-radius: 30rpx 0rpx 0rpx 30rpx;
			padding: 9rpx 21rpx;

			.write-icon {
				width: 24rpx;
				height: 24rpx;
				margin: 4rpx 4rpx 4rpx 0;
			}

			.write-text {
				color: #FFFFFF;
				font-size: 24rpx;
				text-align: center;
				line-height: 32rpx;
				white-space: nowrap;
			}
		}
	}

	// 评分总览
	.rating-overview {
		margin-right: 30rpx;
		padding: 14rpx 29rpx 18rpx 32rpx;
		background-color: #F5F7FA;
		border-radius: 16rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		flex-wrap: nowrap;
		font-family: PingFangSC, PingFang SC;

		.rating-overall {
			color: #349FFF;
			font-size: 40rpx;
			font-weight: 500;
			line-height: 56rpx;
			margin-top: 6rpx;
			flex: 0 0 auto;
		}

		.rating-info {
			margin: 6rpx 0 0 17rpx;
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			flex: 0 0 auto;

			.rating-count {
				color: #666;
				font-size: 22rpx;
				line-height: 30rpx;
				margin-top: 32rpx;
			}

			.rating-satisfied {
				color: #349FFF;
				font-size: 24rpx;
				font-weight: 500;
				line-height: 33rpx;
				margin-left: 10rpx;
			}
		}

		.rating-item {
			margin-left: 52rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			flex: 1;

			.rating-score {
				color: #349FFF;
				font-size: 24rpx;
				font-weight: 500;
				line-height: 33rpx;
				text-align: center;
			}

			.rating-label {
				color: #666;
				font-size: 22rpx;
				line-height: 30rpx;
				text-align: center;
				margin-top: 3rpx;
				white-space: nowrap;
			}
		}
	}

	// 标签筛选容器
	.filter-tags-container {
		position: relative;
		margin: 26rpx 30rpx 40rpx 0;
		width: 690rpx;
	}

	// 标签筛选
	.filter-tags {
		max-height: 118rpx; // 两行标签的高度
		overflow: hidden;
		width: 690rpx;
		flex-wrap: wrap;
		display: flex;
		gap: 20rpx;
		transition: max-height 0.3s ease;

		&.expanded {
			max-height: 500rpx; // 足够大的高度以显示所有标签
		}

		.tag-item {
			background-color: #F5F6F7;
			border-radius: 8rpx;
			height: 48rpx;
			padding: 6rpx 16rpx;
			display: flex;
			align-items: center;
			max-width: 300rpx; // 设置最大宽度
			overflow: hidden; // 超出隐藏

			&.tag-active {
				background-color: #349FFF;
			}

			.tag-text {
				color: #17181A;
				font-size: 24rpx;
				line-height: 36rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;

				&.tag-text-active {
					color: #FFFFFF;
				}
			}
		}
	}

	// 展开/收起箭头
	.expand-arrow {
		position: absolute;
		bottom: -50rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1;

		.arrow-icon {
			transition: transform 0.3s ease;

			&.is-expanded {
				transform: rotate(180deg);
			}
		}
	}

	// 排序选项
	.sort-options {
		margin: 20rpx 0 40rpx 0;

		.sort-option {
			font-size: 24rpx;
			line-height: 33rpx;
			padding-right: 20rpx;
			position: relative;
			color: #999;

			&.sort-option-active {
				color: #17181A;
				font-weight: 500;
			}
		}

		.arrow-icon {
			width: 40rpx;
			height: 30rpx;
			color: #349FFF;
			transition: transform 0.3s ease;
			transform-origin: center center;
			position: relative;
			top: 0;

			&.is-expanded {
				transform: rotate(180deg);
				top: -5rpx;
			}
		}

		&:active {
			opacity: 0.8;
		}
	}

	// 评论容器
	.comment-container {
		margin-right: 30rpx;
		padding-bottom: 36rpx;
		margin-bottom: 36rpx;
		border-bottom: 1rpx solid #E9E9E9;

		// 用户信息行
		.user-info-row {
			margin-right: 362rpx;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				flex: none;
			}

			.user-detail {
				margin: 0 0 1rpx 16rpx;

				.user-name {
					color: #17181A;
					font-size: 28rpx;
					font-weight: 500;
					letter-spacing: 0.245rpx;
					line-height: 40rpx;
					white-space: nowrap;
				}

				.comment-date {
					color: #666;
					font-size: 24rpx;
					letter-spacing: 0.21rpx;
					line-height: 33rpx;
					white-space: nowrap;
					margin-top: 6rpx;
				}
			}

			.order-divider {
				flex: none;
				background-color: #D8D8D8;
				width: 2rpx;
				height: 30rpx;
				border-radius: 2rpx;
				margin: 50rpx 0 0 8rpx;
			}

			.order-source {
				color: #666;
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin: 46rpx 0 0 8rpx;
			}
		}

		// 评分行
		.score-row {
			margin: 20rpx 0;

			.score-badge {
				background-color: #349FFF;
				border-radius: 10rpx 10rpx 0rpx 10rpx;
				padding: 4rpx 11rpx;

				.score-value {
					color: #FFFFFF;
					font-size: 24rpx;
					font-weight: 600;
					line-height: 24rpx;
					white-space: nowrap;
				}
			}

			.score-level {
				color: #14131F;
				font-size: 24rpx;
				font-weight: 500;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin-left: 6rpx;
			}

			.score-detail {
				color: #666;
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				white-space: nowrap;
				margin-left: 21rpx;
			}
		}

		// 评论标签
		.review-tags {
			margin: 20rpx 0;
			display: flex;
			flex-wrap: wrap;
			gap: 10rpx;

			.review-tag {
				background-color: #EDF7FF;
				border-radius: 8rpx;
				padding: 8rpx 15rpx 8rpx 16rpx;
				margin-right: 20rpx;
				max-width: 200rpx;
				overflow: hidden;

				.review-tag-text {
					color: #349FFF;
					font-size: 24rpx;
					letter-spacing: 0.21rpx;
					line-height: 32rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
				}
			}
		}

		// 评论内容
		.comment-content {
			margin-top: 20rpx;
			position: relative;

			.content-text {
				color: #14131F;
				font-size: 30rpx;
				letter-spacing: 0.263rpx;
				line-height: 44rpx;
				width: 690rpx;
				max-height: 220rpx;
				overflow: hidden;
				transition: max-height 0.3s ease;
				position: relative;
				word-break: break-all; // 添加自动换行，无论是否截断单词
				word-wrap: break-word; // 确保长单词也能换行
				white-space: normal; // 确保文本正常换行

				&.content-text-expanded {
					max-height: none;

					&::after {
						display: none;
					}
				}
			}

			.read-more-container {
				position: absolute;
				right: 10rpx;
				bottom: 0;
				display: flex;
				flex-direction: row;
				align-items: center;
				padding-left: 30rpx;
				z-index: 2;

				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					height: 100%;
					width: 100%;
					background: linear-gradient(to right, rgba(255, 255, 255, 0.9), #ffffff);
					z-index: -1;
				}

				.read-more {
					color: #1C78E9;
					font-size: 28rpx;
					font-weight: 500;
					letter-spacing: 0.245rpx;
					line-height: 40rpx;
					white-space: nowrap;
				}

				.content-arrow-icon {
					width: 40rpx;
					height: 40rpx;
					color: #1C78E9;
					transition: transform 0.3s ease;

					&.is-expanded {
						transform: rotate(180deg);
					}
				}
			}
		}

		// 评论图片
		.comment-images {
			position: relative;
			width: 690rpx;
			margin: 30rpx 30rpx 30rpx 0;
			display: flex;
			flex-direction: row;
			gap: 24rpx;

			.image-container {
				position: relative;
				width: 214rpx;
				height: 214rpx;
				border-radius: 8rpx;
				overflow: hidden;

				.side {
					position: relative;
					width: 100%;
					height: 100%;

					.video {
						position: absolute;
						width: 100%;
						height: 100%;
					}

					.inV {
						opacity: 0;
						position: absolute;
						z-index: 6;
						left: 0;
						right: 0;
						top: 0;
						bottom: 0;
					}
				}
			}

			.comment-image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}

			.more-images-count {
				position: absolute;
				bottom: 8rpx;
				right: 8rpx;
				background-color: rgba(0, 0, 0, 0.55);
				border-radius: 24rpx;
				padding: 3rpx 16rpx;

				.more-images-text {
					color: #FFFFFF;
					font-size: 24rpx;
					line-height: 33rpx;
					white-space: nowrap;
				}
			}
		}

		// 商家回复
		.merchant-reply {
			background-color: #F7F8FA;
			border-radius: 16rpx;
			margin: 34rpx 30rpx 34rpx 0;
			padding: 24rpx 22rpx 24rpx 30rpx;

			.merchant-reply-item {
				&+.merchant-reply-item {
					margin-top: 20rpx;
					padding-top: 20rpx;
					border-top: 1rpx dashed #E9E9E9;
				}
			}

			.reply-header {
				margin-right: 380rpx;

				.reply-title {
					color: #17181A;
					font-size: 24rpx;
					font-weight: 500;
					line-height: 33rpx;
					white-space: nowrap;
					margin-right: 10rpx;
				}

				.reply-date {
					color: #999;
					font-size: 24rpx;
					line-height: 33rpx;
					white-space: nowrap;
				}
			}

			.reply-content {
				margin-top: 11rpx;

				.reply-text-container {
					width: 638rpx;
					position: relative;

					.reply-text {
						color: #44474D;
						font-size: 24rpx;
						line-height: 34rpx;
						max-height: 102rpx;
						overflow: hidden;
						transition: max-height 0.3s ease;
					}

					.expand-btn-container {
						position: absolute;
						right: 0;
						bottom: 0;
						background-color: transparent;
						display: flex;
						flex-direction: row;
						align-items: center;
						padding-left: 30rpx;
						z-index: 2;

						&::before {
							content: '';
							position: absolute;
							left: 0;
							top: 0;
							height: 100%;
							width: 100%;
							background: linear-gradient(to right, rgb(247 248 250 / 81%), #ffffff);
							z-index: -1;
						}

						.expand-btn {
							color: #1C78E9;
							font-size: 24rpx;
							font-weight: 500;
							line-height: 33rpx;
							white-space: nowrap;
						}

						.expand-arrow-icon {
							width: 32rpx;
							height: 32rpx;
							color: #1C78E9;
							transition: transform 0.3s ease;

							&.is-expanded {
								transform: rotate(180deg);
							}
						}
					}
				}
			}
		}

		// 评论间分隔线
		.comment-divider {
			width: 690rpx;
			height: 1rpx;
			margin: 36rpx 30rpx 36rpx 0;
			background-color: #EEEEEE;
		}
	}

	// 查看全部评论链接
	.view-all-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin: 35rpx auto;

		.view-all {
			color: #14131F;
			font-size: 28rpx;
			letter-spacing: 0.245rpx;
			line-height: 40rpx;
			white-space: nowrap;
		}

		.arrow-right-icon {
			width: 26rpx;
			height: 26rpx;
			color: #14131F;
			margin-left: 6rpx;
		}
	}

	// 空评论状态
	.empty-comment {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;

		.empty-image {
			width: 420rpx;
		}

		.empty-text {
			font-weight: 400;
			font-size: 28rpx;
			color: #14131F;
		}
	}

	// 加载更多
	.load-more {
		padding: 20rpx 0;
		text-align: center;
	}

	// 评论列表为空时的提示
	.no-comments-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;

		.empty-image {
			width: 420rpx;
		}

		.empty-text {
			font-weight: 400;
			font-size: 28rpx;
			color: #14131F;
			margin-top: 20rpx;
		}
	}
}

// 工具类
.flex-row {
	display: flex;
	flex-direction: row;
}

.flex-col {
	display: flex;
	flex-direction: column;
}

.justify-between {
	justify-content: space-between;
}

// 底部固定按钮
.block_7 {
	background-color: rgba(255, 255, 255, 1);
	position: fixed;
	left: 0;
	bottom: 0;
	width: 750rpx;
	height: 120rpx;
	padding: 20rpx 30rpx 20rpx 30rpx;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 10;
	transition: transform 0.3s ease;

	&.hidden {
		transform: translateY(120rpx);
	}
}

.text-wrapper_15 {
	border-radius: 12rpx;
	border: 2rpx solid rgba(185, 185, 185, 0.5);
	padding: 16rpx 78rpx 16rpx 78rpx;
}

.text_38 {
	overflow-wrap: break-word;
	color: rgba(5, 5, 5, 1);
	font-size: 32rpx;
	letter-spacing: 1.2307692766189575rpx;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	text-align: center;
	white-space: nowrap;
	line-height: 44rpx;
}

.text-wrapper_16 {
	background-color: rgba(52, 159, 255, 1);
	border-radius: 12rpx;
	padding: 18rpx 133rpx 17rpx 134rpx;
}

.text_39 {
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 32rpx;
	letter-spacing: 1.2307692766189575rpx;
	font-family: PingFangSC-Semibold;
	font-weight: 600;
	text-align: center;
	white-space: nowrap;
	line-height: 45rpx;
}
</style>