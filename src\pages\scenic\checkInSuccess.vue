<template>
	<view class="check-in-success-page">
		<view class="header-section">
			<view class="header-nav">
				<text class="done-button" @click="handleDone">完成</text>
			</view>
			<view class="success-banner">
				<image class="success-icon" src="@/static/image/check-in/greed-right-icon.webp" />
				<text class="success-text">打卡成功</text>
			</view>
		</view>

		<view class="content-area">
			<view class="check-in-card check-in-card-top">
				<view class="card-inner">
					<image v-if="staticMapUrl && !uploadedImage" :src="staticMapUrl" class="map-container" mode="aspectFill" />
					<image v-if="uploadedImage" :src="uploadedImage" class="uploaded-image" mode="aspectFill"
						style="object-position: center;"></image>
					<view v-if="uploadedImage" class="delete-button" @click="handleDeleteImage">
						<image class="delete-icon" src="@/static/image/trash-can-icon.png" />
					</view>
					<view class="record-button-wrapper" v-if="!uploadedImage">
						<view class="record-button" @click="handleUploadImage">
							<image class="record-icon" src="@/static/image/check-in/camera-icon.webp" />
							<text class="record-text">记录打卡瞬间</text>
						</view>
					</view>
					<view class="card-content">
						<view class="check-in-summary">
							<text>我</text>&nbsp;
							<text class="highlight-year">{{ checkInData.createYear }}</text>&nbsp;
							<text>年打卡的第</text>&nbsp;
							<text class="highlight-count">{{ checkInData.number }}</text>
							<text>&nbsp;个<text v-if="checkInData.city">{{ checkInData.city }}的</text>地点</text>
						</view>
						<view class="location-info" v-if="checkInData.address">
							<view class="location-name-wrapper">
								<image class="location-icon" src="@/static/image/check-in-icon.webp" />
								<text class="location-name">{{ checkInData.name }}</text>
							</view>
							<text v-if="checkInData.score" class="location-rating">{{ checkInData.score }}分</text>
						</view>
					</view>
				</view>

			</view>
			<view class="check-in-card-middle">
				<view class="divider"></view>
				<img class="card-middle-image" src="@/static/image/check-in/card-middle.webp" />
			</view>
			<view class="check-in-card check-in-card-bottom">
				<view class="user-info">
					<image class="user-avatar" :src="imgHost + checkInData.userInfo.avatar" />
					<view class="user-details">
						<text class="user-name">{{ checkInData.userInfo.nickname }}</text>
						<text class="check-in-time">{{ checkInData.createTime }}</text>
					</view>
				</view>
			</view>

			<view class="actions-section">
				<view class="main-actions">
					<view class="action-button" @click="goToMyCheckIn">
						<image class="action-icon" src="@/static/image/check-in/location-icon.webp" />
						<text class="action-text">查看打卡记录</text>
					</view>
					<view class="action-button share" @click="handleShareClick">
						<image class="action-icon" src="@/static/image/check-in/share-icon.webp" />
						<text class="action-text">分享打卡</text>
					</view>
				</view>
				<view v-if="!isCommented" class="review-section">
					<text class="review-prompt">写点评，帮助更多的人了解这里</text>
					<view class="rating-options">
						<view class="rating-item" v-for="item in ratings" :key="item.text"
							@click="goToPostEvaluation(item.starType)">
							<image class="rating-icon" :src="item.icon" />
							<text class="rating-text">{{ item.text }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 分享弹窗 -->
	<l-share-popup ref="sharePopupRef" :share-data="shareData" @share="onShareSuccess" @close="onShareClose" />
</template>

<script setup>
import {
	ref,
	onMounted,
	reactive,
	computed
} from 'vue';
import {
	onUnload,
	onLoad
} from '@dcloudio/uni-app';
import { Tool } from "@/utils/tools.ts";
import request from "@/utils/request.js";
import LSharePopup from './component/l-share-popup.vue';
import { getEnv } from "@/utils/getEnv";
// 删除 html2canvas 和 TMap 相关内容
// import html2canvas from 'html2canvas';

const { VITE_IMG_HOST } = getEnv();
const imgHost = ref(VITE_IMG_HOST)
const checkInData = reactive({
	scenicId: '',
	id: '',
	latitude: '',
	longitude: '',
	createTime: '',
	name: '',
	userInfo: {}
});
const mapScreenshot = ref('');

const getUserData = async () => {
	try {
		const userData = await Tool.getUserInfo();
		console.log(userData.userInfo)
		if (userData.userInfo) {
			checkInData.userInfo = userData.userInfo
		}
	} catch (e) {
		console.error('获取用户信息失败', e);
	}
};

const handleDone = () => {
	Tool.goPage.back();
};

// 跳转到我的打卡页面
const goToMyCheckIn = () => {
	Tool.goPage.push('/pages/my/myCheckIn');
};

// 跳转到评价页面
const goToPostEvaluation = (rating) => {
	Tool.goPage.push(`/pages/comment/postEvaluation?goodName=${encodeURIComponent(scenicName.value)}&scenicId=${checkInData.scenicId}&starType=${rating}`);
};

// 新增函数：更新打卡图片
const updateCheckInImage = async (imageUrl) => {
	if (!checkInData.id) {
		console.error('打卡 ID 不存在，无法更新图片');
		uni.showToast({
			title: '操作失败，打卡 ID 不存在',
			icon: 'none'
		});
		return;
	}
	try {
		await request.post('/comment/updateTClockIn', {
			id: checkInData.id,
			msgUrl: imageUrl
		});
	} catch (error) {
		console.error('更新打卡图片失败', error);
		uni.showToast({
			title: '操作失败',
			icon: 'none'
		});
	}
};

// 上传的图片 URL
const uploadedImage = ref('');

// 处理图片上传
const handleUploadImage = () => {
	Tool.imageUpload.chooseAndUpload({
		success: (path) => {
			// 获取完整的图片 URL
			const imgUrl = path.startsWith('http') ? path : VITE_IMG_HOST + path;
			uploadedImage.value = imgUrl;
			updateCheckInImage(path);
		},
		fail: (err) => {
			console.error('图片上传失败', err);
			uni.showToast({
				title: '图片上传失败',
				icon: 'none'
			});
		}
	});
};

// 处理图片删除
const handleDeleteImage = () => {
	uploadedImage.value = '';
	updateCheckInImage('');
};

const ratings = ref([{
	icon: '/static/image/face_icon/01.png',
	text: '不佳',
	starType: 0
},
{
	icon: '/static/image/face_icon/02.png',
	text: '一般',
	starType: 1
},
{
	icon: '/static/image/face_icon/03.png',
	text: '不错',
	starType: 2
},
{
	icon: '/static/image/face_icon/04.png',
	text: '满意',
	starType: 3
},
{
	icon: '/static/image/face_icon/05.png',
	text: '超棒',
	starType: 4
},
]);

const scenicName = ref('');

// 获取用户打卡信息
const getCheckInInfo = async () => {
	try {
		const { data } = await request.post(`/comment/tClockIn/${checkInData.userInfo.userId}`);
		console.log(data)
		checkInData.id = data.id;
		checkInData.createTime = dayjs(data.createTime).format('YYYY-MM-DD HH:mm')
		checkInData.city = data.city
		checkInData.address = data.address
		checkInData.createYear = data.createYear
		checkInData.number = data.number
		checkInData.name = data.name
		checkInData.score = data.score ? data.score.toFixed(1) : ''
		if (data.msgUrl) {
			uploadedImage.value = data.msgUrl.startsWith('http') ? data.msgUrl : imgHost.value + data.msgUrl;
		}
	} catch (err) {
		console.error('获取景点信息失败', err);
	}
};

// 新增静态图 URL
const staticMapUrl = computed(() => {
	if (!checkInData.latitude || !checkInData.longitude) return '';
	const key = 'OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77'; // 替换为你的腾讯地图 key
	const size = '600*400'; // 可根据实际容器大小调整
	const zoom = 12;
	const center = `${checkInData.latitude},${checkInData.longitude}`;
	const icon = 'https%3A%2F%2Fyeahtrip.com%2Fmaintenance%2Fdeepfile%2Fdata%2F2025-07-16%2Fupload_bb839474aad84ec1c3bb29f47eca7cd2.png'
	const markers = `icon:${icon}|${checkInData.latitude},${checkInData.longitude}`;
	return `https://apis.map.qq.com/ws/staticmap/v2/?center=${center}&zoom=${zoom}&size=${size}&markers=${markers}&key=${key}`;
});

// 判断当天是否已点评
const isCommented = ref(true);
const checkIsCommented = async () => {
	const res = await request.post('/comment/canLabel', {
		scenicId: checkInData.scenicId,
		userId: checkInData.userInfo.userId
	})
	isCommented.value = res.data
}

// 分享弹窗引用
const sharePopupRef = ref(null);

// 分享数据
const shareData = computed(() => {
	// 创建一个普通的 JavaScript 对象副本
	const checkInDataObject = { ...checkInData };

	return {
		title: `我在${scenicName.value || '景点'}打卡成功了！`,
		description: `我在${checkInData.city || ''}的${scenicName.value || '景点'}打卡成功，这是我的第${checkInData.number || 1}次打卡，快来看看吧！`,
		image: uploadedImage.value || staticMapUrl.value || '',
		url: `${window.location.origin}/pages/scenic/detail?id=${checkInData.scenicId}`,
		checkInData: checkInDataObject,
		userInfo: checkInData.userInfo,
	}
});

// 处理分享按钮点击
const handleShareClick = () => {
	if (sharePopupRef.value) {
		sharePopupRef.value.open();
	}
};

// 分享成功回调
const onShareSuccess = (data) => {
	console.log('分享成功', data);
	uni.showToast({
		title: '分享成功',
		icon: 'success'
	});
};

// 分享关闭回调
const onShareClose = () => {
	console.log('分享弹窗关闭');
};

onLoad(async () => {
	await getUserData();
	// 使用 Tool.getRoute.params() 获取 URL 参数
	const params = Tool.getRoute.params();
	checkInData.latitude = params.latitude || 0;
	checkInData.longitude = params.longitude || 0;
	checkInData.scenicId = params.id || '';

	getCheckInInfo();
	if (checkInData.scenicId) {
		checkIsCommented()
	}
});

onMounted(() => {
	// #ifdef H5
	// 删除 TMap 相关的 initMap、loadMap、loadScript、onMounted 中的 loadScript、onUnload 中的 map.destroy 等
	// #endif
});

onUnload(() => {
	// 删除 TMap 相关的 initMap、loadMap、loadScript、onMounted 中的 loadScript、onUnload 中的 map.destroy 等
});
</script>

<style lang="scss" scoped>
.check-in-success-page {
	background: linear-gradient(180deg, rgba(209, 232, 255, 0.8) 0%, #F5F6F8 100%);
	background-size: 100% auto;
	background-position: top center;
	background-repeat: no-repeat;
	width: 100%;
	min-height: 100vh;
}

.header-section {
	padding: 25rpx 30rpx 28rpx;
	display: flex;
	flex-direction: column;
}

.header-nav {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 31rpx;

	.done-button {
		color: #17181a;
		font-size: 32rpx;
		font-family: PingFangSC-Medium;
		font-weight: 500;
		line-height: 45rpx;
	}
}

.success-banner {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;

	.success-icon {
		width: 42rpx;
		height: 42rpx;
	}

	.success-text {
		color: #17181a;
		font-size: 32rpx;
		font-family: PingFangSC-Medium;
		font-weight: 500;
		line-height: 44rpx;
	}
}

.content-area {
	padding: 0 30rpx;
	z-index: 1;
}

.check-in-card {
	background: #ffffff;

	padding: 30rpx;
	display: flex;
	flex-direction: column;

	// background:
	// 		radial-gradient(700rpx at 0 840rpx, transparent 20rpx, #ffffff 20rpx),
	// 		radial-gradient(700rpx at 100% 840rpx, transparent 20rpx, #ffffff 20rpx),
	// 		#ffffff;
	&.check-in-card-top {
		border-radius: 16rpx 16rpx 0 0;
		// margin-bottom: 30rpx;
	}

	&.check-in-card-bottom {
		border-radius: 0 0 16rpx 16rpx;
		// margin-bottom: 30rpx;
	}

	.card-inner {
		background-size: cover;
		background-position: center;
		border-radius: 16rpx;
		padding-top: 570rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		overflow: hidden;
	}

	.map-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}

	.uploaded-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.delete-button {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.delete-icon {
		width: 30rpx;
		height: 30rpx;
	}

	.record-button-wrapper {
		position: absolute;
		top: 0;
		left: 50%;
		transform: translateX(-50%);
		margin-top: 322rpx;
	}

	.record-button {
		display: flex;
		align-items: center;
		gap: 12rpx;
		background-color: #349fff;
		border-radius: 34rpx;
		padding: 11rpx 22rpx;

		.record-icon {
			width: 46rpx;
			height: 46rpx;
		}

		.record-text {
			color: #fff;
			font-size: 28rpx;
			line-height: 40rpx;
		}
	}

	.card-content {
		background: linear-gradient(180deg, rgba(237, 237, 237, 0) 0%, rgba(241, 242, 245, 0.8) 27%, #F6F7FD 100%);
		background-size: 100% 100%;
		width: 100%;
		padding: 66rpx 30rpx 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		position: relative;
		z-index: 9;
	}

	.check-in-summary {
		font-size: 28rpx;
		font-family: PingFangSC-Medium;
		font-weight: 500;
		color: #17181a;
		line-height: 40rpx;

		.highlight-year,
		.highlight-count {
			font-size: 48rpx;
			font-family: Helvetica-Bold;
			font-weight: 700;
			color: #349fff;
		}
	}

	.location-info {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;

		.location-name-wrapper {
			display: flex;
			align-items: center;
			gap: 8rpx;
			min-width: 0;
		}

		.location-icon {
			width: 42rpx;
			height: 42rpx;
			flex: none;
		}

		.location-name {
			font-size: 28rpx;
			font-weight: 500;
			color: #17181a;
			width: 100%;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.location-rating {
			font-size: 26rpx;
			font-weight: 500;
			color: #17181a;
			flex: none;
		}
	}



	.user-info {
		display: flex;
		align-items: center;
		gap: 24rpx;

		.user-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		.user-details {
			display: flex;
			flex-direction: column;
			gap: 4rpx;
		}

		.user-name {
			font-size: 30rpx;
			font-weight: 500;
			color: #14131f;
		}

		.check-in-time {
			font-size: 28rpx;
			color: #666;
		}
	}
}

.check-in-card-middle {
	height: 52rpx;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;

	// border-radius: 0 0 16rpx 16rpx;
	// margin-bottom: 30rpx;
	.card-middle-image {
		width: 100%;
		height: 100%;
	}

	.divider {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 88%;
		height: 2rpx;
		border-top: 2rpx dashed #CCCCCC;
		z-index: 1;
	}
}

.actions-section {
	padding: 36rpx 0;
	display: flex;
	flex-direction: column;
	gap: 48rpx;

	.main-actions {
		display: flex;
		justify-content: space-around;
	}

	.action-button {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 20rpx 47rpx;
		border-radius: 40rpx;
		background: #fff;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(136, 136, 136, 0.12);

		&.share .action-text {
			color: #349fff;
			font-weight: 500;
		}
	}

	.action-icon {
		width: 34rpx;
		height: 34rpx;
	}

	.action-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #14131f;
	}

	.review-section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 36rpx 43rpx 42rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 40rpx;
	}

	.review-prompt {
		font-size: 28rpx;
		font-weight: 500;
		color: #17181a;
	}

	.rating-options {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	.rating-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.rating-icon {
		width: 60rpx;
		height: 60rpx;
	}

	.rating-text {
		font-size: 28rpx;
		color: #14131f;
	}
}
</style>
