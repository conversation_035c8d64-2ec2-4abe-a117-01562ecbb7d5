<template>
	<y-nav-bar :backgroundColor="'#fff'" :fontColor="'#000'" backBtn solid>区块链交易存证</y-nav-bar>
  <view class="card">
    <view class="content">
      <view class="wrap" v-if="block.value">
        <view class="item">
          <span class="title">存证号</span>
          <span class="value">{{block.value.certId || '--'}}</span>
        </view>
        <view class="item">
          <span class="title">发行方区块链账号</span>
          <span class="value" v-for="(item,i) in block.value.fromAccount" :key="i">{{item || '--'}}</span>
        </view>
				<!-- <view class="item">
				  <span class="title">接收方区块链账号</span>
				  <span class="value" v-for="(item,i) in block.value.issuerAccount" :key="i">{{item || '--'}}</span>
				</view> -->
				<view class="item">
				  <span class="title">存证内容</span>
				  <span class="value">{{block.value.certContent || '--'}}</span>
				</view>
        <view class="item">
          <span class="title">存证时间</span>
          <span class="value">{{block.value.savedAt || '--'}}</span>
        </view>
				<view class="item">
				  <span class="title">交易哈希</span>
				  <span class="value">{{block.value.txId || '--'}}</span>
				</view>
       <!-- <view class="item">
          <span class="title">藏品介绍</span>
          <Viewer class="value MDcontent" :value="'' || '--'"></Viewer>
        </view> -->
      </view>
      <view class="wrap_bg"><span></span></view>
      <view class="wrap_fg">
        <view class="top">
          <img class="img_1" src="@/static/image/card/1.png">
          <view class="line_box">
            <view class="line_1"></view>
            <img class="img_2" src="@/static/image/card/2.png">
          </view>
          <img class="img_3" src="@/static/image/card/3.png">
        </view>
        <view class="middle"></view>
        <view class="bottom">
          <view class="line_box">
            <img class="img_4" src="@/static/image/card/4.png">
            <view class="line_2"></view>
          </view>
          <view class="line_3 not2vw-line_3"></view>
        </view>
        <img class="logo" src="@/static/image/loading-dark-icon.png">
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive ,onMounted } from 'vue'
	import {onLoad } from "@dcloudio/uni-app"
	import request from '@/utils/request.js'


	const routerParams = reactive({});
	onLoad((option)=>{
		for(let key in option){routerParams[key]=option[key]}
	});
  const block= reactive({});
	const init = async () =>{
		try{
			const {data} = await request.get(`/order/certInfo/${routerParams.certId || '1666247751489704414912922884'}`)
			block.value=data
		}catch(err){
			console.log(err);
		}
	}

	onMounted(async ()=>{
		await init()
	});
  // const detailsId = useRoute().query.id
  // const detailsData = ref({})
  // apiMyCollectionsDetail(detailsId).then(res => {
  //   detailsData.value = res.data
  // })
</script>

<style lang="scss" scoped>
  .card {
    background: linear-gradient(180deg, #8FE6FF 0%, #76E2B5 100%);
    padding: 40rpx 30rpx;
		height: calc(100% - 3rem);
		overflow-y: scroll;
    .content {
      width: 100%;
      background-size: cover;
      padding: 120rpx 32rpx 142rpx 40rpx;
      position: relative;
      .wrap {
        width: 100%;
				position: relative;
				z-index: 1;
        min-height: calc(100vh - 350rpx);
        border: 2px solid #8BFF56;
        padding: 30rpx;
        color: #fff;
        background: rgba(12, 12, 12, 0.77);
        .item {
          display: flex;
          flex-direction: column;
          padding: 16rpx 0;
          .title {
            font-size: 26rpx;
            opacity: .8;
          }
          .value {
            margin: 10rpx 0;
            font-size: 30rpx;
            font-weight: bold;
            opacity: .9;
            word-break: break-all;
          }
        }
      }
      .wrap_bg {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        background: url("@/static/image/card/0.png") no-repeat center;
        background-size: cover;
        clip-path: polygon(0 60rpx, 60rpx 0, 100% 0, 100% calc(100% - 60rpx), calc(100% - 60rpx) 100%, 0 100%);
        >span {
          display: block;
          width: 100%;
          height: 100%;
          background: rgba(8, 8, 8, 0.54);
        }
      }
      .wrap_fg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .top {
          display: flex;
          .img_1 {
            margin: 24rpx 6rpx 24rpx 50rpx;
            height: 14rpx;
          }
          .img_2 {
            margin-top: 20rpx;
            height: 30rpx;
          }
          .img_3 {
            margin: 10rpx 4rpx;
            height: 74rpx;
          }
          .line_box {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            .line_1 {
              margin-top: 24rpx;
              width: 100%;
              height: 2rpx;
              background: #8BFF56;
            }
          }
        }
        .middle {
          margin: 0 24rpx 0 auto;
          width: 2rpx;
          flex: 1;
          background: #8BFF56;
        }
        .bottom {
          display: flex;
          .line_box {
            margin-left: 44rpx;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-end;
            .img_4 {
              width: 96rpx;
            }
            .line_2 {
              margin: 22rpx 0 30rpx;
              width: 100%;
              height: 2rpx;
              background: #8BFF56;
            }
          }
          .line_3 {
            margin: 0 24rpx 30rpx 0;
            width: 40rpx;
            height: 40rpx;
            background: #8BFF56;
          }
          .not2vw-line_3 {
            clip-path: polygon(0 calc(100% - 2.828rpx), calc(100% - 2.828rpx) 0, 100% 0, 0 100%);
          }
        }
        .logo {
          width: 160rpx;
          position: absolute;
          bottom: 58rpx;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
</style>

<style lang="scss">
  .MDcontent {
    img {
      max-width: 100%;
    }
  }
</style>