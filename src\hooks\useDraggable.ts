// useDraggable.js
import { ref, onMounted } from "vue"

export default function useDraggable({ initPos, endCallback }) {
	const posX = ref(0)
	const posY = ref(0)
	let startX = 0
	let startY = 0
	// 屏幕的宽度（rpx）
	const width = 750
	const tutuWidth = (window.innerWidth / width) * 200
	const tutuHeight = (window.innerHeight / width) * 200

	const startDrag = event => {
		startX = event.touches[0].clientX - posX.value
		startY = event.touches[0].clientY - posY.value
	}

	const onDragging = event => {
		event.preventDefault()
		const x = event.touches[0].clientX - startX
		const y = event.touches[0].clientY - startY
		// 边界检测
		if (x < 0) {
			posX.value = 0
		} else if (x > window.innerWidth - tutuWidth) {
			posX.value = window.innerWidth - tutuWidth
		} else {
			posX.value = x
		}
		if (y < 0) {
			posY.value = 0
		} else if (y > window.innerHeight - tutuHeight) {
			posY.value = window.innerHeight - tutuHeight
		} else {
			posY.value = y
		}
		console.log(posX.value)
		console.log(posY.value)
	}
	const endDrag = () => {
		// 如果元素在屏幕的中间，将其移动到屏幕的边缘
		if (posX.value > (window.innerWidth - tutuWidth) / 2) {
			posX.value = window.innerWidth - tutuWidth
		} else {
			posX.value = 0
		}
		endCallback()
	}

	onMounted(() => {
		// 初始化位置
		if (initPos) {
			posX.value = initPos.x
			posY.value = initPos.y
		} else {
			posX.value = window.innerWidth - tutuWidth
			posY.value = window.innerHeight - tutuHeight
		}
	})

	return { posX, posY, startDrag, onDragging, endDrag }
}
