<template>
	<view class="tab-bar">
		<view v-for="(item, index) in tabList" :key="item" class="tab" :class="{ active: index == props.activeTab }"
			@tap="changeTab(index)">
			<view v-show="index === props.activeTab">
				<y-font-weight>{{ item }}</y-font-weight>
			</view>
			<view v-show="index !== props.activeTab">
				{{ item }}
			</view>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, watch } from "vue"
// 选中的 tab
const props = defineProps({
	activeTab: {
		type: Number,
		default: 0,
		required: true
	},
	tabList: {
		type: Array,
		default: () => ["景区门票", "组合套票", "权益卡"]
	}
})
// tabBar 列表
// const tabList = reactive(['景点门票', '组合套票', '权益卡']);
const emits = defineEmits(["tabClick"])
const changeTab = index => {
	props.activeTab = index

	emits("tabClick", index)
}

//修改 title
watch(
	() => props.activeTab,
	index => {
		uni.setNavigationBarTitle({
			title: props.tabList[index]
		})
	},
	{ immediate: true }
)
</script>
<style lang="scss" scoped>
.tab-bar {
	display: flex;
	align-items: center;
	margin: 30rpx 0;
	font-size: 30rpx;
	font-weight: 400;
	color: #0d0d0d;

	.tab {
		margin-right: 50rpx;
		transition: all 0.1s;

		&.active {
			font-size: 40rpx;
			color: var(--theme-color);
			font-weight: 500;
		}
	}
}
</style>
