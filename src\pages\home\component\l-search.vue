<!--
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-07-03 09:28:05
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-07-03 11:24:33
 * @FilePath: \shop\src\pages\home\component\l-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-box">
    <!-- 组件 -->
    <div
      class="search-panel"
      :style="{
        height: config.searchHeight + 'px',
      }"
      @click="toSearch"
    >
      <div
        class="s-input"
        :style="{
          borderRadius: config.borderType === 'quadrate' ? '0px' : '15px',
          justifyContent: config.textAlign === 'center' ? 'center' : 'start',
          backgroundColor: config.bgColor,
          color: config.textColor,
          border: `1px solid ${config.borderColor}`,
        }"
      >
        <y-svg class="s-input-icon" name="globalsearch" />
        搜索门票·景点·攻略·资讯
      </div>
      <!-- <div class="s-btn">搜索</div> -->
    </div>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import ySvg from "@/components/y-svg/y-svg.vue";
export default {
  components: { ySvg },
  name: "globalsearch",
  props: {
    config: Object,
  },
  methods: {
    toSearch() {
      sessionStorage.setItem("hotWordList", JSON.stringify(this.config.hotWordList));
      sessionStorage.setItem("hotWordType", JSON.stringify(this.config.hotWordType));
      Tool.goPage.push("/pages/gloabSearch/gloabSearch");
    },
  },
};
</script>

<style scoped lang="less">
.search-box {
  position: relative;
  padding: 7px 15px;
  background-color: #fff;
  .search-panel {
    height: 30px;
    display: flex;
    align-items: center;
    .s-input {
      padding-left: 10px;
      height: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      border-radius: 15px;
      .s-input-icon {
        margin-right: 5px;
        width: 20px;
        height: 20px;
      }
    }
    .s-btn {
      width: 40px;
      flex: none;
      text-align: right;
      color: #000;
    }
  }
}
</style>
