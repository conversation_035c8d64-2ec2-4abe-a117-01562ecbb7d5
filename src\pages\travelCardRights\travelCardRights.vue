<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>权益卡权益</y-nav-bar>
	<view class="travelCardRights">
		<view class="tab-bar">
			<view v-for="(item, index) in stase.tabList" :key="item" @tap="changeTab(index)">
				<view :class="index == stase.tabIndex ? 'tab active' : 'tab'">
					{{ item }}
					<view class="modality"></view>
				</view>
			</view>
		</view>
		<!-- 权益卡列表 -->
		<view v-for="(item, k) in stase.ticketList" :key="item.travelCardGoodsName" class="travelCard"
			:class="stase.tabIndex == 1 ? 'fliter' : ''" @tap="skip(item)">
			<view class="img">
				<image class="image" :src="imgHost + item.pictures.toString().split(',')[0]"></image>
			</view>
			<view class="content">
				<!-- <view class='info'>{{ticket.address}}</view> -->
				<view class="title">{{ item.travelCardGoodsName }}<br />
					<view class="deadline">有效期：{{
						`${item.validityBeginTime} 至 ${item.validityEndTime}`
					}}</view>
				</view>
				<view v-if="stase.tabIndex == 0" class="pic">{{ item.time }}</view>
			</view>
		</view>
		<y-empty v-if="stase.ticketList.length == 0" style="margin-top: 200rpx" type="scenic">暂无权益卡</y-empty>
	</view>
</template>

<script setup>
import { toRefs, reactive, ref, watch, onBeforeMount, onMounted } from "vue"
import { orderStatus } from "@/utils/constant.js"
import request from "@/utils/request.js"
import { formatTime, getRoute } from "@/utils/tool.js"
import { onLoad } from "@dcloudio/uni-app"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({})
const imgHost = ref(getEnv().VITE_IMG_HOST)
const stase = reactive({
	tabList: ["已生效", "已失效"],
	tabIndex: 0,
	ticketList: [] //权益卡数据
})

const changeTab = async index => {
	stase.tabIndex = index
	stase.ticketList = []
	if (index == 1) {
		await init(0)
	} else {
		await init(1)
	}
}
let userData = {}
onMounted(async () => {
	userData = await Tool.getUserInfo()
	if (!userData.realNameInfo?.idNumber) {
		uni.showModal({
			title: "当前登录用户未实名",
			confirmText: "去实名",
			success: obj => {
				if (obj.confirm) {
					Tool.goPage.push(`/pages/certification/certification`)
				}
				//  else if (obj.cancel) {
				// }
			}
		})
	}
	await init(1)
})
const skip = item => {
	if (stase.tabIndex == 0) {
		Tool.goPage.push(
			`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}`
		)
	}
}
const init = async number => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.get(
			`/rightsService/travelGoodInfoPageList`,
			{
				idCard: userData.realNameInfo.idNumber,
				isEnable: number,
				storeId: getRoute.params().storeId
			}
		)
		stase.ticketList = data.data
		if (number == 1) {
			stase.ticketList.map(n => {
				let time =
					(new Date(n.validityEndTime).getTime() - new Date().getTime()) /
					1000 /
					60 /
					60 /
					24
				console.log("time", time)
				if (time < 1) {
					n.time = "当天有效"
				} else {
					n.time = `剩余${Math.ceil(time)}天`
				}
			})
		}
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
</script>

<style lang="less" scoped>
.travelCardRights {
	background-color: F6F6F6;
	margin: 0 40rpx;
	height: 100vh;

	.fliter {
		filter: grayscale(100%) !important;
	}

	.tab-bar {
		display: flex;
		height: 120rpx;
		justify-content: space-around;
		align-items: center;

		.tab {
			color: #101010;
			font-size: 30rpx;

			.modality {
				margin-top: 6rpx;
				height: 103rpx;
				height: 10rpx;
				border-radius: 5rpx;
			}
		}

		.active {
			font-weight: 600;

			.modality {
				background-color: var(--theme-color);
			}
		}
	}

	.travelCard {
		--radius-size: 28rpx;
		margin: 30rpx 0;
		width: 100%;
		overflow: hidden;
		height: 466rpx;
		border-radius: var(--radius-size);
		background-color: #fff;
		display: flex;
		flex-direction: column;
		box-shadow: 0rpx 2rpx 9rpx 3rpx rgba(195, 206, 218, 0.31);

		>.img {
			width: 100%;
			height: 340rpx;
			border-radius: var(--radius-size);
			overflow: hidden;

			.image {
				width: 100%;
				height: 100%;
			}
		}

		.content {
			flex: 1;
			display: flex;
			margin: 13.5rpx 0 6.5rpx 0;

			.title {
				font-size: 34rpx;
				font-weight: 600;
				line-height: 48rpx;
				padding: 0 0rpx 0 33rpx;
				flex: 1;

				.deadline {
					color: #050505;
					font-size: 26rpx;
					font-weight: 400;
				}
			}

			.info {
				padding-right: 7px;
				color: #868686;
				font-size: 26rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				flex: 1;
				line-height: 37rpx;
			}

			.pic {
				color: #f43636;
				font-size: 30rpx;
				font-weight: 600;
				padding-right: 30rpx;

				span {
					font-size: 48rpx;
					padding: 0 9rpx 0 0;
					font-weight: 600;
				}

				label {
					font-size: 26rpx;
					color: #6b6b6b;
				}
			}
		}
	}
}
</style>
