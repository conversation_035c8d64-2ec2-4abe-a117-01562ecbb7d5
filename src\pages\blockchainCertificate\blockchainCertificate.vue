<template>
  <view class="main">
    <view class="chain-bg">
      <view style="padding: 233rpx 44rpx 0 44rpx;">
        <view class="chain-info">
          <view class="info-title">区块信息</view>
          <view class="info-content">
            {{ chainInfo.fileVo.blockHeight }}
          </view>
        </view>
        <view class="chain-info">
          <view class="info-title">存证哈希</view>
          <view class="info-content">
            {{ chainInfo.fileVo.txId }}
          </view>
        </view>
        <view class="chain-info">
          <view class="info-title">存证时间</view>
          <view class="info-content">
            {{ chainInfo.fileVo.onChainTime }}
          </view>
        </view>
        <view class="chain-info">
          <view class="info-title">所在链</view>
          <view class="info-content">
            {{ chainInfo.fileVo.chainName }}
          </view>
        </view>
        <view class="chain-data">
          <view class="data-title">发行方</view>
          <view class="data-content">{{ chainInfo.fileVo.issuerName }}</view>
        </view>
        <view class="chain-data">
          <view class="data-title">发行方统一社会信用代码</view>
          <view class="data-content">{{ chainInfo.fileVo.issuerCode }}</view>
        </view>
        <view class="chain-data">
          <view class="data-title">上传时间</view>
          <view class="data-content">{{ chainInfo.fileVo.uploadTime }}</view>
        </view>
      </view>
      <image class="chapter" :class="{ 'start-stamp': showStamp }" src="@/static/image/block-chain-icon.png"
        mode="scaleToFill" />
    </view>
    <view class="evidence-info">
      <view class="evidence-title">存证信息</view>
      <view class="evidence-conteit">
        <view class="evidence-item">
          <view class="item-left">存证时间</view>
          <view class="item-right">{{ chainInfo.onChainTime }}</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">交易哈希</view>
          <view class="item-right" style="color: #1890ff"
            @click="goLink(`${chainUrl}/#/tx_list?tx=${chainInfo.parentTxHash}`)">
            {{ Tool.hideMiddleChars(chainInfo.parentTxHash || '') }}
          </view>
          <!-- <view class="item-right">{{ Tool.hideMiddleChars(chainInfo.parentTxHash || '') }}</view> -->
        </view>
        <view class="evidence-item">
          <view class="item-left">证书哈希</view>
          <view class="item-right" style="color: #1890ff"
            @click="goLink(`${chainUrl}/#/tx_list?tx=${chainInfo.txHash}`)">
            {{ Tool.hideMiddleChars(chainInfo.txHash || '') }}
          </view>
        </view>
        <view view class=" evidence-item">
          <view class="item-left">区块信息</view>
          <view class="item-right">{{ chainInfo.blockHeight }}</view>
        </view>
        <!-- <view class="evidence-item">
          <view class="item-left">商品名称</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">入园时间</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">类型</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">订单编号</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">订单金额</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view>
        <view class="evidence-item">
          <view class="item-left">创建时间</view>
          <view class="item-right">sdfsdfsdfsdsf</view>
        </view> -->
      </view>

    </view>
    <view class="download">
      <view class="download-btn" @tap="handleDownload">下载证书</view>
    </view>
  </view>
</template>

<script setup>
import { getEnv } from "@/utils/getEnv";
const chainInfo = ref({
  blockInfo: 'SDFSFSFAF',
  hash: 'idh398948jnf93u493jcw943820ne934u0jf232fu2ecsbduwi39249294592',
  time: 'SDFSFSFAF',
  chain: 'SDFSFSFAF',
  fileVo: {},
  orderChainVoucherFile: {}
})
const showStamp = ref(false)
const chainUrl = getEnv().VITE_CHAIN_URL
const init = async () => {
  const { txId } = Tool.getRoute.params()
  console.log(txId);
  console.log(txId);
  console.log(txId);
  console.log(txId);

  const { data } = await request.get(
    `/blockChain/order/voucher/get`,
    {
      txId
    }
  )
  chainInfo.value = data
  // 延迟 500ms 后启动动画
  setTimeout(() => {
    showStamp.value = true
  }, 500)
}
const goLink = (url) => {
  window.open(url)
}
onMounted(() => {
  init()

})

const handleDownload = () => {
  const url = chainInfo.value?.orderChainVoucherFile?.accessUrl
  if (!url) {
    uni.showToast({
      title: '下载链接不存在',
      icon: 'none'
    })
    return
  }
  window.open(url)

  // uni.downloadFile({
  //   url: url,
  //   success: (res) => {
  //     if (res.statusCode === 200) {
  //       uni.saveFile({
  //         tempFilePath: res.tempFilePath,
  //         success: function (res) {
  //           uni.showToast({
  //             title: '证书已保存',
  //             icon: 'success'
  //           })
  //         },
  //         fail: function () {
  //           uni.showToast({
  //             title: '保存失败',
  //             icon: 'none'
  //           })
  //         }
  //       })
  //     }
  //   },
  //   fail: () => {
  //     uni.showToast({
  //       title: '下载失败',
  //       icon: 'none'
  //     })
  //   }
  // })
}
</script>

<style lang="scss" scoped>
.main {
  padding: 30rpx;
  padding-bottom: 180rpx;
  background: linear-gradient(180deg, #BBD9FF 0%, #EFF6FF 100%);

  .chain-bg {
    position: relative;
    width: 100%;
    background-image: url('@/static/image/chain-certificate.png');
    background-size: contain;
    background-repeat: no-repeat;
    height: 1400rpx;

    .chain-info {
      margin-bottom: 24rpx;

      .info-title {
        font-size: 30rpx;
        line-height: 30rpx;
        font-weight: bold;
        color: #14131F;
        margin-bottom: 20rpx;
      }

      .info-content {
        background-color: #F3F7FD;
        border: 1px solid #EDEDED;
        font-size: 28rpx;
        line-height: 32rpx;
        padding: 30rpx 20rpx;
        text-align: center;
        color: #14131F;
        word-wrap: break-word;
      }
    }

    .chain-data {
      margin-top: 36rpx;

      .data-title {
        font-size: 28rpx;
        line-height: 28rpx;
        color: #6E6E6E;
        letter-spacing: 2px;
        text-align: left;
        font-style: normal;
        margin-bottom: 20rpx;
      }

      .data-content {
        font-size: 28rx;
        line-height: 28rpx;
        color: #14131F;
        // line-height: 28rpx;
        text-align: left;
        font-style: normal;
      }
    }

    .chapter {
      position: absolute;
      bottom: 300rpx;
      right: 50rpx;
      width: 138rpx;
      height: 138rpx;
      transform-origin: 50% 50%;
      filter: drop-shadow(0 0 5rpx rgba(0, 0, 0, 0.3));
      opacity: 0; // 初始状态隐藏

      &.start-stamp {
        animation: stampDown 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        animation-fill-mode: forwards;
      }
    }
  }

  .evidence-info {
    background-color: white;
    border-radius: 12rpx;
    padding: 30rpx;

    .evidence-title {
      font-weight: 500;
      color: #14131F;
      font-size: 34rpx;
      border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
      padding-bottom: 30rpx;
    }

    .evidence-conteit {
      margin-top: 30rpx;

      .evidence-item {
        display: flex;
        font-size: 28rpx;
        line-height: 28rpx;
        text-align: left;
        margin-bottom: 20rpx;

        .item-left {
          color: #6E6E6E;
          flex: none;
          width: 120rpx;
          text-align: justify;
          text-align-last: justify;
          margin-right: 20rpx;
        }

        .item-right {
          color: #14131F;
        }
      }
    }
  }

  .download {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 140rpx;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

    .download-btn {
      background: #349FFF;
      color: white;
      width: 600rpx;
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 12rpx;
    }
  }
}

@keyframes stampDown {
  0% {
    opacity: 0;
    transform: translate3d(0, 0, 200rpx) scale(1.3) rotate(-5deg);
  }

  50% {
    opacity: 0.8;
    transform: translate3d(0, 0, 100rpx) scale(1.1) rotate(-2deg);
  }

  70% {
    opacity: 1;
    transform: translate3d(0, 0, 30rpx) scale(1.05) rotate(2deg);
  }

  85% {
    transform: translate3d(0, 0, 10rpx) scale(0.98) rotate(0deg);
  }

  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
  }
}
</style>
