<template>
  <view class="upload-img">
    <view class="img-list">
      <!-- 视频容器（始终在最前面） -->
      <view class="video-container" v-if="videoUrl">
        <!-- 视频预览模态框 -->
        <view v-if="videoPreviewVisible" class="video-preview-modal" @tap="closeVideoPreview">
          <video :src="currentVideo" class="preview-video" controls autoplay @tap.stop></video>
        </view>
        <!-- 视频元素（移除点击事件） -->
        <video v-else :src="videoUrl" :poster="videoPoster" class="video" :controls="false" :show-play-btn="false"
          :autoplay="false" :muted="true" :enable-play-gesture="false" object-fit="cover"></video>

        <!-- 播放按钮（新增） -->
        <view class="sidePlay" @tap.stop="previewVideo(videoUrl)">
          <!-- <image class="play-icon" src="@/static/image/play.png" mode="widthFix"></image> -->
        </view>

        <!-- 视频删除按钮 -->
        <image @tap="delVideo" v-if="props.upload" class="delete-icon" src="@/static/image/delete-icon.png"
          mode="widthFix"></image>

        <!-- 视频上传进度 -->
        <view v-if="videoUploading" class="upload-progress">
          <view class="progress-bar" :style="{ width: videoUploadProgress + '%' }"></view>
          <text class="progress-text">{{ videoUploadProgress }}%</text>
        </view>
      </view>

      <!-- 图片列表 -->
      <view class="img-item" v-for="(item, index) in imageList" :key="index">
        <!-- 图片元素 -->
        <image @tap="previewImage(index)" class="image" :src="item" mode="aspectFill"></image>

        <!-- 图片删除按钮 -->
        <image @tap="delImage(index)" v-if="props.upload" class="delete-icon" src="@/static/image/delete-icon.png"
          mode="widthFix"></image>

        <!-- 图片上传进度 -->
        <view v-if="imageUploading[index]" class="upload-progress">
          <view class="progress-bar" :style="{ width: imageUploadProgress[index] + '%' }"></view>
          <text class="progress-text">{{ imageUploadProgress[index] }}%</text>
        </view>
      </view>

      <!-- 上传视频按钮 -->
      <view v-if="showVideoBtn" style="margin-right: 24rpx" class="add-btn" @tap="chooseVideo">
        <div class="takePic">
          <img src="@/static/image/videoIcon.png" alt="" />
          <view class="ttt">上传视频</view>
        </div>
      </view>

      <!-- 上传图片按钮 -->
      <view v-if="showUploadBtn" class="add-btn" @tap="chooseImage">
        <div class="takePic">
          <img src="@/static/image/takePictures.png" alt="" />
          <view class="ttt">上传图片</view>
          <view class="ttt">（{{ imageList.length }} / {{ maxImages }}）</view>
        </div>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, toRefs, computed, watch } from "vue";
import { getEnv } from "@/utils/getEnv";
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  limit: {
    type: Number,
    default: 9,
  },
  upload: {
    type: Boolean,
    default: false,
  },
  newStyle: {
    type: Boolean,
    default: false,
  },
  showVideo: {
    type: Boolean,
    default: true,
  },
  videoMaxSize: {
    type: Number,
    default: 50,
  },
  videoMaxDuration: {
    type: Number,
    default: 60,
  },
});

const emits = defineEmits(["update:modelValue"]);

// 视频相关状态
const videoUrl = ref("");
const videoPreviewVisible = ref(false);
const currentVideo = ref("");
const videoUploading = ref(false);
const videoUploadProgress = ref(0);

// 图片相关状态
const imageList = ref([]);
const imageUploading = ref({});
const imageUploadProgress = ref({});
const videoPoster = ref("");

// 图片最大数量（总限制减去视频占用的1个位置）
const maxImages = computed(() => props.limit);

// 显示上传视频按钮
const showVideoBtn = computed(() => {
  return props.showVideo && !videoUrl.value && props.upload && imageList.value.length < props.limit;
});

// 显示上传图片按钮
const showUploadBtn = computed(() => {
  return imageList.value.length < maxImages.value && props.upload;
});

const { VITE_UPLOAD_HOST, VITE_UPLOAD_HOST__DEV, VITE_IMG_HOST } = getEnv();
// 图片和视频域名
const imgHost = import.meta.env.PROD ? VITE_UPLOAD_HOST : VITE_UPLOAD_HOST__DEV;

// 判断是否为视频
const isVideo = (url) => {
  const videoExtensions = ["mp4", "mov", "avi", "wmv", "flv"];
  const ext = url.split(".").pop().toLowerCase();
  return videoExtensions.includes(ext);
};

// 初始化分离视频和图片
const initMedia = () => {
  if (!props.modelValue) {
    videoUrl.value = "";
    imageList.value = [];
    return;
  }

  const list = props.modelValue.split(",");
  // 分离视频和图片
  const videoItem = list.find((item) => isVideo(item));
  videoUrl.value = videoItem ? (videoItem.startsWith("http") ? videoItem : VITE_IMG_HOST + videoItem) : "";

  imageList.value = list.filter((item) => !isVideo(item)).map((item) => (item.startsWith("http") ? item : VITE_IMG_HOST + item));
};

// 更新modelValue
const updateModelValue = () => {
  // 合并视频和图片
  const videoPart = videoUrl.value ? [videoUrl.value.replace(VITE_IMG_HOST, "")] : [];
  const imageParts = imageList.value.map((img) => img.replace(VITE_IMG_HOST, ""));

  const newValue = [...videoPart, ...imageParts].join(",");
  emits("update:modelValue", newValue);
};

// 初始化
initMedia();
watch(() => props.modelValue, initMedia);

// 关闭视频预览
const closeVideoPreview = () => {
  videoPreviewVisible.value = false;
  currentVideo.value = "";
};

// 预览视频
const previewVideo = (url) => {
  currentVideo.value = url;
  videoPreviewVisible.value = true;
};

// 删除视频
const delVideo = () => {
  videoUrl.value = "";
  updateModelValue();
};

// 删除图片
const delImage = (index) => {
  imageList.value.splice(index, 1);
  delete imageUploading.value[index];
  delete imageUploadProgress.value[index];
  updateModelValue();
};

// 选择视频上传
const chooseVideo = () => {
  uni.chooseVideo({
    sourceType: ["album", "camera"],
    maxDuration: props.videoMaxDuration,
    camera: "back",
    success: (res) => {
      const tempFilePath = res.tempFilePath;
      const size = res.size / 1024 / 1024; // MB

      if (size > props.videoMaxSize) {
        uni.showToast({
          title: `视频大小不能超过${props.videoMaxSize}MB`,
          icon: "none",
        });
        return;
      }

      uploadVideo(tempFilePath);
    },
    fail: (err) => {
      console.log("选择视频失败", err);
      uni.showToast({
        title: "选择视频失败",
        icon: "none",
      });
    },
  });
};
const generateVideoPoster = (videoUrl) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.crossOrigin = "anonymous"; // 处理跨域问题
    video.src = videoUrl;

    // 当视频元数据加载完成
    video.addEventListener("loadedmetadata", () => {
      // 设置当前时间为0.1秒（避免首帧黑屏）
      video.currentTime = 0.1;
    });

    // 当视频跳转到指定时间
    video.addEventListener("seeked", () => {
      try {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // 设置canvas尺寸与视频相同
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // 绘制视频帧到canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 转换为DataURL (PNG格式)
        const dataUrl = canvas.toDataURL("image/png");
        resolve(dataUrl);
      } catch (e) {
        reject(e);
      }
    });

    // 错误处理
    video.addEventListener("error", (err) => {
      reject(new Error(`视频加载失败: ${err.message}`));
    });

    // 超时处理
    setTimeout(() => {
      reject(new Error("生成海报超时"));
    }, 5000);
  });
};
// 上传视频
const uploadVideo = (filePath) => {
  videoUploading.value = true;
  videoUploadProgress.value = 0;

  uni.showLoading({ mask: true, title: "视频上传中" });

  const uploadTask = uni.uploadFile({
    url: imgHost,
    filePath: filePath,
    name: "file",
    success: async (uploadFileRes) => {
      try {
        const resData = JSON.parse(uploadFileRes.data);
        if (resData && resData[0] && resData[0].path) {
          const videoPath = resData[0].path;
          const fullVideoUrl = VITE_IMG_HOST + videoPath;
          videoUrl.value = fullVideoUrl;
          // 新增：获取视频首帧作为封面图
          try {
            const poster = await generateVideoPoster(fullVideoUrl);
            videoPoster.value = poster;
          } catch (e) {
            console.error("生成视频海报失败:", e);
            videoPoster.value = ""; // 失败时清空海报
          }
          updateModelValue();
          uni.showToast({
            title: "视频上传成功",
            icon: "success",
          });
        } else {
          throw new Error("视频上传失败，返回格式不正确");
        }
      } catch (error) {
        console.error("解析视频上传结果失败", error);
        uni.showToast({
          title: "视频上传失败",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.log("上传视频失败", err);
      uni.showToast({
        title: "视频上传失败",
        icon: "none",
      });
    },
    complete: () => {
      videoUploading.value = false;
      videoUploadProgress.value = 0;
      uni.hideLoading();
    },
  });

  uploadTask.onProgressUpdate((res) => {
    videoUploadProgress.value = res.progress;
    uni.showLoading({ mask: true, title: `视频上传中 ${res.progress}%` });
  });
};

// 预览图片
const previewImage = (index) => {
  uni.previewImage({
    current: index,
    urls: imageList.value,
  });
};

// 选择图片上传
const chooseImage = () => {
  const count = maxImages.value - imageList.value.length;
  if (count <= 0) return;

  uni.chooseImage({
    count: count,
    sizeType: ["original", "compressed"],
    success: (res) => {
      res.tempFiles.forEach((file, i) => {
        const imgSize = file.size / 1024 / 1024;
        const oSize = 0.5; // 图片大小限制 (MB)
        const ocale = oSize / imgSize;
        const tempFilePath = res.tempFilePaths[i];
        const index = imageList.value.length;

        // 标记为上传中
        imageUploading.value[index] = true;
        imageUploadProgress.value[index] = 0;

        uni.showLoading({ mask: true, title: "图片压缩" });
        compressImage(tempFilePath, { quality: ocale }).then((compressedPath) => {
          uni.showLoading({ mask: true, title: "图片上传" });
          uni.uploadFile({
            url: imgHost,
            filePath: compressedPath,
            name: "file",
            success: async (uploadFileRes) => {
              const imgObj = JSON.parse(uploadFileRes.data)[0];
              imageList.value.push(VITE_IMG_HOST + imgObj.path);
              updateModelValue();
            },
            complete: () => {
              delete imageUploading.value[index];
              delete imageUploadProgress.value[index];
              uni.hideLoading();
            },
          });
        });
      });
    },
    fail: (err) => {
      console.log("选择图片上传失败", err);
    },
  });
};

// 图片分辨率压缩
const calcImageSize = (res, pixels) => {
  let imgW, imgH;
  imgW = res.width;
  imgH = res.height;

  let ratio;
  if ((ratio = (imgW * imgH) / pixels) > 1) {
    ratio = Math.sqrt(ratio);
    imgW = parseInt(imgW / ratio);
    imgH = parseInt(imgH / ratio);
  } else {
    ratio = 1;
  }

  return { imgW, imgH };
};

// 压缩图片
const compressImage = (imgUrl, options = {}) => {
  const MAX_PIXELS = 2000000;
  const MAX_QUALITY = 0.8;
  const IMG_TYPE = "jpg";
  const CANVAS_ID = "compress_canvas";
  const BASE_64 = false;

  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imgUrl,
      success: (res) => {
        let pixels = options.pixels || MAX_PIXELS;
        let quality = options.quality || MAX_QUALITY;
        let type = options.type || IMG_TYPE;

        let { imgW, imgH } = calcImageSize(res, pixels);
        let w = options.width || imgW;
        let h = options.height || imgH;

        // #ifdef H5
        type = type == "jpg" ? "jpeg" : type;
        let img = new Image();
        img.src = res.path;
        img.onload = () => {
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          canvas.width = w;
          canvas.height = h;
          ctx.drawImage(img, 0, 0, w, h);
          let base64 = canvas.toDataURL(`image/${type}`, quality);
          resolve(base64);
        };
        // #endif
      },
    });
  });
};
</script>

<style lang="scss" scoped>
/* 样式基本保持不变，只需确保视频容器和图片容器的样式一致 */
.upload-img {
  display: flex;
  flex-wrap: wrap;
}

.img-list {
  display: flex;
  flex-wrap: wrap;

  .video-container,
  .img-item {
    position: relative;
    margin-right: 20rpx;
    margin-bottom: 20rpx;

    .video,
    .image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 16rpx;
    }

    .video {
      background-color: #000;
    }

    .delete-icon {
      $w: 38rpx;
      position: absolute;
      top: (-$w, 2);
      right: (-$w, 1.5);
      width: $w;
      height: $w;
      z-index: 1;
    }

    .upload-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 20rpx;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 0 0 16rpx 16rpx;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        background-color: #007aff;
        transition: width 0.3s;
      }

      .progress-text {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        color: white;
        font-size: 18rpx;
        text-align: center;
        line-height: 20rpx;
      }
    }
  }
}

.sidePlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  z-index: 10;

  .play-icon {
    width: 60rpx;
    height: 60rpx;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5));
    /* 添加阴影提高可见性 */
  }
}

/* 调整视频样式 */
.video-container,
.img-item {
  position: relative;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  width: 145rpx;
  /* 固定容器尺寸 */
  height: 145rpx;
  /* 固定容器尺寸 */

  .video,
  .image {
    width: 100%;
    /* 填满容器 */
    height: 100%;
    /* 填满容器 */
    border-radius: 16rpx;
    object-fit: cover;
    /* 保持比例填充 */
  }
}

.video-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-video {
    width: 100%;
    max-width: 800px;
    max-height: 80vh;
  }
}

.add-btn {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  background: #f5f5f5;
  border-radius: 16rpx;

  .takePic {
    text-align: center;
    font-size: 24rpx;

    img {
      padding-top: 32rpx;
      width: 48rpx;
      height: 48rpx;
    }

    .ttt {
      color: #14131f;
      font-size: 24rpx;
    }
  }
}
</style>
