<template>
	<view class="main" :style="{
		marginLeft: conf.pageMargin,
		marginRight: conf.pageMargin
	}">
		<tab-control :tab-list="conf.groupList" :on-change="onTabChange">
			<scroll-view class="tickets" :scroll-x="conf.groupType === '0'" scroll-left="120" :show-scrollbar="false">
				<view :style="{
					display: conf.groupType === '0' ? 'block' : 'flex'
				}">
					<view class="ticket-item" v-for="(item, index) in ticketList.slice(0, conf.dataLength)" :key="item.goodsName"
						:style="{
							borderRadius: conf.borderRadius,
							marginLeft: index !== 0 ? conf.productMargin : 0,
							flexGrow: 0,
							flexShrink: 1,
							width: conf.groupType === '0' ? conf.w : '50%',
							height: conf.groupType === '0' ? 'auto' : 'auto'
						}" @click="onLink(item)">
						<view :style="{ paddingTop: conf.pt }">
							<image v-if="item.picUrl?.split(',')[0]" class="img" :src="imgHost + item.picUrl?.split(',')[0]"
								:mode="conf.aspectType" />
							<view v-else class="default-img">
								<image src="@/static/image/default-img.png" mode="widthFix" />
							</view>

							<view v-if="conf.priceSwitch" class="price">￥{{ item.salePrice }} <text style="font-size: 18rpx;">起</text>
							</view>
							<view v-if="conf.textSwitch" :style="{
								fontWeight: conf.textType,
								textAlign: conf.pointType
							}" :class="{
								name: true,
								composed: currentTabKey === tabList[1].key
							}">{{ item.goodsName }}</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</tab-control>
	</view>
</template>
<script lang="ts" setup>
import tabControl from "@/components/y-tab-control/y-tab-control.vue"
import request from "@/utils/request.js"
import { getRoute, pxToRpx } from "@/utils/tool.js"
import { defineComponent, onMounted, reactive, ref, computed } from "vue"
import { getEnv } from "@/utils/getEnv";


defineComponent({
	tabControl
})

const props = defineProps({
	config: {
		type: Object,
		default: () => ({
			setStyle: {
				imageList: []
			}
		})
	}
})

const conf = computed(() => {
	const style = {}
	const { config } = props

	// 文字样式
	if (config.textType === "bold") style.textType = "bold"
	// 图片比例
	if (config.scaleType === "0") {
		// 3/2
		style.w = "274rpx"
		style.pt = "66.67%"
	} else if (config.scaleType === "1") {
		// 1/1
		style.w = "240rpx"
		style.pt = "100%"
	} else if (config.scaleType === "3") {
		// 16/9
		style.w = "274rpx"
		style.pt = "56.25%"
	}
	// 图片填充
	if (config.aspectType === "fill") style.aspectType = "aspectFill" //填充
	if (config.aspectType === "fit") style.aspectType = "aspectFit" //留白
	// 图片圆角
	if (config.borderRadius)
		style.borderRadius = `${pxToRpx(config.borderRadius)}`
	// 商品间距
	if (config.productMargin)
		style.productMargin = `${pxToRpx(config.productMargin)}`
	// 页面间距
	if (config.pageMargin) style.pageMargin = `${pxToRpx(config.pageMargin)}`

	// 数据长度
	if (config.groupType === "1") {
		style.dataLength = 2
	} else {
		style.dataLength = 20
	}

	return {
		...config,
		...style
	}
})

const imgHost = getEnv().VITE_IMG_HOST

//初始化
const isIos = ref(false)

// tab 相关
const tabList = [
	{
		key: "1",
		label: "景区门票"
	},
	{
		key: "2",
		label: "组合套票"
	},
	{
		key: "3",
		label: "权益卡"
	}
]

const currentTabKey = ref(tabList[0].key)

//模拟请求到 ticket 列表
const ticketList = ref([])

const listMap = reactive({
	ticketList: [],
	composeList: [],
	travelCardList: []
})

//获取单票列表
const getTicket = async () => {
	try {
		let params = {
			// isTravelCardRights: false,
			isRecommend: true,
			storeId: getRoute.params().storeId
		}
		const { code, data } = await request.get(`/appScenic/ticketList`, params)
		console.log(data)
		listMap.ticketList = data || []
		ticketList.value = data || []
		console.log(ticketList.value)
	} catch (err) {
		console.log(err)
	}
}
//获取组合票
const getPackageTicket = async () => {
	try {
		let params = {
			search: {
				isRecommend: true
			},
			storeId: getRoute.params().storeId
		}
		const { code, data } = await request.post(
			`/appTicket/appComposeGoodsList`,
			params
		)
		console.log(code, data)
		const list = (data.data || []).map(i => ({
			goodsName: i.name,
			scenicName: i.scenicName || "",
			picUrl: i.picUrl,
			sellingPrice: i.storeGoodsPrice,
			storeGoodsId: i.storeGoodsId
		}))

		listMap.composeList = list || []
	} catch (err) {
		console.log(err)
	}
}
//获取权益卡
const getTravelCard = async () => {
	try {
		let params = {
			storeId: getRoute.params().storeId,
			isRecommend: true
		}
		const { code, data } = await request.get(
			`/appScenic/appTravelGoodsPageList`,
			params
		)
		const list = (data.data || data.list || []).map(
			({ goodsBaseInfo, travelCardUnitInfo }) => {
				return {
					goodsName: travelCardUnitInfo.goodsName,
					scenicName: travelCardUnitInfo.scenicName || "",
					picUrl: goodsBaseInfo.picUrl,
					sellingPrice: travelCardUnitInfo.salePrice,
					rightsId: travelCardUnitInfo.rightsId,
					storeGoodsId: travelCardUnitInfo.storeGoodsId
				}
			}
		)
		listMap.travelCardList = list
	} catch (err) {
		console.log(err)
	}
}
const allTicketList = ref([])
// 获取票列表
const getTicketList = async (gropuList = []) => {

	for (let i = 0; i < gropuList.length; i++) {
		const { goodsList } = gropuList[i]
		try {
			const { data } = await request.post(`/appTicket/designerGoodsList`, {
				storeGoodsIds: goodsList,
				storeId: getRoute.params().storeId
			})
			allTicketList.value[i] = data || []
			if (ticketList.value.length === 0 && i === 0) ticketList.value = data
		} catch (err) {
			console.log(err)
		}
	}
}

// 跳转对应详情页
const onLink = (item) => {
	const urls = {
		1: `/pages/scenic/scenic?storeGoodsId=${item.storeGoodsId}`,
		2: `/pages/book/book?storeGoodsId=${item.storeGoodsId}&orderType=single&timeShareId=0`,
		3: `/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}`
	}
	Tool.goPage.push(urls[item.dataType.toString()])


	// switch (currentTabKey.value) {
	// 	case tabList[0].key:
	// 		Tool.goPage.push(
	// 			`/pages/book/book?storeGoodsId=${item.storeGoodsId}&orderType=single&timeShareId=0`
	// 		)
	// 		break
	// 	case tabList[1].key:
	// 		Tool.goPage.push(`/pages/scenic/scenic?storeGoodsId=${item.storeGoodsId}`)

	// 		break

	// 	default:
	// 		Tool.goPage.push(
	// 			`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}`
	// 		)

	// 		break
	// }
}

// tab 回调
const onTabChange = async (e: string) => {
	ticketList.value = allTicketList.value[e]
	console.log(ticketList.value);
}


onMounted(() => {
	if (uni.getSystemInfoSync().platform == "ios") {
		isIos.value = true
	} else {
		isIos.value = false
	}
	getTicketList(props.config.groupList)
	// getTicket()
	// getPackageTicket()
	// getTravelCard()
})

// watchEffect(() => {

//   switch (currentTabKey.value) {
//     case '1':
//       ticketList.value = listMap.ticketList;
//       break;
//     case '2':
//       ticketList.value = listMap.composeList;
//       break;
//     case '3':
//       ticketList.value = listMap.travelCardList;

//       break;
//     default:
//       break;
//   }

// })
</script>
<style lang="scss" scoped>
.main {
	// padding: 0 30rpx;
}

.tickets {
	height: 100%;
	padding: 20rpx;
	white-space: nowrap;
	width: 100%;

	.ticket-item {
		display: inline-block;
		width: 274rpx;
		height: 154rpx;
		position: relative;
		overflow: hidden;
		// background-color: gray;

		.img {
			position: absolute;
			left: 0;
			top: 0;
		}

		.price {
			position: absolute;
			right: 0;
			top: 0;
			color: #f43636;
			min-width: 97rpx;
			text-align: center;
			font-size: 24rpx;
			height: 33rpx;
			line-height: 33rpx;
			background: linear-gradient(180deg, #ffcb85 0%, #efe6c9 100%);
			border-radius: 0 0 0 24rpx;
			padding: 0 8rpx;
		}

		.name {
			position: absolute;
			bottom: 0rpx;
			left: 0;
			color: white;
			font-size: 26rpx;
			padding-left: 20rpx;
			padding-right: 20rpx;
			width: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			text-wrap: nowrap;
			line-height: 63rpx;
			height: 63rpx;
			background: linear-gradient(180deg,
					rgba(0, 0, 0, 0) 0%,
					rgba(0, 0, 0, 0.35) 100%);
		}

		.composed {
			height: 63rpx;
			line-height: 63rpx;
			background: linear-gradient(180deg, #fc6072 0%, #ffa98b 100%);
			bottom: 0;
		}
	}

	.img {
		width: 100%;
		height: 100%;
	}

	.default-img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 100rpx;
		}
	}
}
</style>
