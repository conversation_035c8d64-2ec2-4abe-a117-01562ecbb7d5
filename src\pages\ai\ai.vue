<template>
  <div class="ai-page">
    <!-- 头部 -->
    <!-- <y-navbar title="AI 助手"></y-navbar> -->

    <div class="dialogue">
      <!-- 设置 -->
      <div class="dialogue__setting">
        <setting @open="(type) => (ssshow = !type)" @changeVoice="changeVoice" @clearDialogue="clearDialogue" />
      </div>

      <div class="dialogue-list">
        <scroll-view :scroll-top="scrollTop" scroll-y="true" lower-threshold="100"
          @scrolltolower="handleDLScrollTolower" @scroll="handleDLScroll" style="height: calc(100vh - 200rpx);">
          <div ref="dialogueRef" style="padding-bottom: 20rpx">
            <div class="ai-header-card">
              <div class="ai-header">
                <image class="ai-logo" src="@/static/image/ai/tutu-avatar.png" mode="widthFix" />
                <div class="ai-title">AI 景区助手{{ sceincName ? ` - ${sceincName}` : '' }}</div>
              </div>
            </div>
            <dialogue v-if="weatherInfo" :showTip="false" :content="weatherInfo" />
            <!-- 快捷输入 -->
            <div class="dialogue__skill">
              <div style="display: flex">
                <div v-for="(item, index) in skillList" :key="index">
                  <div v-for="(e, i) in item" :key="i" @click="onClickSkill(e)" class="dialogue__skill__item" :style="{
                    background: `url(${e.bg}) no-repeat`,
                    backgroundSize: 'contain',
                  }">
                    <div class="dialogue__skill__item-title">
                      {{ e.title }}
                      <image class="dialogue__skill__item-icon" :src="e.icon" mode="scaleToFill" />
                    </div>
                    <div class="dialogue__skill__item-content">
                      {{ e.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 历史对话 -->
            <div v-if="historyList.isShow">
              <dialogue v-for="(item, index) in historyList.list" :key="index" :content="item.content"
                :params="item.params" :isGoLine="true" :isMe="item.isMe" />
            </div>

            <div v-show="!historyList.isShow && historyList.list.length > 0" @click="showHistory" class="history-btn">
              点击查看历史对话
            </div>
            <div v-show="historyList.isShow && historyList.list.length > 0" class="history-btn">
              以上为历史对话
            </div>

            <!-- 聊天对话 -->
            <dialogue v-for="(item, index) in dialogueList" :key="index" :content="item.content" :isMe="item.isMe"
              :flagList="item.flagList" :type="item.type" :showTip="item.showTip" :params="item.params" :isGoLine="true"
              @on-submit="onClickXcgh" @show-scenic="showScenic" />
            <skillCom v-if="curSkill" :params="curSkill" @on-submit="onClickXcgh" />

            <!-- 录音输入 -->
            <dialogue style="margin-bottom: 10px" v-show="isRecording" :content="doalogueValue" :isMe="true"
              type="recording" />

            <!-- ai 思考中 -->
            <dialogue v-show="socketMsg.isThinking || socketMsg.message" :content="socketMsg.message" :showTip="false"
              :isThinking="socketMsg.isThinking" />
          </div>
        </scroll-view>
      </div>

      <!-- 推荐问题 -->
      <div v-if="false" class="dialogue__recommendation">
        <image class="dialogue__recommendation__clear" src="@/static/image/ai/clear-icon.png" mode="widthFix" />
        <scroll-view scroll-x="true" style="width: 100%">
          <div style="display: flex">
            <div class="dialogue__recommendation-item" v-for="(item, index) in recommendationList" :key="index">
              {{ item }}
            </div>
          </div>
        </scroll-view>
      </div>

      <!-- 输入区 -->
      <div class="chat-input-container">
        <chatBox ref="chatBoxRef" v-model="doalogueValue" @onRecord="onRecord" :socketMsg="socketMsg"
          @onSubmit="onSubmit" />
      </div>
    </div>

    <y-popup :title="scenicDetail.title" v-model="popModel">
      <div style="padding: 0 46rpx">
        <image v-if="scenicDetail.img" style="width: 100%; border-radius: 12rpx" :src="scenicDetail.img"
          mode="scaleToFill" />
        <div style="padding-bottom: 160rpx">
          {{ scenicDetail.content }}
        </div>
      </div>
    </y-popup>
  </div>
</template>

<script setup>
import useWebSocket from "@/hooks/useWebSocket.ts"
import request from "@/utils/request.js"
import { nextTick, onMounted, onUnmounted, reactive, ref } from "vue"
import chatBox from "./components/chatBox.vue"
import dialogue from "./components/dialogue.vue"
import setting from "./components/setting.vue"
import skillCom from "./components/skillList.vue"
import reqAi from "@/utils/reqAi.js"
import { getEnv } from "@/utils/getEnv";

// 导入图片资源
import jdtjBg from "@/static/image/ai/dktj-bg.webp"
import jdtjIcon from "@/static/image/ai/dktj-icon.webp"
import jqjsBg from "@/static/image/ai/jqjs-bg.webp"
import jqjsIcon from "@/static/image/ai/jqjs-icon.webp"
import lxghBg from "@/static/image/ai/lxgh-bg.png"
import lxghIcon from "@/static/image/ai/lxgh-icon.png"
import zstjBg from "@/static/image/ai/zstj-bg.png"
import zstjIcon from "@/static/image/ai/zstj-icon.png"
const baseAiPath = '/algorithm/total-product-microservices/api'
const scenicDetail = reactive({
  title: "",
  img: "",
  content: "",
});
const dialogueRef = ref(null);
const { VITE_AI_XCGH_URL, VITE_AI_BASE_URL, VITE_IMG_HOST } = getEnv();
const wsChatUrl = VITE_AI_XCGH_URL; // 聊天长连接 URL
const baseUrl = VITE_AI_BASE_URL;
const imgHost = VITE_IMG_HOST;
const ssshow = ref(true);
const props = defineProps({
  // 是否显示导览点位
  showTour: {
    type: Boolean,
    default: false,
  },
});

const popModel = ref(false);
let userId = "";
// 推荐问题列表
const recommendationList = ref([
  "景区介绍",
  "行程规划",
  "景点推荐",
  "住宿推荐",
]);
// 对话列表
const dialogueList = reactive([]);
// 历史对话
const historyList = reactive({
  isShow: false,
  list: [],
});
const showHistory = () => {
  historyList.isShow = true;
  onScrollBottom();
};
const isRecording = ref(false);
const onRecord = (isRecord) => {
  isRecording.value = isRecord;
  if (isRecord) {
    stopAudio();
  }
  onScrollBottom();
};

// 清空对话
const clearDialogue = async () => {
  uni.showLoading({
    title: "清空中",
  });
  await reqAi.delete(
    `${baseAiPath}/scenic_route_planning/history`,
    {
      user_id: userId,
    },
    {
      withOut: true,
    }
  );
  uni.showToast({
    title: "清空成功",
    icon: "success",
  });
  dialogueList.length = 0;
  historyList.list = [];
};

const showScenic = async (name) => {
  uni.showLoading({
    title: "加载中",
  });

  scenicDetail.title = name;
  scenicDetail.content = "";
  scenicDetail.img = "";

  reqAi
    .get(`${baseAiPath}/scenic_route_planning/location_url`, {
      location: name,
    })
    .then(({ data: imgUrl }) => {
      scenicDetail.img = imgUrl;
    });
  reqAi
    .get(`${baseAiPath}/scenic_route_planning/location_info`, {
      location: name,
    })
    .then(({ data }) => {
      scenicDetail.content = data;
      popModel.value = true;
    })
    .finally(() => {
      uni.hideLoading();
    });
};

const changeVoice = (isPlay) => {
  if (!isPlay) {
    stopAudio();
  }
};
const curSkill = ref(null);
// 技能列表
const skillList = ref([
  [
    {
      key: "jqjs",
      title: "景区介绍",
      icon: jqjsIcon,
      bg: jqjsBg,
      content: "探索景区，发现惊喜",
      skills: [
        "介绍深圳热门景点？",
        "介绍一下深圳欢乐谷的游玩攻略？",
        "请帮我科普一下深圳世界之窗的前世今生？",
      ],
    },
    // {
    //   key: "xcgh",
    //   title: "行程规划",
    //   icon: lxghIcon,
    //   bg: lxghBg,
    //   content: "多条路线任你挑",
    //   skills: [
    //     "深圳一人游，有哪些必去景点？",
    //     "对于深圳 3 日游，有哪些必去的景点和推荐行程安排？",
    //     "深圳 2 日游，有什么必去的路线？",
    //   ],
    // },
  ],
  [
    {
      key: "jdtj",
      title: "景点推荐",
      icon: jdtjIcon,
      bg: jdtjBg,
      content: "热门景点等你来",
      skills: [
        "深圳旅游，有哪些好玩的景点推荐？",
        "深圳一日游有哪些必去的网红打卡点？",
        "深圳两人出游可以去哪些地方？",
      ],
    },
    // {
    //   key: "zstj",
    //   title: "住宿推荐",
    //   icon: zstjIcon,
    //   bg: zstjBg,
    //   content: "性价比超高的酒店",
    //   skills: [
    //     "亲子出游，推荐一些适合小朋友的亲子酒店？",
    //     "推荐一些性价比超高的酒店？",
    //     "推荐深圳高人气酒店？",
    //   ],
    // },
  ],
]);
const scrollTop = ref(999999);

const doalogueValue = ref(""); // 输入框的内容
const lastScrollTop = ref()
const onScrollBottom = () => {
  // 滚动到底部
  nextTick().then(() => {
    scrollTop.value += 1;
  });
};

// 点击行程规划
const onClickXcgh = (text) => {
  if (typeof text === "string") {
    doalogueValue.value = text;
    dialogueList.pop();
    curSkill.value = null;
    onSubmit();
    onScrollBottom();
  } else {
    if (text.type === "closeTripPreference") {
      // 删除行程偏好
      dialogueList.forEach((item, index) => {
        if (item.type === "tripPreference") {
          dialogueList.splice(index, 1);
        }
      });
      onScrollBottom();
    }
  }
};

// 点击技能
const onClickSkill = (item) => {
  if (curSkill.value?.key === item.key) {
    curSkill.value = null;
  } else {
    curSkill.value = { ...item };
  }
  onScrollBottom();
};

// 发送对话
const onSubmit = (val) => {
  if (!doalogueValue.value) return;
  if (wsStatusChat.value !== "open") {
    uni.showToast({
      title: "连接中，请稍后",
      icon: "none",
    });
    return;
  }
  if (socketMsg.status === "start") {
    uni.showToast({
      title: "正在回复中，请稍后",
      icon: "none",
    });
    return;
  }

  stopAudio();
  if (socketMsg.message) {
    dialogueList.push({
      content: socketMsg.message,
      isMe: false,
    });
    onScrollBottom();
    socketMsg.message = "";
  }
  wsSendChat({
    question: doalogueValue.value,
    user_id: userId,
  });
  dialogueList.push({
    content: doalogueValue.value,
    isMe: true,
  });
  socketMsg.isThinking = true; // 开始思考
  doalogueValue.value = "";
  onScrollBottom();
};

const chatBoxRef = ref(null);
const { storeId, sceincName, scenicId } = Tool.getRoute.params();

const getArticles = async (flagList) => {
  const journeyPlaceNames = flagList
    .filter((item) => item.type === "journey")
    .map((item) => item.name);
  const { data: articleList } = await request.get(
    `/user/journey/article/suggest`,
    {
      journeyPlaceNames,
      storeId,
    }
  );
  const { data: goodsList } = await request.get(`/user/journey/goods/suggest`, {
    journeyPlaceNames,
    storeId,
  });
  articleList.forEach((item) => {
    item.pictureUrl = item.pictureUrl
      ? imgHost + item.pictureUrl.split(",")[0]
      : "";
  });
  goodsList.forEach((item) => {
    item.pictureUrl = item.pictureUrl
      ? imgHost + item.pictureUrl.split(",")[0]
      : "";
  });
  dialogueList.push({
    type: "articles",
    params: {
      articleList,
      goodsList,
    },
  });
  onScrollBottom();
};

// 用户是否滚动
const isScrollByUser = ref(false);
// tutu ws 消息回调
const socketMsg = reactive({
  playData: "",
  isThinking: false,
  message: "",
  status: "finish", // ws 状态 start finish replace
  flagList: []
});
const onSocketMsg = (msg) => {
  // 修改聊天记录最后一条
  // 开始传输
  if (msg.code === 200) {
    socketMsg.isThinking = true;
    socketMsg.message = "";
    socketMsg.status = "start";
    socketMsg.flagList = [];
    isScrollByUser.value = false;
    lastScrollTop.value = 0;
  }
  // 传输中
  if (msg.code === 201 && socketMsg.status === "start") {
    socketMsg.isThinking = false;
    socketMsg.message += msg.answer;
    getTTS(msg.answer);
    if (!isScrollByUser.value) {
      onScrollBottom();
    }
  }
  // 传输结束
  if (msg.code === 202 && ["start", "replace"].includes(socketMsg.status)) {
    getTTS("", "end");

    socketMsg.status = "finish"
    const p = {
      content: socketMsg.message,
      isMe: false,
      flagList: [...new Set(socketMsg.flagList)],
      showTip: true
    }
    if (socketMsg.flagList.length > 0 && socketMsg.flagList.some(e => e.type === "journey")) {
      p.params = {
        is_route: msg.is_route,
        topic_id: msg.topic_id,
        answer_round: msg.answer_round
      }
    } else {
      p.params = {
        is_route: msg.is_route,
      }
    }
    dialogueList.push(p)

    socketMsg.message = "";
    socketMsg.status = "";

    // 弹出行程规划弹窗
    if (msg.user_info_required) {
      dialogueList.push({
        type: "tripPreference",
        params: msg.user_info || {},
      });
      if (!isScrollByUser.value) {
        onScrollBottom();
      }
    }
    // 查询文章商品
    if (socketMsg.flagList.length > 0) getArticles(socketMsg.flagList);
  }
  // 替换点位
  if (msg.code === 209) {
    try {
      const { flag, result, type } = msg.ner_res
      if (!flag) return
      if (type === 0) {
        // 行程规划
        Object.values(result).forEach(list => {
          list.forEach(item => {
            socketMsg.flagList.push({
              type: 'journey',
              name: item
            })
          })
        })
      } else if (type === 1 && props.showTour) {
        // 景点导览
        const line_id = result.line_id
        Object.keys(result).forEach(key => {
          // 判断是否是点位
          if (key.indexOf('location_') !== -1) {
            const item = result[key]
            socketMsg.flagList.push({
              type: 'tour',
              name: item.name,
              point_id: item.point_id,
              line_id: line_id,
            })
          }
        })
        getApp().globalData.pointObj = result
      }
    } catch (error) {
      console.error(error)
    }
  }
  // 直接跳转
  if (msg.code === 213) {
    const { result } = msg.ner_res
    if (result.length > 0) {
      Tool.globalData.set("customPointList", result)
      // 添加提示
      uni.showToast({
        title: '正在跳转到导览页面',
        icon: 'none',
        duration: 1500
      })
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/tour/tour?guideId=${scenicId}`
        })
      }, 1500)
    }
  }
}

// 滚动到底部/右边，距离 50 时触发
const handleDLScrollTolower = () => {
  isScrollByUser.value = false
}
// 上次的 AI 对话高度
const lastOffsetHeight = ref(0);
// onScroll
const handleDLScroll = (event) => {
  if (socketMsg.status === "start") {
    // 当前滚动高度小于上次滚动高度，且对话框高度大于等于上次对话框高度
    if ((event.detail.scrollTop + 6 < lastScrollTop.value) && (dialogueRef.value.offsetHeight >= lastOffsetHeight.value)) {
      // 向上滑动了
      isScrollByUser.value = true
    }
    lastScrollTop.value = event.detail.scrollTop;
    lastOffsetHeight.value = dialogueRef.value.offsetHeight;
  }
}

// websocket
const {
  wsOpen: wsOpenChat,
  wsSend: wsSendChat,
  wsClose: wsCloseChat,
  wsStatus: wsStatusChat,
} = useWebSocket({
  url: wsChatUrl,
  onSocketMsg,
  onError: (err) => {
    socketMsg.isThinking = false;
    dialogueList.push({
      content: err.msg,
      isMe: false,
      showTip: false,
    });
  },
});

let audio = null;
let playList = [];
// 停止播放
const stopAudio = () => {
  audio?.pause();
  audio = null;
  playList = [];
};

// 播放语音
/**
 * status 字段含义
 * getTexting 生成文字中
 * texted 生成文字结束
 * getWaving 生成语音中
 * waved 生成语音结束
 * playing 播放中
 * played 播放结束
 */
const getTTS = async (msg, status) => {
  if (uni.getStorageSync("voice") === "close") return; // 语音开关关闭
  const index = playList.length > 0 ? playList.length - 1 : 0;
  // 消息长度逐步增加
  const mObj = {
    0: 50,
    1: 100,
    2: 200,
  };
  const maxLength = mObj[index] || 250;
  if (playList[index]?.msg?.length <= maxLength && status !== "end") {
    playList[index].msg += msg;
  } else {
    if (playList[index]?.msg) {
      playList[index].status = "texted";
      getWav(playList);
    }
    playList.push({
      msg: msg,
      wav: "",
      status: "getTexting",
    });
  }
};

const getWav = async (playList) => {
  // 请求语音合成
  if (playList.some((e) => e.status === "getWaving")) return; // 请求中
  const curItem = playList.find((e) => {
    if (e.status === "texted") {
      // 设置为请求中
      e.status = "getWaving";
      return true;
    } else {
      return false;
    }
  });
  if (!curItem) return;

  const { data, code } = await reqAi.post(`${baseAiPath}/dialogue/scenic-tts`, {
    message: curItem.msg,
    speaker: "神里绫人",
  });

  if (code !== 200) {
    curItem.status = "texted";
    getWav(playList);
    return;
  }
  curItem.wav = data;
  curItem.status = "getWaved";
  playAudio(playList);
  getWav(playList);
};

const playAudio = (playList) => {
  if (playList.some((e) => e.status === "playing")) return;
  const wavItem = playList.find((e) => e.status === "getWaved");

  if (wavItem) {
    wavItem.status = "playing";
    const wavData = wavItem.wav;
    const binary = atob(wavData);
    const buffer = new ArrayBuffer(binary.length);
    const view = new Uint8Array(buffer);
    for (let i = 0; i < binary.length; i++) {
      view[i] = binary.charCodeAt(i);
    }
    const blob = new Blob([buffer], { type: "audio/wav" });
    const url = URL.createObjectURL(blob);
    audio = new Audio();
    audio.src = url;
    audio.play();
    // 监听播放结束
    audio.onended = function () {
      wavItem.status = "played";
      playAudio(playList);
    };
  }
};

const weatherInfo = ref('')
// 获取天气
const getWeather = async () => {
  // 如果有景区名称则传参
  const { data } = await reqAi.post(`https://test-gcluster.shukeyun.com/algorithm/travel-assistant/start`, { scenicName: sceincName });
  weatherInfo.value = data[0].msg
}

// 页面加载时自动初始化
onMounted(async () => {
  // 打开 WebSocket 连接
  wsOpenChat();

  // 设置 cookie
  await setCookie();

  // 获取用户信息
  const { userInfo } = await Tool.getUserInfo();
  userId = userInfo.userId;

  // 获取历史记录
  getHistory();

  // 获取天气
  if (sceincName) getWeather();

  // 开发环境下监听键盘回车
  if (!import.meta.env.PROD) {
    document.addEventListener("keydown", onKeydown);
  }
});

// 监听回车键
const onKeydown = (e) => {
  if (e.keyCode === 13) {
    onSubmit();
  }
};

// 获取历史记录
const getHistory = async () => {
  const { data } = await reqAi.get(
    `${baseAiPath}/scenic_route_planning/history/${userId}`,
    {
      limit: 100,
    },
    {
      withOut: true,
    }
  );
  if (data.length > 0) {
    data.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const list = item[key]; // 记录从新到旧
        list.forEach((item, index) => {
          // 问题
          historyList.list.push({
            content: item.question,
            isMe: true,
          });
          // 答案
          historyList.list.push({
            content: item.answer,
            isMe: false,
            params: {
              is_route: item.is_route,
              topic_id: key,
              answer_round: index + 1,
            },
          });
        });
      });
    });
  }
  // 翻转数组，使最新的消息在最下面
  historyList.list.reverse();
};

// 设置 chat cookie
const setCookie = async () => {
  const cookie = await Tool.getCookie("Authorization");
  reqAi.post(`${baseAiPath}/scenic_route_planning/cas-token`, {
    token: cookie,
    app_id: localStorage.getItem("appId"),
  });
};

// 页面卸载时关闭连接
onUnmounted(() => {
  wsCloseChat();
  stopAudio();
});
</script>

<style lang="scss" scoped>
.ai-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: url('@/static/image/ai/chat-bg.webp') no-repeat;
  background-size: cover;

  .dialogue {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx 0;

    .dialogue__setting {
      position: absolute;
      top: 30rpx;
      right: 0;
      z-index: 10;
    }

    .dialogue-list {
      flex: 1;
      padding-top: 20rpx;
      margin-bottom: 120rpx;
      /* 为底部输入框留出空间 */
      overflow: hidden;
    }

    .dialogue__skill {
      margin-bottom: 20px;
      padding-top: 10rpx;

      .dialogue__skill__title {
        margin-bottom: 32rpx;
        font-size: 34rpx;

        .scroll-X {
          width: 100%;
          white-space: nowrap;
        }
      }

      .dialogue__skill__item {
        flex: none;
        width: 271rpx;
        margin-top: 20rpx;
        margin-right: 20rpx;
        padding: 42rpx 32rpx 40rpx;
        color: white;

        .dialogue__skill__item-title {
          display: flex;
          align-items: center;
          font-size: 34rpx;
        }

        .dialogue__skill__item-icon {
          width: 46rpx;
          height: 46rpx;
        }

        .dialogue__skill__item-content {
          margin-top: 23rpx;
          overflow: hidden;
          font-size: 28rpx;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .dialogue__recommendation {
      display: flex;
      align-items: center;
      margin: 30rpx 0;

      .dialogue__recommendation__clear {
        width: 65rpx;
        margin-right: 20rpx;
      }

      .dialogue__recommendation-item {
        display: flex;
        flex: none;
        align-items: center;
        height: 60rpx;
        margin-left: 20rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        background: #ffffff;
        border: 1px solid var(--theme-color);
        border-radius: 12px;
      }
    }

    .history-btn {
      margin-bottom: 20rpx;
      color: #8e8e8e;
      font-size: 24rpx;
      text-align: center;
    }

    .chat-input-container {
      position: fixed;
      bottom: 0;
      left: 30rpx;
      right: 30rpx;
      background: transparent;
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  .ai-header-card {
    background-color: transparent;
    padding: 20rpx 28rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;

    .ai-header {
      display: flex;
      align-items: center;

      .ai-logo {
        width: 100rpx;
        height: 100rpx;
        margin-right: 20rpx;
        position: relative;
      }

      .ai-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
}
</style>
