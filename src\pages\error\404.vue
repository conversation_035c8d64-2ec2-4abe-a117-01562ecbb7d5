<style lang="scss" scoped>
.error-mian {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.error-btn {
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		background-color: var(--theme-color);
		border-radius: 8rpx;
		width: 200rpx;
		height: 60rpx;
	}
}
</style>
<template>
	<view class="error-mian">
		<image src="@/static/image/404.png" mode="scaleToFill" />
		<view>非常抱歉，未能找到你所要的界面</view>
		<view class="error-btn" @click="onBackHome">返回首页</view>
	</view>
</template>

<script setup lang="ts">
	const onBackHome = () => {
		const redirectLoading = sessionStorage.getItem("redirectLoading")
		if (redirectLoading && redirectLoading.indexOf("/pages/loading/loading") === -1) {
			uni.redirectTo({
				url: redirectLoading
			})
		} else {
			const storeId = localStorage.getItem("storeId")
			Tool.goPage.replace(`/?storeId=${storeId}`) // 跳转 回原来的页面
		}
	};  
</script>
