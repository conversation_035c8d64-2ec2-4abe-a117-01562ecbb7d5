import { defineConfig, loadEnv } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import Components from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';
import { envMap } from './src/utils/getEnv';
import { fileURLToPath, URL } from 'node:url';

// import mkcert from "vite-plugin-mkcert"
// https://vitejs.dev/config/
export default defineConfig((config) => {
  const mode = config.mode;
  const { VITE_API_BASE_HOST, VITE_API_CAS_HOST, VITE_AI_BASE_URL, VITE_UPLOAD_HOST, VITE_API_EXCHANGE_HOST } = envMap[mode] || {};

  return {
    define: {
      'process.env.MODE': JSON.stringify(mode),
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    // plugins: [uni(), mkcert()],
    plugins: [
      // 自动导入本地组件
      Components({
        // 指定组件位置，默认是 src/components
        // dirs: ["src/components"],
        deep: true, //search for subdirectories
        // ui 库解析器
        // resolvers: [ElementPlusResolver()],
        extensions: ['vue'],
        // 配置文件生成位置
        dts: 'src/components.d.ts',
      }),
      AutoImport({
        imports: ['vue', 'uni-app', { dayjs: [['default', 'dayjs']] }],
        dirs: ['src/utils', 'src/hooks'],
        dts: 'src/auto-imports.d.ts',
        vueTemplate: true,
      }),
      uni(),
    ],
    build: {
      terserOptions: {
        compress: {
          drop_console: false,
        },
      },
    },
    server: {
      https: true,
      open: true,
      // port: 3000,
      proxy: {
        '^/uploadApi': {
          target: VITE_UPLOAD_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/uploadApi/, ''),
        },
        '^/baseApi': {
          target: VITE_API_BASE_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/baseApi/, ''),
        },
        '^/casApi': {
          target: VITE_API_CAS_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/casApi/, ''),
        },
        '^/fire/yms/api-map': {
          target: 'https://apis.map.qq.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/fire\/yms\/api-map/, ''),
        },
        '^/aiApi': {
          target: VITE_AI_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/aiApi/, ''),
        },
       '^/exchangeApi': {
          target: VITE_API_EXCHANGE_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/exchangeApi/, ''),
        },
      },
    },
  };
});
