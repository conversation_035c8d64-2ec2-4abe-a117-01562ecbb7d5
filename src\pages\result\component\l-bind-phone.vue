<template>
	<view class="new-phone-box">
		<view class="new-phone-title">请输入您需要绑定的新手机号</view>
		<view class="new-phone-input">
			<input
				type="number"
				:value="params.phone"
				@input="e => (params.phone = e.detail.value)"
				:placeholder-style="placeholderColor"
				placeholder="输入手机号" />
		</view>
		<view class="new-phone-input">
			<input
				type="number"
				:value="params.phoneCode"
				@input="e => (params.phoneCode = e.detail.value)"
				:placeholder-style="placeholderColor"
				placeholder="验证码" />
			<y-verification-code :mobile="params.phone" />
		</view>
	</view>
	<y-button :disable="disableBtn" @tap="submit">确定</y-button>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import request from "@/utils/request.js"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"

const placeholderColor = computed(() => {
	return { color: "#9397A8" }
})

const params = reactive({
	phone: "",
	phoneCode: ""
})

const disableBtn = computed(() => {
	return !params.phone || !params.phoneCode
})

const submit = async () => {
	uni.showLoading({
		title: "修改中"
	})
	// await request.casPut('/bind/phone',params)
	uni.navigateBack({
		delta: 1
	})
}
</script>
<style lang="scss" scoped>
.new-phone-box {
	display: flex;
	flex-direction: column;
	align-items: center;

	.new-phone-title {
		margin: 125rpx 0 40rpx;
		font-size: 34rpx;
		font-weight: 400;
		color: #14131f;
	}
	.new-phone-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 36rpx;
		margin-bottom: 30rpx;
		width: 630rpx;
		height: 96rpx;
		background: #f5f5f5;
		// border-radius: 25rpx;
		@include theme-border-radius_type();
	}
}
</style>
