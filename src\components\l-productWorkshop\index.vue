<!--
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-07-30 13:52:26
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-07-31 11:18:13
 * @FilePath: \interface-designer-frontend\src\components\componentscom\productWorkshop\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="productWorkshop">
    <img src="@/assets/images/productlink.png" alt="" />
  </div>
</template>
<script>
export default {
  name: 'productWorkshop',
  props: {
    datas: Object,
  },
  data() {},
}
</script>
<style scoped lang="less">
.productWorkshop {
  text-align: center;
  padding: 12px;
  background: #fff;
  img {
    width: 100%;
  }
}
</style>
