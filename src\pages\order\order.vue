<template>
  <!-- <y-nav-bar backgroundColor="#7ED3DA" solid>订单</y-nav-bar> -->
  <view class="order">
    <tabBar @changeTab="changeTab" />
    <scroll-view scroll-y style="flex: 1; height: 0" @scrolltolower="loadMore">
      <view style="padding-bottom: 120rpx">
        <template v-if="lists.length !== 0">
          <orderItem v-for="(n, i) in lists" :order="n" :key="i" />
          <y-loadmore :status="loadStatus" theme="light"></y-loadmore>
        </template>
        <template v-else>
          <y-empty style="margin-top: 100rpx" type="order">暂无订单</y-empty>
        </template>
      </view>
    </scroll-view>
  </view>
  <view class="safe-area-inset-bottom"></view>
  <uni-popup ref="popup" :mask-click="false" mask-background-color="rgba(0,0,0,0.4)" type="center">
    <view class="commentB">
      <view class="topB"></view>
      <img src="@/static/image/face_icon/commentSuccess.png" alt="" />
      <view class="com_A">点评成功</view>
      <view class="com_B">感谢你的评价，我们会继续为你提供更优质的</view>
      <view class="com_C" @click="toCommentDetail">查看点评</view>
    </view>
    <view class="icon">
      <uni-icons type="close" size="32" color="#D8D8D8" @click="close"></uni-icons>
    </view>
  </uni-popup>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import tabBar from "./component/orderTabBar.vue";
import orderItem from "./component/orderItem.vue";
import request from "@/utils/request.js";
import { getRoute } from "@/utils/tool.js";
const emits = defineEmits(["open"]);

const page = reactive({
  current: 1,
  size: 10,
});
const tabType = ref("");
const lists = ref([]);
const loadStatus = ref("loadmore");
const hasNextPage = ref(true);

//重新请求页面
const getList = async (type) => {
  //是否有下一页
  if (type === "next" && !hasNextPage.value) {
    return;
  }

  switch (type) {
    case "init":
      page.current = 1;
      hasNextPage.value = true;
      break;
    case "next":
      page.current += 1;
      break;
  }

  loadStatus.value = "loading";
  uni.showLoading({
    title: "易旅宝",
    mask: true,
  });

  try {
    // 获取系统来源
    const sourceType = Tool.getSystemSource();

    let params = {
      current: page.current,
      pageSize: page.size,
      sourceType,
      storeId: getRoute.params().storeId,
    };

    // 根据 tab 类型设置查询参数
    if (tabType.value == "unpaid") {
      params.orderStatus = 20;
    }
    if (tabType.value == "completed") {
      params.orderStatus = 30;
      params.waitUseYn = true;
    }
    if (tabType.value == "sale") {
      params.afterSalesYn = true;
    }
    if (tabType.value == "comment") {
      params.commentYn = true;
    }

    const { data } = await request.get("/order/orderGroupList", params);

    if (type === "init") {
      lists.value = data.records || [];
      hasNextPage.value = data.hasNext || data.total > data.records.length;
    } else if (type === "next") {
      if (data.records && data.records.length > 0) {
        lists.value = [...lists.value, ...data.records];
      }
      hasNextPage.value = data.hasNext || data.total > lists.value.length;
    }

    // 加载状态
    if (hasNextPage.value) {
      loadStatus.value = "loadmore";
    } else {
      loadStatus.value = "nomore";
    }
  } catch (err) {
    console.error("获取订单列表失败：", err);
    loadStatus.value = "loadmore";
    hasNextPage.value = false;
  }

  uni.hideLoading();
};

const toCommentDetail = () => {
  const res = JSON.parse(sessionStorage.getItem("tabData"));
  const { commentId, scenicName, scenicId, storeId } = res;
  sessionStorage.setItem("tabData", "");
  close()
  Tool.goPage.push(`/pages/scenic/commentDetail?commentId=${commentId}&scenicName=${scenicName}&scenicId=${scenicId}&storeId=${storeId}`);
};

//改变 tab
const changeTab = (type) => {
  tabType.value = type;
  getList("init");
};

const popup = ref(null);
const open = () => {
  popup.value.open();
  emits("open", true);
};
const close = () => {
  sessionStorage.setItem("tabData", "");
  popup.value.close();
  emits("open", false);
};

//加载更多
const loadMore = () => {
  getList("next");
};

defineExpose({
  loadMore,
});

onMounted(() => {
  getList("init");
  if (sessionStorage.getItem("tabData")) {
    const res = JSON.parse(sessionStorage.getItem("tabData"));
    if (res.commentType == "1") {
      open();
    }
  }
});
</script>

<style lang="scss" scoped>
.order {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f1f1f1;
}
.commentB {
  width: 540rpx;
  height: 654rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  text-align: center;
  background: #fff;
  // position: relative;
  .topB {
    border-radius: 16rpx;
    // position: absolute;
    height: 60rpx;
    // left: 0;
    // right: 0;
    background: linear-gradient(180deg, #e3edfe 0%, #ffffff 100%);
  }
  img {
    width: 284rpx;
    height: 256rpx;
  }
  .com_A {
    font-size: 36rpx;
    margin: 24rpx 0 16rpx;
    font-weight: 700;
  }
  .com_B {
    color: #666666;
    width: 376rpx;
    margin: 0 auto;
  }
  .com_C {
    margin: 40rpx auto 0;
    width: 360rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 16rpx;
    border: 2rpx solid #b9b9b9;
  }
}
.icon {
  text-align: center;
  margin-top: 20rpx;
}
</style>
