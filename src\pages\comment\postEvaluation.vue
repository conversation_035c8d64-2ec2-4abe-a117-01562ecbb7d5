<!--
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-06-18 18:28:09
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-07-25 17:26:11
 * @FilePath: \shop\src\pages\comment\PostEvaluation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="comment">
    <view class="card">
      <view class="faceBox">
        <view class="faceItem" v-for="(item, index) in data.faceList" :key="index" @click="data.activeNow = index">
          <image class="face_img" :src="filterImgUrl(index)" mode="aspectFit" />
          <text :class="{ isActive: data.activeNow === index }">{{ item }}</text>
        </view>
      </view>
      <view class="starBox" v-if="data.activeNow !== undefined">
        <view class="starItem">
          <text class="starText">景色/环境</text>
          <view class="starShow">
            <view class="starAlign" v-for="(starItem, starIndex) in 5" :key="starIndex" @click="data.jinse = starIndex">
              <img class="starImg" src="@/static/image/face_icon/star.png" alt="" v-if="data.jinse < starIndex || data.jinse === undefined" />
              <img class="starImg" src="@/static/image/face_icon/star-active.png" alt="" v-else />
            </view>
          </view>
          <text class="statuT" v-if="data.jinse !== undefined">{{ data.faceList[data.jinse] }}</text>
        </view>
        <view class="starItem">
          <text class="starText">趣味</text>
          <view class="starShow">
            <view class="starAlign" v-for="(starItem, starIndex) in 5" :key="starIndex" @click="data.quwei = starIndex">
              <img class="starImg" src="@/static/image/face_icon/star.png" alt="" v-if="data.quwei < starIndex || data.quwei === undefined" />
              <img class="starImg" src="@/static/image/face_icon/star-active.png" alt="" v-else />
            </view>
          </view>
          <text class="statuT" v-if="data.quwei !== undefined">{{ data.faceList[data.quwei] }}</text>
        </view>
        <view class="starItem">
          <text class="starText">服务/性价比</text>
          <view class="starShow">
            <view class="starAlign" v-for="(starItem, starIndex) in 5" :key="starIndex" @click="data.fuw = starIndex">
              <img class="starImg" src="@/static/image/face_icon/star.png" alt="" v-if="data.fuw < starIndex || data.fuw === undefined" />
              <img class="starImg" src="@/static/image/face_icon/star-active.png" alt="" v-else />
            </view>
          </view>
          <text class="statuT" v-if="data.fuw !== undefined">{{ data.faceList[data.fuw] }}</text>
        </view>
      </view>
    </view>
    <view class="card">
      <view class="title">点评内容</view>
      <textarea
        class="myTextarea"
        placeholder="有什么亮点和不足？写点评帮助其他旅行者吧"
        name=""
        id=""
        cols="30"
        rows="10"
        :maxlength="500"
        @input="onTextAreaChange"
        v-model="data.noteVal"
      ></textarea>
      <view class="showTextNum">
        <text>你的用心点评，需5字起哟</text> &nbsp;&nbsp;
        <text>{{ data.noteVal.replace(/ /g, "").length || 0 }}/500</text>
      </view>
      <div class="line"></div>
      <div class="tagList">
        <div class="tagItem" @click="showDrawer = true">
          <img src="@/static/image/face_icon/tag.png" alt="" />
          <text style="margin-left: 10rpx">添加标签</text>
        </div>
        <div class="tagItem hasColor" v-for="item in data.actTagList" :key="item.id" @click.stop="delTag(item.id, false)">
          <text>{{ item.label }}</text>
          <img class="x_min" src="@/static/image/face_icon/x_min.png" alt="" />
        </div>
      </div>
      <div class="line"></div>
      <view class="picture">
        <view class="upload">
          <y-uploadvideo :showVideo="true" :newStyle="true" upload v-model="data.picture" :limit="9" />
        </view>
      </view>
    </view>
    <view class="card">
      <view class="title">出游信息</view>
      <view class="txt_chu">出游类型（选填）</view>
      <view class="playTagBox tagList">
        <view :class="['tagItem', 'playTagItem', data.playTypeIndex === index ? 'playActive' : null]" v-for="(item, index) in data.playArr" :key="index" @click="choosePlay(index)">
          {{ item }}</view
        >
      </view>
      <view class="cell flex-c" @click="showTimeDr = true">
        <text>浏览时长（选填）</text>
        <view class="flex-c">
          <text>{{ data.timeArr[data.nowTime] }}</text>
          <uni-icons type="right" color="#999999" size="20" />
        </view>
      </view>
    </view>
    <view class="comment_nim flex-c">
      <div class="flex-c" @click="data.evaluateShow = !data.evaluateShow">
        <img src="@/static/image/face_icon/gou.png" alt="" v-if="data.evaluateShow" />
        <img src="@/static/image/face_icon/no-gou.png" alt="" v-else />
      </div>
      <text>匿名评价</text>
      <text class="nm_font">匿名将隐藏头像和昵称</text>
    </view>
    <view class="botBox">
      <view class="comment_nim flex-c lijie">
        <div class="flex-c" @click="data.receiveShow = !data.receiveShow">
          <img src="@/static/image/face_icon/gou.png" alt="" v-if="data.receiveShow" />
          <img src="@/static/image/face_icon/no-gou.png" alt="" v-else />
        </div>
        <text>我理解并接受</text>
        <text class="min_font" @click="toCommentRule">点评发布规则</text>
      </view>
      <view class="btn" @click="submit">提交</view>
    </view>

    <Drawer :visible="showDrawer" @update:visible="showDrawer = $event">
      <view class="drawer-content-custom">
        <view class="drawer-header flex-c">
          <view @click="showDrawer = false">
            <uni-icons type="closeempty" size="26" color="#999" />
          </view>
          <view class="drawer-title">选择标签</view>
          <view @click="confrimTags">确定</view>
        </view>
        <view class="tag-list">
          <view class="tag-item" v-for="(item, index) in data.tagList" :key="index" @click.stop="selectTag(item)">
            <span>{{ item.label }}</span>
            <uni-icons type="checkmarkempty" size="26" color="#349FFF" v-if="filterTag(item.id)" />
          </view>
        </view>
        <view class="tag-act">
          <span style="white-space: nowrap">已选({{ data.shortTagsList.length }}/3)：</span>
          <div class="tagItem hasColor" v-for="item in data.shortTagsList" :key="item.id" @click.stop="delTag(item.id)">
            <text style="white-space: nowrap">{{ item.label }}</text>
            <img class="x_min" src="@/static/image/face_icon/x_min.png" alt="" />
          </div>
        </view>
      </view>
    </Drawer>
    <Drawer :visible="showTimeDr" @update:visible="showTimeDr = $event">
      <view class="drawer-content-custom">
        <view class="timeDrawer">
          <view class="timeDrawer-title">选择浏览时长</view>
          <view class="timeDrawer-close" @click="showDrawer = false">
            <uni-icons type="closeempty" size="24" color="#999" />
          </view>
        </view>
        <view class="tag-list" style="height: 840rpx">
          <view class="tag-item" v-for="(val, key, index) in data.timeArr" :key="index" @click.stop="timeSelectTag(index)">
            <span>{{ val }}</span>
            <uni-icons type="checkmarkempty" size="24" color="#349FFF" v-if="data.nowTime === index" />
          </view>
        </view>
      </view>
    </Drawer>
    <uni-popup ref="backPopup" type="center" :mask-click="false">
      <view class="popup-modal">
        <!-- 标题 -->
        <view class="popup-title">确定要退出吗</view>
        <!-- 内容 -->
        <view class="popup-content"> 退出后点评将不会保存，你的评论对大家真的很重要 </view>
        <!-- 按钮区域 -->
        <view class="popup-buttons">
          <view class="popup-btn cancel-btn" @click="handleModalCancel"> 退出 </view>
          <view class="popup-btn confirm-btn" @click="handleModalConfirm"> 继续点评 </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import Drawer from "@/components/drawer/drawer.vue";
import { faceList, playArr, timeArr } from "@/utils/constant";
const showDrawer = ref(false);
const showTimeDr = ref(false);
const alertDialog = ref(false);
const backDialog = ref(null); // 返回确认弹窗的ref
const backPopup = ref(null);
const data = reactive({
  activeNow: undefined,
  jinse: undefined,
  quwei: undefined,
  fuw: undefined,
  noteVal: "",
  faceList: faceList,
  playArr: playArr,
  timeArr,
  picture: "",
  playTypeIndex: undefined,
  evaluateShow: false,
  receiveShow: false,
  avatar: "",
  nickname: "",
  nowTime: null,
  tagList: [],
  shortTagsList: [],
  actTagList: [],
});
const choosePlay = (i) => {
  if (data.playTypeIndex !== i) {
    data.playTypeIndex = i;
  } else {
    data.playTypeIndex = undefined;
  }
};
const selectTag = (tag) => {
  if (filterTag(tag.id)) {
    delTag(tag.id);
  } else if (data.shortTagsList.length < 3) {
    data.shortTagsList.push(tag);
  } else if (data.shortTagsList.length == 3) {
    uni.showToast({
      title: "最多添加3个标签！",
      icon: "error",
    });
  }
  // showDrawer.value = false;
};
const confrimTags = () => {
  data.actTagList = data.shortTagsList;
  showDrawer.value = false;
};
const timeSelectTag = (v) => {
  if (v == data.nowTime) {
    data.nowTime = null;
  } else {
    data.nowTime = v;
    showTimeDr.value = false;
  }
};
const toCommentRule = () => {
  Tool.goPage.push("/pages/comment/commentRules");
};
const filterTag = (id) => {
  return data.shortTagsList.find((item) => item.id === id);
};
const delTag = (id, isShort = true) => {
  if (isShort) {
    data.shortTagsList = data.shortTagsList.filter((item) => item.id !== id);
  } else {
    data.actTagList = data.actTagList.filter((item) => item.id !== id);
  }
};

watch(
  () => showDrawer.value,
  (v) => {
    if (v) {
      // 打开抽屉时禁止滚动
      document.body.style.overflow = "hidden";
      data.shortTagsList = [...data.actTagList];
    } else {
      // 关闭抽屉时恢复滚动
      document.body.style.overflow = "";
    }
  }
);
watch(
  () => showTimeDr.value,
  (v) => {
    if (v) {
      // 打开抽屉时禁止滚动
      document.body.style.overflow = "hidden";
    } else {
      // 关闭抽屉时恢复滚动
      document.body.style.overflow = "";
    }
  }
);
const getTags = async () => {
  const res = await request.get(`/comment/findByLabelList/${getRoute.params().storeId}`);
  if (res.code === 20000) {
    data.tagList = res.data;
  }
};

const filterImgUrl = (index) => {
  let str = "";
  if (data.activeNow !== undefined && data.activeNow === index) {
    str = "-active";
  }
  return `/static/image/face_icon/0${index + 1}${str}.png`;
};

const verification = () => {
  if (data.activeNow === undefined) {
    uni.showToast({
      title: "请给景区评分！",
      icon: "error",
    });
    return false;
  } else if (data.jinse === undefined) {
    uni.showToast({
      title: "请给景色/环境评分！",
      icon: "error",
    });
    return false;
  } else if (data.quwei === undefined) {
    uni.showToast({
      title: "请给趣味评分！",
      icon: "error",
    });
    return false;
  } else if (data.fuw === undefined) {
    uni.showToast({
      title: "请给服务/性价比评分！",
      icon: "error",
    });
    return false;
  } else if (data.noteVal.replace(/ /g, "").length === 0) {
    uni.showToast({
      title: "请输入点评内容！",
      icon: "error",
    });
    return false;
  } else if (data.noteVal.replace(/ /g, "").length < 5) {
    uni.showToast({
      title: "请至少点评5个字！",
      icon: "error",
    });
    return false;
  } else if (!data.receiveShow) {
    uni.showToast({
      title: "请理解并勾选点评发布规则",
      icon: "error",
    });
    return false;
  } else {
    return true;
  }
};

const isVideo = (url) => {
  const videoExtensions = ["mp4", "mov", "avi", "wmv", "flv"];
  const ext = url.split(".").pop().toLowerCase();
  return videoExtensions.includes(ext);
};

const submit = async () => {
  if (!verification()) {
    return;
  }
  let msgList = data.picture ? data.picture.split(",") : [];
  let params = {
    comment: data.noteVal,
    score: data.activeNow + 1,
    profileUrl: data.avatar,
    msgUrl: msgList,
    orderId: getRoute.params().orderId ? getRoute.params().orderId : null,
    environmentScore: data.jinse + 1,
    interestScore: data.quwei + 1,
    serviceScore: data.fuw + 1,
    labelId: data.actTagList.map((item) => item.id),
    travelType: data.playTypeIndex != undefined ? data.playTypeIndex + 1 : null,
    browseDuration: data.nowTime,
    anonymous: data.evaluateShow ? 1 : 0,
    commentType: data.receiveShow ? 1 : 0,
    pictureType: data.picture ? 1 : 0,
    videoType: msgList.length > 0 && isVideo(msgList[0]) ? 1 : 0,
    type: getRoute.params().type == "3" ? 3 : getRoute.params().orderId ? 1 : 2,
    goodsName: getRoute.params().goodName,
    storeId: getRoute.params().storeId,
    nickname: data.nickname,
    scenicId: getRoute.params().scenicId ? getRoute.params().scenicId : null,
    // scenicId: 1,
    // goodsId: getRoute.params().goodsId || 1486614082213478401,
  };
  const res = await request.post("/comment/addMessage", params);
  if (res.code === 20000) {
    const { scenicName, id } = res.data;
    sessionStorage.setItem(
      "tabData",
      JSON.stringify({
        commentType: 1,
        commentId: id,
        scenicName,
        scenicId: getRoute.params().scenicId,
      })
    );
    if (getRoute.params().type == "1") {
      Tool.goPage.reLaunch(`/?storeId=${getRoute.params().storeId}&curTab=order`);
    } else {
      data.activeNow = undefined;
      data.jinse = undefined;
      data.quwei = undefined;
      data.fuw = undefined;
      data.noteVal = "";
      data.actTagList = [];
      data.picture = "";
      data.playTypeIndex = undefined;
      data.nowTime = null;
      data.receiveShow = false;
      Tool.goPage.push(`/pages/scenic/commentDetail?commentId=${id}&scenicName=${scenicName}&scenicId=${getRoute.params().scenicId}&storeId=${getRoute.params().storeId}`);
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: "error",
    });
  }
};

const onTextAreaChange = (e) => {
  data.noteVal = e.detail.value;
};

// 继续点评（确认按钮）
const handleModalConfirm = () => {
  backPopup.value.close(); // 关闭popup
  history.pushState({ page: "" }, "", location.href);
};

// 退出（取消按钮）
const handleModalCancel = () => {
  backPopup.value.close(); // 关闭popup
  Tool.goPage.back();
};
onShow(() => {
  if (getRoute.params().scenicName) {
    nextTick(() => {
      window.document.title = getRoute.params().scenicName;
    });
  }
});

// 返回事件处理函数
const handleBack = (e) => {
  if (
    data.noteVal ||
    data.activeNow !== undefined ||
    data.jinse !== undefined ||
    data.quwei !== undefined ||
    data.fuw !== undefined ||
    data.actTagList.length > 0 ||
    data.picture ||
    data.playTypeIndex !== undefined ||
    data.nowTime
  ) {
    e.preventDefault();
    backPopup.value.open();
  } else {
    Tool.goPage.back();
  }
};
onLoad(() => {
  history.pushState({ page: "" }, "", location.href);
  window.addEventListener("popstate", handleBack);
});
onUnload(() => {
  window.removeEventListener("popstate", handleBack);
});

onMounted(async () => {
  if (getRoute.params().starType) {
    data.activeNow = +getRoute.params().starType;
  }
  getTags();
  const { userInfo } = await Tool.getUserInfo();
  try {
    data.nickname = userInfo.nickname;
    data.avatar = userInfo.avatar;
  } catch (error) {}
});
</script>
<style lang="scss" scoped>
.comment {
  padding: 30rpx;
  box-sizing: border-box;
  background: #f1f1f1;
  .card {
    background: #fff;
    border-radius: 12rpx;
    padding-bottom: 20rpx;
    margin-bottom: 30rpx;
    .faceBox {
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 190rpx;
      .faceItem {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .face_img {
          width: 60rpx;
          height: 60rpx;
        }
        .isActive {
          color: #ff9201;
        }
      }
    }
    .starBox {
      padding: 0 40rpx;
      box-sizing: border-box;
      .starItem {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        .starText {
          width: 140px;
        }
        .starShow {
          display: flex;
          gap: 20rpx;
          .starAlign {
            display: flex;
            align-items: center;
            .starImg {
              width: 38rpx;
              height: 36rpx;
            }
          }
        }
        .statuT {
          white-space: nowrap;
          margin-left: 32rpx;
          color: #ff9201;
        }
      }
    }
  }
  .title {
    padding: 30rpx;
    font-size: 32rpx;
    font-size: #14131f;
    font-weight: 700;
  }
  .myTextarea {
    padding: 0 30rpx !important;
    width: 100%;
    height: 300rpx;
    line-height: 46rpx;
  }
  .showTextNum {
    text-align: right;
    font-size: 24rpx;
    color: #c8cacc;
    margin-top: 18rpx;
    padding: 0 30rpx;
  }
  .line {
    width: 630rpx;
    height: 2rpx;
    margin: 16rpx auto;
    background: #f7f7f7;
  }
  .tagList {
    display: flex;
    flex-wrap: wrap;
    padding: 10rpx 30rpx;
    box-sizing: border-box;
    // justify-content: space-between;
    gap: 24rpx 22rpx;
  }
  .tagItem {
    display: flex;
    align-items: center;
    padding: 10rpx 13rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    font-size: 26rpx;
    // margin-bottom: 24rpx;
    position: relative;
    img {
      width: 32rpx;
      height: 32rpx;
    }
    .x_min {
      width: 22rpx;
      height: 22rpx;
      position: absolute;
      top: -5rpx;
      right: -8rpx;
    }
  }
  .hasColor {
    background: #edf7ff;
    color: #349fff;
    padding: 10rpx 18rpx;
  }
  .picture {
    padding: 6rpx 30rpx;
    .description {
      height: 30rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(20, 19, 31, 0.5);
      line-height: 30rpx;
    }
  }
  .txt_chu {
    padding: 0 30rpx;
  }
  .playTagBox {
    padding: 16rpx 30rpx;
    gap: 24rpx 16rpx;
    .playTagItem {
      background: #f1f1f1;
      padding: 12rpx 18rpx;
      font-size: 14px;
    }
    .playActive {
      background: #edf7ff !important;
      border: 1rpx solid #349fff;
    }
  }
  .cell {
    padding: 20rpx 30rpx;
    flex: 1;
    justify-content: space-between;
  }
  .comment_nim {
    margin-bottom: 240rpx;
    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 14rpx;
    }
    .nm_font {
      color: #999999;
      font-size: 24rpx;
      margin-left: 10rpx;
    }
    .min_font {
      color: #349fff;
      margin-left: 10rpx;
      font-size: 28rpx;
      text-decoration: underline;
    }
  }
  .botBox {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    height: 200rpx;
    z-index: 11;
    .lijie {
      padding: 24rpx 30rpx;
      box-sizing: border-box;
      margin-bottom: 0;
    }
    .btn {
      margin: 0 auto;
      width: 600rpx;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      background: #349fff;
      border-radius: 12rpx;
      font-size: 36rpx;
      color: #fff;
    }
  }
  .flex-c {
    display: flex;
    align-items: center;
  }
  .drawer-content-custom {
    height: 1116rpx;
    padding: 30rpx;
    // position: relative;

    .drawer-header {
      justify-content: space-between;

      .drawer-title {
        font-size: 36rpx;
        font-weight: 600;
      }
    }

    .tag-list {
      display: flex;
      flex-direction: column;
      // gap: 20rpx;
      margin-top: 20rpx;
      height: 811rpx;
      overflow-y: auto;

      .tag-item {
        padding: 32rpx 28rpx;
        background: #fff;
        font-size: 30rpx;
        color: #333;
        border-bottom: 2rpx solid #f5f6f7;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:active {
          background: #e6f7ff;
          color: #349fff;
        }
      }
    }
    .tag-act {
      display: flex;
      // flex-wrap: wrap;
      align-items: center;
      padding: 10rpx 30rpx;
      box-sizing: border-box;
      gap: 24rpx 22rpx;
      padding: 38rpx;
      box-shadow: 0rpx -3rpx 3rpx 0rpx rgba(226, 226, 226, 0.5);
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 99;
      background: #fff;
      overflow-x: auto;
    }
    .timeDrawer {
      text-align: center;
      position: relative;
      .timeDrawer-title {
        font-size: 36rpx;
        font-weight: 600;
      }
      .timeDrawer-close {
        position: absolute;
        right: 10rpx;
        top: 5rpx;
      }
    }
  }
  .popup-modal {
    width: 560rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    .popup-title {
      padding: 30rpx 0 10rpx;
      text-align: center;
      font-size: 34rpx;
      font-weight: bold;
      color: #14131f;
    }

    .popup-content {
      padding: 20rpx 30rpx 30rpx;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }

    .popup-buttons {
      display: flex;
      height: 98rpx;
      border-top: 1rpx solid #f1f1f1;
    }

    .popup-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
    }

    .cancel-btn {
      color: #666;
      border-right: 1rpx solid #f1f1f1;
    }

    .confirm-btn {
      color: #349fff;
    }
  }
}
</style>
