<!-- FilterMenu.vue -->
<template>
  <view class="filter-menu">
    <!-- Tab 切换栏 - 使用scroll-view实现横向滚动 -->
    <scroll-view class="tab-scroll-view" scroll-x :scroll-into-view="'tab-' + activeTab" scroll-with-animation>
      <view class="tab-bar">
        <text v-for="(tab, index) in tabsList" :key="index" :id="'tab-' + index" class="tab-item" :class="{ active: index === activeTab }" @click="handleTabChange(index)">
          {{ tab.label }}
        </text>
      </view>
    </scroll-view>

    <view class="sort-bar" v-if="noPrice.includes(activeTab)">
      <text v-for="(option, idx) in sortOptions" :key="idx" class="sort-item" :class="{ active: idx === activeSort }" @click="handleSortChange(idx)">
        {{ option }}
      </text>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

// 定义 props
const props = defineProps({
  tabsList: {
    type: Array,
    required: true,
    default: () => [],
  },
  activeTab: {
    type: Number,
    default: 0,
  },
  sortOptions: {
    type: Array,
    required: true,
    default: () => [],
  },
  activeSort: {
    type: Number,
    default: 0,
  },
  noPrice: {
    type: Array,
    default: () => [],
  },
});

// 定义事件（支持 v-model 双向绑定）
const emit = defineEmits(["update:activeTab", "update:activeSort"]);

// Tab 切换事件
const handleTabChange = (index) => {
  emit("update:activeTab", index);
};

// 排序切换事件
const handleSortChange = (index) => {
  emit("update:activeSort", index);
};
</script>

<style lang="scss" scoped>
.filter-menu {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  // Tab 滚动容器
  .tab-scroll-view {
    width: 100%;
    white-space: nowrap;
    border-bottom: 1px solid #f5f5f5;

    // 隐藏滚动条（根据需要可选）
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }
  }

  // Tab 栏样式
  .tab-bar {
    display: inline-flex;
    flex-direction: row;
    padding: 24rpx 0;

    .tab-item {
      font-size: 28rpx;
      color: #333333;
      position: relative;
      padding: 0 30rpx 12rpx;
      flex-shrink: 0; // 防止压缩

      &.active {
        color: #1677ff;
        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: #1677ff;
        }
      }
    }
  }

  // 排序栏样式
  .sort-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    padding: 20rpx 0;
    border-top: 1px solid #f5f5f5;

    .sort-item {
      font-size: 26rpx;
      color: #666666;

      &.active {
        color: #1677ff;
        font-weight: 500;
      }
    }
  }
}
</style>
