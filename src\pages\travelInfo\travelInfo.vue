<template>
  <view class="travelInfo">
    <view class="box">
      <view class="card">
        <view class="item">
          <view class="left">行程封面</view>
          <view class="right">
            <image v-if="userJourneyDto.journeyPictures" :src="imgHost + userJourneyDto.journeyPictures"
              mode="aspectFill" class="img" @click="chooseImage" />
            <uni-icons @click="chooseImage" v-else type="image" color="#000" size="80rpx" />
            <uni-icons type="right" color="#000" size="40rpx" />
          </view>
        </view>
        <view class="item">
          <view class="left">行程名称</view>
          <view class="right">
            <view class="text"><input type="text" placeholder="请输入" maxlength="30"
                v-model="userJourneyDto.journeyName" /></view>
            <uni-icons type="right" color="#000" size="40rpx" />
          </view>
        </view>
      </view>
      <view class="card">
        <view class="item--area">
          <view class="top">行程简介</view>
          <textarea class="bottom" placeholder="写点什么，记录这次旅行～" v-model="userJourneyDto.journeyRemark" maxlength="500" />
          <view class="tips">{{ userJourneyDto.journeyRemark?.length || 0 }}/500</view>
        </view>
      </view>
    </view>
    <view class="button" @click="save">完成</view>
  </view>
</template>
<script setup>
import request from "@/utils/request.js";
import { useTravelStore } from "@/stores/travel";
import { getEnv } from "@/utils/getEnv";

const { VITE_IMG_HOST, VITE_UPLOAD_HOST } = getEnv();
const { journeyId } = Tool.getRoute.params();
const imgHost = ref(VITE_IMG_HOST);
const userJourneyDto = ref({});
const travel = useTravelStore();

const chooseImage = () => {
  uni.chooseImage({
    // 选择图片
    count: 1,
    sourceType: ["album", "camera"],
    success: function (res) {
      const filePath = res.tempFilePaths[0];
      uni.showLoading({ mask: true, title: "图片上传" });
      uni.uploadFile({
        // 上传图片
        url: VITE_UPLOAD_HOST,
        filePath,
        name: "file",
        success: async (uploadFileRes) => {
          // 保存
          userJourneyDto.value.journeyPictures = JSON.parse(
            uploadFileRes.data
          )[0].path;
          uni.hideLoading();
        },
        fail: () => { },
      });
    },
    fail: function () { },
  });
};
const save = () => {
  travel.userJourneyDto = userJourneyDto.value;
  goPage.back();
  // request
  //   .post(`/user/journey/${journeyId ? "update" : "save"}`, {
  //     ...userJourneyDto.value,
  //   })
  //   .then(({ data }) => {
  //     travel.userJourneyDto = data;
  //     uni.showToast({
  //       icon: "none",
  //       title: "保存成功",
  //     });
  //     journeyId
  //       ? goPage.back()
  //       : goPage.replace(
  //           "/pages/travelDetails/travelDetails?journeyId=" + data.journeyId
  //         );
  //   });
};

onBeforeMount(() => {
  if (journeyId) {
    userJourneyDto.value = JSON.parse(JSON.stringify(travel.userJourneyDto));
  }
});
</script>
<style lang="scss" scoped>
.travelInfo {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f1f1f1;

  .box {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    overflow: auto;

    .card {
      margin: 10rpx;
      width: 690rpx;
      background: #fff;
      border-radius: 12rpx;

      .item {
        padding: 0 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 112rpx;

        .left {
          font-weight: bold;
          font-size: 30rpx;
          color: #14131f;
        }

        .right {
          display: flex;
          align-items: center;
          gap: 15rpx;
        }

        .img {
          width: 80rpx;
          height: 80rpx;
          border-radius: 12rpx;
        }

        .text {
          font-size: 30rpx;
          color: #060131;

          >input {
            text-align: end;
          }
        }
      }

      .item:not(:first-child) {
        border-top: 2rpx solid rgba(191, 198, 209, 0.25);
      }

      .item--area {
        padding: 30rpx;
        position: relative;
        color: #14131f;

        .top {
          font-weight: bold;
          font-size: 30rpx;
        }

        .bottom {
          margin-top: 23rpx;
          width: 100%;
          font-size: 28rpx;

          &::placeholder {
            color: #14131f80;
          }
        }

        .tips {
          position: absolute;
          bottom: 24rpx;
          right: 24rpx;
          font-size: 28rpx;
          color: #14131f80;
        }
      }
    }
  }

  .button {
    margin: 34rpx 75rpx;
    width: 600rpx;
    height: 88rpx;
    background: #349fff;
    border-radius: 12rpx;
    font-weight: bold;
    font-size: 36rpx;
    color: #fff;
    line-height: 88rpx;
    text-align: center;
  }
}
</style>
