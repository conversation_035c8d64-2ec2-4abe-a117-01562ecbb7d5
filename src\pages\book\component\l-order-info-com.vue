<template>
	<view class="book-num">
		<view class="title">
			<view>{{ ticketInfo._title }}</view>
		</view>
		<view class="enter-time" v-for="(item, index) in ticketDayList" :key="index">
			<view class="com-title" v-if="item.text">{{ item.text }}</view>
			<view class="title">
				<y-font-weight>入园时间</y-font-weight>
			</view>
			<l-calendar v-model:day="item.day" :dateRange="ticketInfo._dateRange" :price="ticketInfo._storeGoodsPrice" />
		</view>
		<view class="time-period" v-show="timePeriodList && timePeriodList.length">
			<view class="title">
				<y-font-weight>分时时段</y-font-weight>
			</view>
			<view>
				<view class="period-list">
					<view class="period-item-box" v-for="(item, itemIndex) in timePeriodList" :key="itemIndex">
						<view class="period-item" :class="{
                disabled: item?.disabled,
                active: timePeriodField?.timeShareId === item?.timeShareId,
              }" @click="onTimePeriodClick(item)">
							{{item?.timeShareBeginTime}} - {{ item?.timeShareEndTime }}
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="add-number">
			<view class="left">
				<y-font-weight>购买数量</y-font-weight>
			</view>
			<y-number v-model="localTicketNum" />
		</view>
		<view class="book-notice">
			<view class="left" v-if="ticketInfo?.isRealName == 1">
				需要携带身份证入园
			</view>
			<view class="right" @tap="popModel(false)" v-if="ticketInfo.goodsId">
				预订须知 <uni-icons type="forward" color="#65300F"></uni-icons>
			</view>
		</view>
		<y-popup v-model="isPopUpWin" type="reserve" title="预订须知">
			<view v-if="ticketInfo.note" class="rich-content">
				<div v-html="ticketInfo.note"></div>
			</view>
			<y-empty v-else>暂无内容</y-empty>
		</y-popup>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, computed, watch, onMounted } from "vue"
import { hexToRgb, getWeekDate, markdownToHtml, compareTimes } from "@/utils/tool.js"
import request from "@/utils/request.js"
import dayjs from "dayjs"
import LCalendar from "@/pages/book/component/l-calendar.vue"

const props = defineProps({
	ticketNum: {
		type: Number,
		default: 1
	},
	ticketInfo: {
		type: Object,
		default: () => ({})
	},
	type: {
		type: String,
		default: "single"
	},
	// 数量范围
	numRange: {
		type: Array,
		default: () => [1, 99]
	}
})

const emits = defineEmits(["onChangeDate", "update:ticketNum", "onChangeTimePeriodStock", "update:ticketInfo"])

// 本地票数量，避免直接修改 props
const localTicketNum = computed({
	get: () => props.ticketNum,
	set: (val) => emits("update:ticketNum", val)
})

// 分时时段
const timeLaws = ref([])
const timePeriodList = ref([])
const timePeriodField = ref({})

// 处理分时时段
const dealInitTimeLaws = () => {
// 初始化默认时间和默认时间段信息
if (props.ticketInfo?.timeLaws && props.ticketInfo.timeLaws.length) {
    const _timeLaws = [...props.ticketInfo.timeLaws];
    _timeLaws.sort((a, b) => {
      // 对日期进行排序
      if (dayjs(a?.timeShareData) < dayjs(b?.timeShareData)) return -1;
      return 1;
    });
    _timeLaws.map((e) => {
      // 对时间段进行排序
      if (e?.timeShareDateList) {
        e.timeShareDateList.sort((a, b) => {
          if (compareTimes(a.timeShareBeginTime, b.timeShareBeginTime) === -1) return -1;
          return 1;
        });
      }
    });
    timeLaws.value = _timeLaws; // 排序好的分时时段
  }
}
const dealTimePeriodDatas = () => {
  // 初始化默认日期下的默认时间段
  const _timeList = (timeLaws.value || []).filter(
    (e) =>
      e?.timeShareData === dayjs(props.ticketInfo._day).format('YYYY-MM-DD') &&
      e?.timeShareDateList && e.timeShareDateList.length,
  );
  
  if (_timeList?.length) {
    let _flag = false; // 是否赋值过
    const _list = (_timeList[0]?.timeShareDateList || []).map((_item) => {
      const _disabled = (!dayjs(props.ticketInfo._day).isAfter(dayjs()) && (_item?.timeShareEndTime && compareTimes(dayjs().format('HH'), _item?.timeShareEndTime) === 1) || !_item?.stockAmount);
      if (!_disabled && !_flag) {
        timePeriodField.value = _item; // 设置第一个可用的时段为默认值
        _flag = true
      }
      return {
        ..._item,
        disabled: _disabled,
      }
    })
    timePeriodList.value = _list; // 当前选中时间对应的分时时段
  } else {
    timePeriodField.value = {}
    timePeriodList.value = []
  }
}


// 入园快捷选择
const latelyEnterDay = ref([])
const ticketDayList = ref([])

// 禁用减少按钮
const disSubtract = computed(() => {
	return props.ticketNum <= props.numRange[0]
})
// 禁用增加按钮
const disAdd = computed(() => {
	console.log("禁用增加按钮")
	console.log(props.ticketNum)
	console.log(props.numRange[1])
	return props.ticketNum >= props.numRange[1]
})
// 修改票的数量
const onChangeTicketNum = type => {
	let num = props.ticketNum
	if (type === "add" && !disAdd.value) {
		num++
	} else if (type === "subtract" && !disSubtract.value) {
		num--
	}
	console.log(type)
	console.log(disAdd)
	console.log(disSubtract)
	emits("update:ticketNum", num)
}
// 入园快捷选择
watch(
	() => props.ticketInfo._day,
	(newVal, oldVal) => {
		let [startDate] = props.ticketInfo._dateRange || []
		const lastDayList = []
		if (!startDate) startDate = dayjs().format("YYYY-MM-DD")
		console.log(newVal)
		const dayList = []
		for (let index = 0; index < 4; index++) {
			const day = dayjs(startDate).add(index, "day").format("YYYY-MM-DD")
			let text = getWeekDate(day)
			// 今天显示今天
			if (dayjs(day).isSame(dayjs(), "day")) text = "今天"
			// 明天显示明天
			if (dayjs(day).isSame(dayjs().add(1, "day"), "day")) text = "明天"
			console.log({
				text,
				day
			})
			dayList.push({
				text,
				day
			})
		}
		// 如果当前选择的日期不在快捷选择中、且在于有效时间内，则添加到快捷选择中
		if (
			!dayList.some(item => item.day === newVal) &&
			dayjs(newVal).isAfter(startDate)
		) {
			const lastIndex = dayList.length - 1
			dayList[lastIndex] = {
				text: getWeekDate(newVal),
				day: newVal
			}
		}
		console.log(dayList)
		latelyEnterDay.value = dayList
		ticketDayList.value = dayList
    // 处理当前日期下需要展示的分时时段数据
    dealTimePeriodDatas();
	},
	{
		immediate: true
	}
)
watch(
	() => props.ticketNum,
	() => {
		if (props.ticketInfo.isPeopleNumber && (disAdd.value || disSubtract.value))
			uni.showToast({
				icon: "none",
				title: `此票购买数量需在${props.ticketInfo.minPeople}～${props.ticketInfo.maxPeople}之间`
			})
	}
)
watch(
  () => props.ticketInfo?.timeLaws,
  () => {
    dealInitTimeLaws();
    // 处理当前日期下需要展示的分时时段数据
    dealTimePeriodDatas();
  },
  {
		immediate: true
	}
)
watch(
  () => timePeriodField,
  () => {
    // 分时时段变化时，需要去修改库存
    emits('onChangeTimePeriodStock', timePeriodField.value);
  },
  {
		immediate: true,
    deep: true,
	}
)

//预定须知弹窗
const isPopUpWin = ref(false)
const noteContent = ref('')

const popModel = async isCard => {
	if (isCard) {
		isPopUpWin.value = true
	} else {
		await getGoodsNote(props.ticketInfo.goodsId)
		isPopUpWin.value = true
	}
}
//获取预定须知
const getGoodsNote = async goodsId => {
	try {
		const { data } = await request.get(`/appScenic/goodsNote/${goodsId}`)
		noteContent.value = markdownToHtml(data)
		// 创建一个新对象而不是直接修改 props
		emits('update:ticketInfo', { ...props.ticketInfo, note: noteContent.value })
	} catch (err) {
		console.log(err)
	}
}
const showMoreDay = ref(false)

// 时段改变
const onTimePeriodClick = (item) => {
  if (item.disabled) return;
  timePeriodField.value = item
}
</script>
<style lang="scss" scoped>
.book-num {
	margin-bottom: 20rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;

	.travel-card-img {
		width: 100%;
	}

	> .title {
		display: flex;
		justify-content: space-between;
		margin: 30rpx;
		padding-bottom: 30rpx;
		font-size: 38rpx;
		font-weight: 600;
		color: #050505;
		line-height: 44rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
	}

	.enter-time {
		margin: 30rpx 30rpx 0 30rpx;
		padding-bottom: 25rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);

		> .title {
			margin-bottom: 25rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #050505;
			line-height: 32rpx;
		}

		.calendar {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.calendar__list {
				flex: 1;
				display: flex;
				overflow: scroll;
				-ms-overflow-style: none;
				scrollbar-width: none;
				&::-webkit-scrollbar {
					display: none;
				}
			}
			.calendar__more {
				color: var(--theme-color);
				font-weight: 400;
				font-size: 26rpx;
				width: 90rpx;
				height: 164rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 3;
				box-shadow: -4px 0px 8px -2px rgba(219, 219, 219, 0.5);
			}

			.active {
				background: rgba(var(--theme-bg), 0.1) !important;
				border: 1px solid var(--theme-color) !important;
			}

			.item {
				flex: none;
				position: relative;
				display: flex;
				align-items: center;
				flex-direction: column;
				justify-content: center;
				padding: 0 15rpx;
				overflow: hidden;
				width: 136rpx;
				height: 164rpx;
				margin-right: 16rpx;
				border-radius: 8rpx;
				background: #fbfbfb;
				border: 1px solid #e1e1e2;

				.date {
					font-size: 25rpx;
					font-weight: 400;
					color: #050505;
					line-height: 38rpx;
				}

				.price {
					font-size: 32rpx;
					font-weight: 500;
					color: #f43636;
					line-height: 40rpx;

					.unit {
						font-size: 24rpx;
					}
				}
			}
		}
	}

	.book-notice {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #fff5e8;
		height: 56rpx;
		padding: 0 30rpx;
		color: #65300f;
		border-bottom-left-radius: 19rpx;
		border-bottom-right-radius: 19rpx;

		.left {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 33rpx;
		}

		.right {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 33rpx;

			> .icon {
				// width: 20rpx;
				margin-left: 8rpx;
			}
		}
	}

	.add-number {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 0 26rpx;
		margin: 0rpx 30rpx 0 30rpx;

		.left {
			font-size: 32rpx;
			font-weight: 500;
			color: #050505;
			line-height: 45rpx;
		}

		.right {
			display: flex;
			align-items: center;

			.across,
			.vertical {
				position: absolute;
				width: 22rpx;
				height: 4rpx;
				background-color: #fff;
				border-radius: 2rpx;
			}

			.add,
			.subtract {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50rpx;
				height: 50rpx;
				background: var(--theme-color);
				border-radius: 8rpx;

				&.disable {
					background-color: #f0f0f0;

					.across,
					.vertical {
						background-color: #c5c5c5;
					}
				}
			}

			.vertical {
				transform: rotate(90deg);
			}

			.number {
				margin: 0 30rpx;
			}
		}
	}

  .time-period {
    margin: 30rpx 30rpx 0 30rpx;
		padding-bottom: 25rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
    .title {
      font-size: 28rpx;
    }
    .period-list {
      display: flex;
      flex-wrap: wrap;
      padding: 16rpx 0 0 0;
      .period-item-box {
        display: flex;
        margin: 0 0 16rpx 0;
        width: 33.3%;
        font-size: 22rpx;
        .period-item {
          padding: 4rpx 8rpx;
          background: #fbfbfb;
          border: 1px solid #e1e1e2;
          border-radius: 0.25rem;
        }
        .active {
          background: rgba(var(--theme-bg), 0.1) !important;
          border: 1px solid var(--theme-color) !important;
        }
        .disabled {
          background-color: #f0f0f0;
        }
      }
    }
  }
}
.com-title{
	margin: 30rpx 0 20rpx;
	color:#349FFF;
	font-size: 30rpx;
	font-weight: 500;
}
</style>
