# 项目信息
sonar.projectKey=shop

# 源代码路径（必须）
sonar.sources=src

# 编程语言（可省略，SonarQube 自动识别 TS/JS，指定 js 可支持混合项目）
sonar.language=js

# TypeScript 配置文件（用于分析类型）
sonar.typescript.tsconfigPath=tsconfig.json

# ESLint 静态检查报告（必须先生成 eslint-report.json）
sonar.javascript.eslint.reportPaths=eslint-report.json

# cypress 测试覆盖率报告（必须先生成 coverage/e2e/lcov.info）
sonar.javascript.lcov.reportPaths=coverage/e2e/lcov.info

# CSS/预处理器文件后缀
sonar.css.file.suffixes=.css,.scss,.less

# 排除不需要分析的路径或文件
sonar.exclusions=**/*.test.ts,**/*.spec.ts,dist/**,node_modules/**

sonar.exclusions=**/*.java
