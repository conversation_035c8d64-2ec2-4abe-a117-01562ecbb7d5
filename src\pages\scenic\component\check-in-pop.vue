<template>
	<uni-popup ref="popup" type="center" :is-mask-click="false">
		<view class="popup-container">
			<image class="popup-image" referrerpolicy="no-referrer" :src="popupContent.image" />
			<view class="text-group">
				<text class="popup-title">{{ popupContent.title }}</text>
				<text class="popup-message">{{ popupContent.message }}</text>
			</view>
			<view class="popup-button" @click="closePopup">
				<text class="button-text">{{ popupContent.buttonText }}</text>
			</view>
			<uni-icons class="close-icon" type="close" size="32" color="#D8D8D8"
				@click="closePopup"></uni-icons>
		</view>
	</uni-popup>
</template>

<script setup>
import {
	ref,
	defineExpose,
	computed
} from 'vue';
import checkInFailImage from '@/static/image/check-in-fail.webp';
import writeCommentImage from '@/static/image/write-comment.png';

const emit = defineEmits(['getLocation']);

const failType = ref('location'); // 'location' or 'distance' or 'reviewed'
const popupContent = computed(() => {
	const data = {
		location: {
			image: checkInFailImage,
			title: '打卡失败',
			message: '开启定位权限后才能完成打卡哦',
			buttonText: '立即开启',
			showCloseIcon: true,
		},
		distance: {
			image: checkInFailImage,
			title: '打卡失败',
			message: '距离景区较远，请走进 1km 内再尝试打卡',
			buttonText: '我知道了',
			showCloseIcon: true,
		},
		reviewed: {
			image: writeCommentImage,
			title: '温馨提示',
			message: '同个景区一天仅能点评一次哦',
			buttonText: '我知道了',
			showCloseIcon: false,
		}
	};
	return data[failType.value] || {};
});

const popup = ref(null);

const openPopup = (type = 'location') => {
	failType.value = type;
	if (popup.value) {
		popup.value.open();
	}
};

const closePopup = () => {
	if (popup.value) {
		popup.value.close();
	}

	if (failType.value === 'location') {
		emit('getLocation');
	}
};

defineExpose({
	open: openPopup,
	close: closePopup
});
</script>

<style lang="scss" scoped>
.popup-container {
	width: 540rpx;
	background: linear-gradient(180deg, #E3EDFE 0%, #FFFFFF 100%);
	background-size: 100% 100%;
	padding: 48rpx 74rpx 56rpx 74rpx;
	border-radius: 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;

	.popup-image {
		width: 256rpx;
		height: 256rpx;
	}

	.text-group {
		margin-top: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.popup-title {
			color: #14131f;
			font-size: 36rpx;
			font-family: PingFangSC-Medium;
			font-weight: 500;
			line-height: 50rpx;
			white-space: nowrap;
		}

		.popup-message {
			color: #666;
			font-size: 28rpx;
			font-weight: normal;
			white-space: nowrap;
			line-height: 40rpx;
			margin-top: 16rpx;
			word-wrap: break-word;
				white-space: normal;
				text-align: center;
		}
	}

	.popup-button {
		background-color: #349FFF;
		border-radius: 16rpx;
		margin-top: 92rpx;
		width: 360rpx;
		padding: 19rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.button-text {
			color: #fff;
			font-size: 30rpx;
			font-family: PingFangSC-Medium;
			font-weight: 500;
			white-space: nowrap;
			line-height: 42rpx;
		}
	}

	.close-icon {
		position: absolute;
		bottom: -90rpx;
		left: 50%;
		transform: translateX(-50%);
		color: #D8D8D8;
	}
}
</style>
