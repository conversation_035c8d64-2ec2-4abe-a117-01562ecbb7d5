<style lang="scss" scoped>
.panel-box {
	flex: none; // 不随父元素的大小改变
	margin-bottom: 20rpx;

	padding: 30rpx;

	color: #14131f;
	font-size: 28rpx;
	font-size: 32rpx;
	background: rgba(255, 255, 255, 0.6);
		border-radius: 0rpx 22rpx 22rpx 22rpx;
		backdrop-filter: blur(12rpx);

	.tip__content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		width: 100%;
		margin: 10rpx 0;
		padding: 0rpx 30rpx;
		overflow: hidden;
		height:70rpx;
		color: #4787FB;
		font-size: 26rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		background: #fff;
		border-radius: 35rpx;
	}

	.title {
		color: #14131f;

		font-weight: 500;

		font-size: 32rpx;
		margin-bottom: 20rpx;
	}

	.item {
		display: flex;

		align-items: center;

		justify-content: space-between;

		padding: 20rpx 0;

		font-size: 28rpx;

		.right {
			display: flex;

			align-items: center;

			.circle {
				position: relative;

				width: 44rpx;

				height: 44rpx;

				border: 4rpx solid #349fff;

				border-radius: 50%;

				.hen {
					position: absolute;

					top: 50%;

					left: 50%;

					width: 20rpx;

					height: 4rpx;

					background: #349fff;

					transform: translate(-50%, -50%);
				}

				.shu {
					position: absolute;

					top: 50%;

					left: 50%;

					width: 4rpx;

					height: 20rpx;

					background: #349fff;

					transform: translate(-50%, -50%);
				}
			}

			.num {
				width: 100rpx;
				text-align: center;
				font-size: 36rpx;
			}
		}
	}

	.buttom {
		display: flex;

		gap: 30rpx;
		justify-content: space-between;
		margin-top: 50rpx;

		div {
			width: 50%;
			padding: 20rpx 0;

			color: #349fff;
			font-weight: 500;
			font-size: 36rpx;
			text-align: center;
			border-radius: 12px;
		}

		.left {
			color: #349fff;
			background-color: rgba(52, 159, 255, 0.17);
		}

		.right {
			color: #fff;
			background-color: #349fff;
		}
	}
}

.multiple {
	.multiple-title {
		margin-bottom: 20rpx;

		color: #14131f;

		font-weight: 500;

		font-size: 28rpx;
	}

	.multiple-content {
		display: flex;

		flex-wrap: wrap;

		div {
			margin: 10rpx 20rpx 10rpx 0;

			padding: 6rpx 20rpx;

			color: #14131f;
			font-size: 28rpx;
			background: #f6f6f6;
			border: 1rpx solid #bfbfbf;
			border-radius: 6rpx;
		}

		.active {
			color: #fff;
			background: #349fff;
			border: 1rpx solid #349fff;
		}
	}
}
</style>
<template>
	<div class="panel-box">
		你好，我是 AI 文旅小助手，我可以为你规划行程，推荐景点/酒店等，你可以问我：
		<div style="margin: 16rpx 0">
			<div @click="emits('onSubmit', item)" class="tip__content" v-for="(item, index) in params.skills"
				:key="index">
				{{ item }}
				<image style="width: 22rpx;height: 20rpx;" src="@/static/image/ai/f-icon.webp" mode="widthFix" />
			</div>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	params: {
		type: Object,
		default: () => ({})
	}
})

const emits = defineEmits(["onSubmit"])

</script>
