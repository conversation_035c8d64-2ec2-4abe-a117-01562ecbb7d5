import { logOut } from '@/utils/tool.js';
import { getEnv } from '@/utils/getEnv';

//全局配置
let baseHost = '';
const { VITE_AI_BASE_URL, VITE_AI_BASE_URL__DEV } = getEnv();
if (import.meta.env.PROD) {
  // 生产环境，使用配置
  baseHost = VITE_AI_BASE_URL;
} else {
  // 开发环境，使用代理
  baseHost = VITE_AI_BASE_URL__DEV;
}

function request(method, url, params, option) {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    let cookie = uni.getStorageSync('ylb-cookie');
    // #endif
    uni.request({
      method: method,
      url,
      header: {
        // #ifdef MP-WEIXIN
        cookie: cookie,
        // #endif
        // "X-Trace-Id":
        // 	Math.random().toString(36).substring(2, 6) +
        // 	"-" +
        // 	new Date().getTime() // 本次请求唯一标识
      },
      data: Array.prototype.isPrototypeOf(params) ? params : { systemType: 'app', ...params },
      // 请求成功
      success: ({ data }) => {
        if (option?.withOut) {
          // 直接返回
          resolve(data);
          return;
        }

        if (!data.code && data.status) {
          data.code = data.status;
        }

        if (data.code === 200) {
          // 正常回调
          resolve(data);
          return;
        }
        // 异常统一拦截
        const errMsg = data.msg || data.message;
        if (errMsg)
          uni.showToast({
            title: errMsg,
            icon: 'none',
          });
        // 令牌失效
        if (data.code == 401) {
          // 去登陆
          logOut('redirect');
        }
        // 服务器错误
        if (data.code == 500) {
          // 跳转到 500 页面
          // goPage.replace("/pages/error/500")
        }
        // 异常回调
        reject(data);
      },
      // 请求失败
      error: (res) => {
        // uni.showToast({
        // 	title: "请求失败！请检查网络配置！",
        // 	icon: "none"
        // })
        // goPage.replace("/pages/error/500")
      },
    });
  });
}

export default {
  get(url, params, option) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('GET', requestUrl, params, option)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  post(url, params, host) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('POST', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  put(url, params, host) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('PUT', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  delete(url, params, option) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('DELETE', requestUrl, params, option)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};
