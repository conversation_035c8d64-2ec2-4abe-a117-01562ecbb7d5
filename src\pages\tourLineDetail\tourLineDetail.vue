<template>
	<view class="line_detail">
		<image
			class="top_img"
			:src="imgSrc(detail?.publicizeUrl)"
			mode="aspectFill" />
		<view class="content">
			<view class="left">
				<view class="title">{{ detail?.lineName }}</view>
				<view class="tips">
					<text>{{ detail?.pointNumber }}个点位</text>
					<text>{{ detail?.estimateHour }}小时</text>
					<text>{{ detail?.estimateDistance }}公里</text>
				</view>
			</view>
			<view class="right">
				<image
					:class="{ go: !current }"
					:src="imgSrc(avatar)"
					mode="aspectFill" />
			</view>
		</view>
		<swiper
			class="swiper"
			previous-margin="159rpx"
			next-margin="159rpx"
			:current="current"
			@change="change">
			<swiper-item v-for="(item, index) in swiperList" :key="index">
				<image
					class="swiper-item"
					:class="{ active: current == index }"
					:src="index ? imgSrc(item.publicizeUrl) : item.publicizeUrl"
					mode="aspectFill" />
			</swiper-item>
		</swiper>
		<view class="dots-box">
			<view class="line"></view>
			<view class="dots">
				<view
					:class="{ active: current == index }"
					v-for="(item, index) in swiperList"
					:key="index">
					<view class="tip">
						<text v-if="index">{{ index }}</text>
						<image v-else :src="imgSrc(avatar)" mode="aspectFit" />
					</view>
					<view class="dot"></view>
					<text class="txt" @click="current = index">{{ item.pointName }}</text>
				</view>
			</view>
		</view>
		<image
			class="bottom_img"
			src="@/static/image/tour/tour_map.svg"
			mode="aspectFit"
			@click="
	Tool.goPage.push(`/pages/tour/tour?guideId=${guideId}&lineId=${id}`)
			" />
	</view>
</template>

<script setup>
import { onMounted, ref, nextTick } from "vue"
import request from "@/utils/request.js"
import { getRoute, imgSrc } from "@/utils/tool.js"
import $tourLineBg from "@/static/image/tour/tour_line_bg.svg"

const { guideId, id } = getRoute.params()
const detail = ref({})
const swiperList = ref([])
const current = ref(0)
const avatar = ref()

const change = event => {
	const index = event.detail.current
	current.value = index
	nextTick(() => {
		document.querySelector(".dots .active").scrollIntoView({
			behavior: "smooth",
			inline: "center"
		})
	})
}

request.get("/navigation/line/info", { id }).then(({ data }) => {
	detail.value = data
	const list = JSON.parse(data.lineContent).filter(item => item.pointId)
	swiperList.value = [
		{
			pointName: "开始游玩",
			publicizeUrl: $tourLineBg
		},
		...list
	]
})
onMounted(async () => {
	const { userInfo } = await Tool.getUserInfo()
	try {
		avatar.value = userInfo.avatar
	} catch (error) {}
})
</script>

<style lang="scss" scoped>
.line_detail {
	display: flex;
	flex-direction: column;
	background: #fff;
	min-height: 100vh;
	padding-bottom: 20rpx;
	.top_img {
		margin: 30rpx auto;
		width: 690rpx;
		height: 388rpx;
		background: #d8d8d8;
		border-radius: 12rpx;
	}
	.content {
		margin: 0 auto;
		width: 690rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.left {
			.title {
				width: 400rpx;
				font-size: 40rpx;
				font-weight: bold;
				color: #000;
				line-height: 1;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.tips {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #535353;
				line-height: 33rpx;
				> text:not(:first-child) {
					margin-left: 20rpx;
				}
			}
		}
		.right {
			> image {
				width: 66rpx;
				height: 66rpx;
				border-radius: 50%;
				transition: 0.2s;
			}
			.go {
				transform: scale(0);
			}
		}
	}
	.swiper {
		margin-top: 54rpx;
		height: 324rpx;
		.swiper-item {
			width: 100%;
			height: 100%;
			transition: 0.2s;
			transform: scale(0.8);
			background: #f9f9fb;
			border-radius: 12rpx;
		}
		.active {
			transform: scale(1);
		}
	}
	.dots-box {
		margin-top: 42rpx;
		position: relative;
		.line {
			position: absolute;
			top: 79rpx;
			width: 100%;
			height: 8rpx;
			background: #ebf5ff;
		}
		.dots {
			position: relative;
			display: flex;
			overflow: auto;
			width: 100%;
			height: 275rpx;
			padding: 0 306rpx;
			&::-webkit-scrollbar {
				display: none;
			}
			> view {
				margin: 0 34rpx;
				width: 66rpx;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				.tip {
					width: 66rpx;
					height: 66rpx;
					text-align: center;
					line-height: 59rpx;
					color: #349fff;
					font-size: 26rpx;
					font-weight: bold;
					transform: scale(0);
					transform-origin: bottom;
					transition: 0.5s;
					> image {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}
				.dot {
					margin-top: 7rpx;
					width: 20rpx;
					height: 20rpx;
					border-radius: 50%;
					background: #afd9ff;
				}
				.txt {
					margin-top: 19rpx;
					width: 64rpx;
					flex: 1;
					writing-mode: vertical-lr;
					display: flex;
					align-items: center;
				}
			}
			> view:not(:first-child) .tip {
				background: url("@/static/image/tour/tour_line_point.svg") no-repeat
					center/contain;
			}
			.active {
				.tip {
					transform: scale(1);
				}
				.dot {
					background: #349fff;
				}
				.txt {
					color: #349fff;
				}
			}
		}
	}
	.bottom_img {
		position: fixed;
		right: 20rpx;
		bottom: 80rpx;
		width: 80rpx;
		height: 80rpx;
		background: #fff;
		box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
		border-radius: 50%;
		padding: 14rpx;
	}
}
</style>
