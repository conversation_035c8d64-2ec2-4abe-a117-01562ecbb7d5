{"uni-app": {"scripts": {"dev:h5": {"title": "H5 - 开发环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "YLB_ENV": "dev"}}, "dev:wx": {"title": "wx - 开发环境", "env": {"UNI_PLATFORM": "mp-weixin", "YLB_ENV": "dev"}}, "test:h5": {"title": "H5 - 测试环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "YLB_ENV": "test"}}, "test:wx": {"title": "wx - 测试环境", "env": {"UNI_PLATFORM": "mp-weixin", "YLB_ENV": "test"}}, "canary:h5": {"title": "H5 - canary 环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "YLB_ENV": "canary"}}, "canary:wx": {"title": "wx - canary 环境", "env": {"UNI_PLATFORM": "mp-weixin", "YLB_ENV": "canary"}}, "prod:h5": {"title": "H5 - 生产环境!!!", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "YLB_ENV": "prod"}}, "prod:wx": {"title": "wx - 生产环境!!!", "env": {"UNI_PLATFORM": "mp-weixin", "YLB_ENV": "prod"}}, "tke:h5": {"title": "H5 - tke 环境", "browser": "chrome", "env": {"UNI_PLATFORM": "h5", "YLB_ENV": "tke"}}}}, "dependencies": {"marked": "^4.0.17"}, "devDependencies": {"vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1"}}