export const scenicGrade = {
  1: 'A',
  2: '2A',
  3: '3A',
  4: '4A',
  5: '5A',
  0: '未评',
};

export const ticketType = {
  0: '门票',
  1: '船票',
  2: '缆车',
};

export const goodsType = {
  0: '成人票',
  1: '儿童票',
  2: '老人票',
  3: '保险票',
  4: '全价票',
  5: '半价票',
  6: '组合票',
  7: '团体票',
};
export const ticketStatus = {
  0: '待核销',
  1: '部分核销', //用过的
  2: '已过期',
  3: '已完成',
  4: '已退票',
};

export const orderStatus = {
	10: "创建订单",
	11: "已取消",
	12: "订单超时失效",
	13: "用户取消订单",
	14: "系统取消订单",
	20: "待付款",
	21: "支付成功",
	22: "支付失败",
	30: "已完成",
	31: "出库成功",
	32: "出库失败",
	33: "出票失败",
	34: "出票成功",
	50: "退款中",
	51: "退款成功",
	52: "退款失败",
	53: "退款取消",
	54: "部分退款",
	55: "退款"
}

export const suggestType = {
  COMBO_TICKET: "组合票",
	SCENIC: "景点",
	TRAVEL_CARD: "权益卡",
}

// ai 文案类型
export const wordTypeEnum = {
	0: "综合版",
	1: "少儿版",
	2: "英文版",
	3: "文化版",
	4: "春天版",
	5: "夏天版",
	6: "秋天版",
	7: "冬天版"
}

export const faceList =  ["不佳", "一般", "不错", "满意", "超棒"]

export const playArr = ["朋友出游", "情侣夫妻", "家庭亲子", "父母陪同", "单独旅行", "其他出游"]

export const timeArr = {
	0: "1小时内",
	1: "1小时",
	2: "2小时",
	3: "3小时",
	4: "4小时",
	5: "5小时",
	6: "6小时",
	7: "7小时",
	8: "1天",
	9: "2天",
	10: "3天",
	11: "3天以上",
}
