import { ref, onMounted } from "vue"

/**
 *
 * @param url websocket 地址
 * @param onSocketMsg 收到消息回调
 * @param onOpen 连接成功回调
 * @param onClose 连接关闭回调
 * @param onError 连接失败回调
 */
export default function useWebSocket({
	url,
	onSocketMsg,
	onOpen,
	onClose,
	onError
}: {
	url: string
	onSocketMsg: (msg: string) => void
	onOpen?: () => void
	onClose?: () => void
	onError: (params: any) => void
}) {
	let timer: number = 0
	let socketTask: any = null
	let wsMsg: any = null

	const wsStatus = ref("close") // ws 状态	close | open | connecting
	const MAX_RECONNECT_ATTEMPTS = 5 // 最大重连次数
	let reconnectAttempts = 0 // 当前重连次数

	// ai 状态码
	const aiErrCode = {
		403: "无权限操作",
		500: "系统出现错误",
		501: "暂不支持此语言服务",
		502: "请输入正确格式",
		504: "当前服务请求繁忙。请稍等。",
		505: "请不要输入无意义的信息",
		506: "当前会话轮次已达限制，请开启新话题",
		507: "超时断开",
		508: "我还没回答完"
	}

	// 连接 websocket
	const wsOpen = () => {
		return new Promise<void>((resolve, reject) => {
			if (wsStatus.value === "close") {
				socketTask = uni.connectSocket({
					url,
					complete: () => {},
					fail: () => {
						wsStatus.value = "close"
						console.log("WebSocket 连接打开失败，请检查！")
						onError && onError()
						reject(new Error("WebSocket 连接打开失败"))
					}
				})

				// 监听消息
				socketTask.onMessage(function (res) {
					if (!res?.data) return
					const msg = JSON.parse(res.data)
					const code = msg?.code?.toString()
					if (code === "507") {
						wsClose()
						reconnect()
					} else if (aiErrCode[code]) {
						onError &&
							onError({
								code,
								msg: aiErrCode[code]
							})
					} else if (msg.type === "pong") {
						console.log("pong")
					} else {
						onSocketMsg(msg)
					}
				})

				// 连接成功监听
				socketTask.onOpen(function (res) {
					console.log("WebSocket 连接已打开！" + url)
					wsStatus.value = "open"
					if (reconnectAttempts !== 0) {
						uni.showToast({
							title: "WebSocket 重连成功",
							icon: "none"
						})
						wsSend(wsMsg)
					}
					reconnectAttempts = 0
					// startHeartbeat()
					onOpen && onOpen()
					resolve()
				})

				// 连接关闭监听
				socketTask.onClose(function (res) {
					wsStatus.value = "close"
					console.log("WebSocket 连接关闭")
					closeHeartbeat()
					onClose && onClose()
				})

				console.log("ws 开始连接")
			} else if (wsStatus.value === "open") {
				console.log("ws 已经连接")
				resolve()
			}
		})
	}
	// 发送消息
	const wsSend = (msg: Object) => {
		if (wsStatus.value === "close") {
			console.log("ws 未连接")
			return
		}
		wsMsg = msg

		socketTask.send({
			data: JSON.stringify(msg)
		})
	}
	// 关闭连接
	const wsClose = () => {
		if (wsStatus.value === "close") {
			console.log("ws 未连接")
			return
		}
		socketTask.close({
			success: () => {
				console.log("WebSocket 已关闭！")
				wsStatus.value = "close"
			}
		})
	}

	// 重连函数
	const reconnect = () => {
		if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
			console.log("达到最大重连次数，停止重连")
			return
		}

		if (wsStatus.value === "close") {
			console.log("尝试重连 WebSocket...")
			uni.showToast({
				title: "尝试重连 WebSocket...",
				icon: "none",
				duration: 2000
			})
			reconnectAttempts++
			setTimeout(() => {
				// 重新初始化 WebSocket 连接
				wsOpen()
			}, 2000) // 每次重连间隔 2 秒
		}
	}

	// 心跳检测
	const startHeartbeat = () => {
		// 心跳检测
		timer = setInterval(() => {
			if (wsStatus.value === "open") {
				wsSend({
					type: "ping"
				})
			}
		}, 10000)
	}
	// 关闭心跳
	const closeHeartbeat = () => {
		clearInterval(timer)
	}

	return { wsOpen, wsSend, wsStatus, wsClose }
}
