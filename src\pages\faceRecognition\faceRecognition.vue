<template>
	<y-nav-bar :backgroundColor="'#61B4FC'" backBtn solid>人脸识别</y-nav-bar>
	<view class="face">
		<view class="tip">请上传一张脸部照片，确保为本人照片，否则影响开闸</view>
		<view class="upload">
			<y-upload upload v-model="imgList" />
		</view>
		<view class="notice">注意事项</view>
		<view class="info">
			<view>1.正对手机，背景尽量纯白色。</view>
			<view>2.露出额头和耳朵，无逆光。</view>
			<view
				>3.确保照片中均为本人照片，不能上传其他人照片，否则会影响开门。</view
			>
			<view>4.人脸铺满整个手机屏幕，不要太远或太近。</view>
		</view>
		<y-button :disable="disable" @tap="sub">{{ butTitle }}</y-button>
	</view>
</template>

<script setup>
import { toRefs, reactive, ref, onMounted, watch } from "vue"
import request from "@/utils/request.js"
import { onLoad } from "@dcloudio/uni-app"
import { getRoute } from "@/utils/tool.js"
import { ticketStatus, ticketType, goodsType } from "@/utils/constant.js"

const imgList = ref("")
const disable = ref(true)
const avatar = ref('')
watch(
	() => imgList.value,
	newVal => {
		console.log("imgList", newVal)
		if (newVal == "") {
			disable.value = true
		} else {
			disable.value = false
		}
	}
)
const butTitle = ref("提交") //按钮文字
onMounted(async () => {
	console.log("getRoute.params()", getRoute.params())
	await init()
})
const init = async () => {
	let params = {
		idCard: getRoute.params().identity,
		idName: decodeURI(getRoute.params().name),
		scenicId: getRoute.params().scenicId,
		ticketId: getRoute.params().ticketNumber
	}
	try {
		const { code, data } = await request.get(`/faceAuth/info`, params)
		if (data.pictureUrl == "") {
			// data.picture='https://test.shukeyun.com/maintenance/deepfile/data/2022-06-30/upload_b21a21d207527444e054230ea9b59e9a.jpg'
		} else {
			butTitle.value = "重新提交"
			if (data.pictureUrl) {
				imgList.value = data.pictureUrl
			}
		}
	} catch (err) {
		console.info(err)
	}
}
// 轮训次数
let count = 0
const getFaceState = async () => {
	count++
	if (count > 10) {
		uni.showToast({
			title: "人脸识别失败，请重新上传！"
		})
		return
	}
	const res = await request.get(`/faceAuth/uploadState`, {
		idCard: getRoute.params().identity,
		scenicId: getRoute.params().scenicId
	})
	if (res.data === "CONTINUE") {
		// 人脸识别中，继续轮询
		setTimeout(() => {
			getFaceState()
		}, 500)
	} else if (res.data === "FAIL" || res.data === "NULL") {
		// 人脸识别失败，重新上传
		uni.showToast({
			title: "人脸识别失败，请重新上传！"
		})
	} else if (res.data === "SUCCESS") {
		// 人脸识别成功，返回上一页
		uni.showToast({
			title: "提交成功！"
		})
		Tool.goPage.back()
	}
}

const sub = async () => {
	if (disable.value) return
	try {
		const { code, data } = await request.post(`/faceAuth/info`, {
			idCard: getRoute.params().identity,
			imageURL: imgList.value
		})
		uni.showLoading({
			title: "审核中",
			mask: true
		})
		count = 0
		setTimeout(() => {
			getFaceState()
		}, 200)
	} catch (err) {
		console.info(err)
	}
}
</script>

<style lang="scss" scoped>
.face {
	padding: 30rpx 40rpx;
	.tip {
		padding: 25rpx 10rpx;
		font-size: 26rpx;
	}
	.upload {
		margin: 10rpx;
	}
	.notice {
		background-color: antiquewhite;
		padding: 30rpx 20rpx;
		font-size: 35rpx;
		margin: 30rpx 0;
	}
	.info {
		font-size: 28rpx;
		view {
			padding: 10rpx;
		}
	}
}
</style>
