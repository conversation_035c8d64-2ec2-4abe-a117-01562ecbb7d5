<template>
  <view>
    <!-- 单票列表 -->
    <template v-if="scenicId">
      <view v-show="n.list && n.list.length > 0" class="ticket" v-for="(n, i) in ticketList" :key="i">
        <view class="ticket-category" v-if="scenicId">
          <text class="category-icon"></text>
          <text class="category-text">{{ n.pcName }}</text>
        </view>
        <view class="ticket-list">
          <view class="item" v-for="(e, k) in n.list" :key="k">
            <view class="title">
              <view class="left">{{ e.goodsName }}</view>
              <view class="right">
                <text class="unit">¥</text>{{ e.storeGoodsPrice
                }}<text class="up">起</text>
              </view>
            </view>
            <view class="time" v-if="e.validityDay">
              <text>{{ `${e.validityDay} 天有效` }}</text>
            </view>

            <view class="tags-row">
              <view class="tag">
                <text v-for="(tag, tagIndex) in e.labels" :key="tagIndex">
                  <text v-if="tagIndex !== 0">/</text> {{ tag.name }}
                </text>
              </view>
              <view class="tag nft" v-if="e.isDigit == '1'">NFT</view>
            </view>

            <view class="reserve">
              <view v-if="e.ticketRemark" class="left" @tap="handleBooking(e)">
                <text v-if="scenicId">预订须知</text>
                <uni-icons type="forward" size="12" color="#1C78E9" v-if="scenicId"></uni-icons>
              </view>
              <view v-else class="left"> </view>

              <view class="right" :class="{ 'disabled': disableTravelCard }" style="position: absolute; right: 30rpx; bottom: 30rpx;" @tap="handleSubmit(e)">立即预订
              </view>
            </view>
          </view>
        </view>
      </view>
      <div v-if="!ticketList || ticketList.length === 0" class="default-icon-box ticket">
        <image class="default-icon" src="@/static/image/default-icon.png" mode="widthFix"></image>
        <div>暂无门票</div>
      </div>
    </template>
    <!-- 组合票列表 -->
    <template v-else>
      <view class="group-ticket">
        <view class="title"> 套票说明 </view>
        <view>
          <view class="item" v-for="(item, index) in ticketList" :key="index">
            <view class="title">
              <y-font-weight>{{ item.scenicName }}</y-font-weight>
            </view>
            <view class="ticket-item" v-for="(e) in item.list" :key="e">
              <view class="ticket-name">
                {{ e.name }}
                <template v-if="e.proType !== undefined || e.goodsType !== undefined">
                  - {{ ticketType[e.proType] }} {{ goodsType[e.goodsType] }}
                </template>
              </view>
              <view class="ticket-info">
                <view v-if="e.isRealName == 1" class="realName">实名制</view>
                <view class="ticket-number">共 {{ e.num }} 张</view>
              </view>
              <view class="ticket-reserve" v-if="e.ticketRemark">
                <view class="left" @tap="handleBooking(e)">
                  <text>预订须知</text>
                  <uni-icons type="forward" size="12" color="#1C78E9"></uni-icons>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="product-explain" v-if="composeGoodsInfo.note">
        <view class="title">
          <y-font-weight>产品特色</y-font-weight>
        </view>
        <view class="note rich-content">
          <div v-html="composeGoodsInfo.note"></div>
        </view>
      </view>
    </template>

    <!-- 预订须知弹窗 -->
    <y-popup v-model="popupVisible" type="reserve" title="预订须知" @close="handlePopupClose">
      <view class="rich-content">
        <div v-if="ticketRemark" v-html="ticketRemark"></div>
        <y-empty v-else>暂无内容</y-empty>
      </view>
    </y-popup>
  </view>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue'
import { goodsType, ticketType } from "@/utils/constant.js"
import { markdownToHtml } from "@/utils/tool.js"
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'

const props = defineProps({
  // 票列表数据
  ticketList: {
    type: Array,
    default: () => []
  },
  // 景区 ID，用于判断是否为单票模式
  scenicId: {
    type: String,
    default: ''
  },
  // 组合票信息
  composeGoodsInfo: {
    type: Object,
    default: () => ({})
  },
  // 是否显示预订须知弹窗
  showPopUp: {
    type: Boolean,
    default: false
  },
  // 预订须知内容
  ticketRemark: {
    type: String,
    default: ''
  },
  // 是否禁用旅游卡
  disableTravelCard: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['booking', 'submit', 'popup-close'])
const ticketRemark = computed(() => {
  return markdownToHtml(props.ticketRemark)
})
// 弹窗可见性
const popupVisible = ref(props.showPopUp)

// 预订须知点击事件
const handleBooking = (e) => {
  console.log(e, props.ticketRemark)
  // emit('booking', e)
  popupVisible.value = true
}

// 立即预订点击事件
const handleSubmit = (e) => {
  if (props.disableTravelCard) {
    uni.showToast({
      title: '请先购买旅游卡',
      icon: 'none'
    });
    return;
  }
  emit('submit', e)
}

// 弹窗关闭事件
const handlePopupClose = () => {
  emit('popup-close')
}
</script>

<style lang="scss" scoped>
// 单票样式
.ticket {
  background-color: #fff;
  margin-bottom: 20rpx;

  .ticket-category {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #E9E9E9;

    .category-icon {
      width: 6rpx;
      height: 28rpx;
      background: #4787FB;
      border-radius: 3rpx;
      margin-right: 12rpx;
    }

    .category-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #050505;
    }
  }

  .ticket-list {
    .item {
      position: relative;
      margin: 0;
      padding: 34rpx 30rpx;
      background-color: #fff;
      border-radius: 12rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

      &:not(:last-child) {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 30rpx;
          /* 左侧留白距离 */
          right: 30rpx;
          /* 右侧留白距离 */
          height: 1px;
          background-color: #E9E9E9;
        }
      }

      >.title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .left {
          margin-right: 8rpx;
          font-size: 32rpx;
          font-weight: 500;
          color: #000000;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .right {
          flex: none;
          font-size: 42rpx;
          font-weight: 600;
          color: #f43636;
          line-height: 42rpx;

          .unit {
            font-size: 28rpx;
          }

          .up {
            font-size: 28rpx;
            color: #6b6b6b;
            font-weight: 400;
          }
        }
      }

      .time {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        font-weight: 400;
        color: #14131F;
        margin-bottom: 12rpx;

        text {
          margin-left: 6rpx;
        }
      }

      .tags-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 12rpx;

        .tag {
          display: inline-block;
          padding: 4rpx 12rpx;
          font-size: 22rpx;
          font-weight: 400;
          color: #ff9201;
          border: 1rpx solid #ff9201;
          border-radius: 6rpx;
          margin-right: 10rpx;
          background-color: rgba(255, 146, 1, 0.05);

          &.nft {
            color: #1C78E9;
            border-color: #1C78E9;
            background-color: rgba(28, 120, 233, 0.05);
          }
        }
      }

      .reserve {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          font-weight: 400;
          color: #1c78e9;
          line-height: 33rpx;

          .icon {
            width: 14rpx;
            margin-left: 8rpx;
          }
        }

        .right {
          display: inline-block;
          padding: 12rpx 30rpx;
          font-size: 26rpx;
          font-weight: 500;
          color: #ffffff;
          line-height: 37rpx;
          letter-spacing: 1px;
          background: #FF9201;
          border-radius: 34rpx;
          box-shadow: 0 4rpx 8rpx rgba(255, 146, 1, 0.3);
        }
      }
    }
  }
}

// 组合票样式
.group-ticket {
  margin-bottom: 30rpx;
  border-radius: 24rpx;

  >.title {
    padding-top: 30rpx;
    font-weight: 600;
    margin-bottom: 22rpx;
    color: #050505;
    font-size: 34rpx;
  }

  .item {
    margin-bottom: 30rpx;
    padding: 34rpx 30rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    >.title {
      margin-right: 8rpx;
      font-weight: 500;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 34rpx;
      color: #14131f;
      margin-bottom: 16rpx;
    }

    .ticket-item {
      padding-top: 30rpx;
      overflow: hidden;

      .ticket-name {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-weight: 500;
        color: #000000;
      }

      .ticket-info {
        display: flex;
        align-items: center;
        margin-top: 15rpx;

        .realName {
          margin-right: 28rpx;
          padding: 2rpx 12rpx;
          background: #ffe7ca;
          border-radius: 6rpx;
          font-size: 22rpx;
          font-weight: 400;
          color: #ff772f;
        }

        .ticket-number {
          font-size: 22rpx;
          font-weight: 400;
          color: #14131f;
        }
      }
    }

    .ticket-item:not(:last-child) {
      border-bottom: 1px solid rgba(157, 157, 157, 0.1);
      padding-bottom: 30rpx;
    }

    .ticket-reserve {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-top: 15rpx;

      .left {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        font-weight: 400;
        color: #1c78e9;
        line-height: 33rpx;

        .icon {
          width: 14rpx;
          margin-left: 8rpx;
        }
      }
    }
  }
}

.product-explain {
  margin-bottom: 30rpx;
  margin-top: 42rpx;
  border-radius: 24rpx;

  >.title {
    margin-bottom: 20rpx;
    color: #050505;
    font-size: 32rpx;
    font-weight: 500;
  }

  .note {
    margin-bottom: 30rpx;
    padding: 34rpx 30rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
}

.default-icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 100rpx 0;

  .default-icon {
    width: 200rpx;
  }
}
.right.disabled {
  background: #dfdfdf !important;
  box-shadow: none !important;
}
</style>
