<template>
  <view class="check-in-map">
    <!-- header -->
    <view class="header-container">
      <image class="header-bg" src="@/static/image/check-in/map-bg.webp" mode="scaleToFill" />
      <view class="header-content">
        <view class="user-info">
          <view class="user-info-left">
            <view class="avatar-wrapper">
              <image class="avatar"
                :src="userInfo.avatar ? imgHost + userInfo.avatar : '/static/image/check-in/avatar.png'" />
              <view class="name">{{ userInfo.nickname }}</view>
            </view>
            <view class="check-in-stats">
              你的打卡分布在
              <text class="highlight">{{ cityCount }}</text>
              个城市
            </view>
            <view class="location-stats">
              共在&nbsp;
              <text class="highlight">{{ locationCount }}</text>
              &nbsp;个地点打卡&nbsp;
              <text class="highlight">{{ totalCheckIns }}</text>
              &nbsp;次

            </view>
          </view>
          <view class="user-info-right">
            <image class="share-icon" src="@/static/image/my/share_icon.png" @click="openSharePopup" />
          </view>
        </view>
      </view>
    </view>

    <!-- map -->
    <view id="container" class="map-view" />

    <!-- add button -->
    <view class="add-check-in-btn" @click="onAddCheckIn">
      <image class="add-icon" src="/static/image/check-in/add-icon.png" />
      <text class="add-text">添加打卡</text>
    </view>

    <l-share-popup ref="sharePopup" :share-data="shareData" />
  </view>
</template>

<script setup>
import { onMounted, ref, onBeforeUnmount, computed } from 'vue';
import request from '@/utils/request.js';
import { debounce } from '@/utils/tool.js';
import { Tool } from '@/utils/tools.ts';
import LSharePopup from './component/l-share-popup.vue';
import { getEnv } from "@/utils/getEnv";

const points = ref([]);
const sharePopup = ref(null);
const userInfo = ref({
  avatar: '',
  nickname: '游客',
  userId: '',
});
const totalCheckIns = ref(0);
const cityCount = ref(0);
const locationCount = ref(0);
const imgHost = ref(getEnv().VITE_IMG_HOST);

// 生成静态地图 URL，使用 points 中的点位作为标记点
const staticMapUrl = computed(() => {
  const key = 'OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77';
  const size = '626*1148';
	if (!points.value || points.value.length === 0) {
		// 没有点位时，返回默认中心点的静态图
    const center = '34.26373,108.94696';
    const zoom = 4
    console.log('oooooooooooooooooooo-')
    console.log(`https://apis.map.qq.com/ws/staticmap/v2/?center=${center}&zoom=${zoom}&size=${size}&key=${key}`)
    return `https://apis.map.qq.com/ws/staticmap/v2/?center=${center}&zoom=${zoom}&size=${size}&key=${key}`;
	}

  const icon = 'https%3A%2F%2Fyeahtrip.com%2Fmaintenance%2Fdeepfile%2Fdata%2F2025-07-16%2Fupload_bb839474aad84ec1c3bb29f47eca7cd2.png';
  // 取前 50 个点位
  const markers = points.value.slice(0, 50).map(p => `${p.lat},${p.lng}`).join('|');
  const markersParam = `icon:${icon}|${markers}`;

	return `https://apis.map.qq.com/ws/staticmap/v2/?size=${size}&markers=${markersParam}&key=${key}`;
});


const shareData = computed(() => ({
  title: '我的打卡足迹',
  description: '快来看看我的打卡足迹吧~',
  footprintImage: staticMapUrl.value,
  url: window.location.href,
  footprintData: {
    cityCount: cityCount.value,
    placeCount: locationCount.value,
    checkInCount: totalCheckIns.value,
  },
  userInfo: userInfo.value,
}));


// 地图实例
let map = null;
// 标记点图层
let markerCluster = null;
// 自定义标记点集合
let checkInPointList = [];
// 聚合点集合
let clusterPointList = [];
// 最后点击的点位
let lastClickedPoint = null;

// 初始化地图（执行同步任务）
const initMap = () => {
  try {
    // 默认坐标 - 北京天安门
    const center = new TMap.LatLng(39.908823, 116.397470);

    // 创建地图
    map = new TMap.Map('container', {
      center,
      zoom: 14,
      viewMode: '2D', // 视图模式
      showControl: false, // 控件
      baseMap: {
        type: 'vector', // {vector: '矢量图', satellite: '卫星图'}
        buildingRange: [14.5, 20], // 设置建筑物楼块的显示级别
      },
    });

    // 创建点聚合对象
    markerCluster = new TMap.MarkerCluster({
      id: 'cluster',
      map: map,
      enableDefaultStyle: false, // 禁用默认聚合样式，使用自定义样式
      minimumClusterSize: 2, // 最小聚合点数
      geometries: [], // 聚合点数据源
      zoomOnClick: true, // 点击聚合数字放大展开
      maxZoom: 17, // 最大聚合缩放级别
      averageCenter: true, // 聚合点取平均值
    });

    // 监听聚合簇变化
    markerCluster.on('cluster_changed', () =>
      clusterChanged(markerCluster.getClusters())
    );

    // 地图点击事件
    map.on('click', closePointPopup);

    // 设置页面标题
    uni.setNavigationBarTitle({ title: '打卡地图' });
  } catch (err) {
    console.error('初始化地图失败：', err);
  }
};

// 监听聚合变化
const clusterChanged = debounce((clusters) => {
  console.log('集群变化：', clusters);

  // 销毁旧聚合簇生成的覆盖物
  if (clusterPointList.length) {
    clusterPointList.forEach((item) => item.destroy());
    clusterPointList = [];
  }

  if (checkInPointList.length) {
    // 移除不存在点位
    const remainingPoints = [];
    checkInPointList.forEach((item) => {
      const notInClusters = clusters.findIndex(
        (v) => v.geometries.length === 1 && v.geometries[0].id === item.id
      ) === -1;
      const notLastClicked = item.id !== lastClickedPoint?.id;

      if (notInClusters && notLastClicked) {
        item.destroy();
      } else {
        remainingPoints.push(item);
      }
    });
    checkInPointList = remainingPoints;
  }

  // 根据新的聚合簇数组生成新的覆盖物
  clusters.forEach(function (item) {
    if (item.geometries.length > 1) {
      // 聚合点
      let clusterPoint = new ClusterPoint({
        map,
        position: item.center,
        content: item.geometries.length,
      });

      // 聚合点点击事件
      clusterPoint.on('click', () => {
        map.fitBounds(item.bounds);
      });

      clusterPointList.push(clusterPoint);
    } else {
      // 单个点位
      if (checkInPointList.findIndex((v) => v.id === item.geometries[0].id) === -1) {
        let checkInPoint = new CheckInPoint({
          map,
          ...item.geometries[0],
        });

        // 点位点击事件
        checkInPoint.on('click', () => {
          if (lastClickedPoint?.id === checkInPoint.id) return;

          if (lastClickedPoint) {
            lastClickedPoint.dom.classList.remove('pointBig');
          }

          checkInPoint.dom.classList.add('pointBig');
          lastClickedPoint = checkInPoint;

          // 居中显示点位
          map.panTo(checkInPoint.position);

          // 这里可以添加打卡点详情的显示逻辑
        });

        checkInPointList.push(checkInPoint);
      }
    }
  });
}, 200);

// 关闭点位弹窗
const closePointPopup = () => {
  if (lastClickedPoint) {
    lastClickedPoint.dom.classList.remove('pointBig');
    lastClickedPoint = null;
  }
};

// 获取打卡统计数据
const getCheckInStats = async (userId) => {
  try {
    const { data } = await request.get(`/comment/statByScenicId/${userId}`);
    totalCheckIns.value = data.clockNum;
    cityCount.value = data.cityNum;
    locationCount.value = data.addressNum;
  } catch (err) {
    console.error('获取打卡统计失败：', err);
  }
};

// 添加多个标记点
const addMarkers = (points) => {
  if (!points || points.length === 0) return;

  const geometries = points.map((point) => ({
    id: point.id,
    position: new TMap.LatLng(point.lat, point.lng),
    properties: {
      title: point.name,
    },
    details: point,
  }));

  markerCluster.setGeometries(geometries);

  // 调整视野
  const bounds = new TMap.LatLngBounds();
  geometries.forEach((geo) => bounds.extend(geo.position));
  map.fitBounds(bounds, {
    padding: 100,
  });
};

// 加载地图 API 完成
const loadMap = () => {
  window.CheckInPoint || import("./checkInPoint.js");
  window.ClusterPoint || import("./clusterPoint.js");
  initMap();
};

// 加载地图 API
const loadScript = () => {
  window.loadMap = loadMap;
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = 'https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=geometry&callback=loadMap';
  document.body.appendChild(script);
};

const openSharePopup = () => {
  sharePopup.value.open();
};

const onAddCheckIn = () => {
  uni.navigateTo({
    url: '/pages/my/addCheckIn',
  });
};

// 请求打卡数据
const getCheckInData = async () => {
  try {
    const localUserData = await Tool.getUserInfo();
    userInfo.value = localUserData.userInfo;

    getCheckInStats(localUserData.userInfo.userId);

    const { data } = await request.post('/comment/tClockInList', {
      userId: localUserData.userInfo.userId,
      pageSize: 1000,
    });

    console.log('打卡数据：', data);

    // 先转换数据
    const allPoints = data.data.map((item) => ({
      id: item.id,
      lat: item.latitude,
      lng: item.longitude,
      name: item.name,
      city: item.city,
      address: item.address
    }));

    // 用经纬度作为唯一标识符去重
    const uniqueCoordinates = new Map();
    points.value = [];

    allPoints.forEach(point => {
      const coordKey = `${point.lat},${point.lng}`;
      if (!uniqueCoordinates.has(coordKey)) {
        uniqueCoordinates.set(coordKey, true);
        points.value.push(point);
      }
    });

    console.log('去重后打卡点位数量：', points.value.length);
    addMarkers(points.value);
  } catch (error) {
    console.error('获取打卡数据失败：', error);
  }
};

// 生命周期（dom 加载）
onMounted(() => {
  // 加载打卡数据
  getCheckInData();

  // 加载地图
  // #ifdef H5
  if (window.TMap) {
    loadMap();
  } else {
    loadScript();
  }
  // #endif
});

// 生命周期（页面卸载）
onBeforeUnmount(() => {
  if (map) {
    map.destroy();
    map = null;
  }
});
</script>

<style lang="scss" scoped>
.check-in-map {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .header-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10;

    .header-bg {
      width: 100%;
      height: 380rpx;
    }

    .header-content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 36rpx 36rpx 0 42rpx;
      box-sizing: border-box;

      .user-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 10rpx;

        .user-info-left {
          .avatar-wrapper {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .avatar {
              width: 56rpx;
              height: 56rpx;
              border-radius: 50%;
            }

            .name {
              color: #14131f;
              font-size: 32rpx;
              font-weight: 500;
            }
          }

          .check-in-stats {
            margin-top: 27rpx;
            color: #14131f;
            font-size: 32rpx;
            font-weight: 500;
            white-space: nowrap;

            .highlight {
              color: #349fff;
              font-size: 56rpx;
              font-family: Helvetica-Bold, sans-serif;
              font-weight: 700;
              margin: 0 9rpx;
            }
          }

          .location-stats {
            margin-top: 22rpx;
            color: #14131f;
            font-size: 28rpx;
            font-weight: 500;
            white-space: nowrap;

            .highlight {
              color: #349fff;
              font-family: Helvetica-Bold, sans-serif;
              font-weight: 700;
            }
          }
        }

        .share-icon {
          width: 82rpx;
          height: 82rpx;
        }
      }
    }
  }

  .map-view {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .add-check-in-btn {
    position: absolute;
    bottom: 100rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    width: 310rpx;
    height: 88rpx;
    background-color: #fff;
    border-radius: 44rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #e1e1e1;

    .add-icon {
      width: 48rpx;
      height: 48rpx;
    }

    .add-text {
      color: #17181a;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}
</style>

<style></style>
