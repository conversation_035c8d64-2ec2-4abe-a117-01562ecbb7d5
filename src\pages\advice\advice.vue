<template>
	<view class="main">
		<form @submit="formSubmit">
			<view class="select">
				<view class="title">反馈类型</view>
				<picker name="type" @change="bindPickerChange" :range="options">
					<view class="d-flex align-items-center">
						<view class="placeholder" v-if="options[type] === undefined"
							>请选择</view
						>
						<view>{{ options[type] }}</view>
						<view class="y-cell-right icon">
							<uni-icons type="right" color="#000" size="20" />
						</view>
					</view>
				</picker>
			</view>
			<view class="wrap">
				<view class="textarea">
					<textarea
						name="content"
						placeholder="请输入反馈信息"
						:maxlength="300"
						@input="onTextAreaChange" />
					<view class="length">{{ text.length }}/300</view>
				</view>
				<view class="divider"></view>
				<view class="picture">
					<view class="d-flex align-items-center">
						<view>上传图片</view>
						<view class="description">（最多 6 张）</view>
					</view>
					<view class="upload">
						<y-upload upload v-model="picture" :limit="6" />
					</view>
				</view>
			</view>

			<view class="contact">
				<input name="phone" type="text" placeholder="手机/邮箱/QQ（选填）" />
			</view>
			<view class="sub">
				<button
					class="sub-btn"
					form-type="submit"
					type="primary"
					:loading="loading">
					提交
				</button>
			</view>
		</form>
	</view>
</template>
<script setup lang="ts">
import request from "@/utils/request.js"
import { getRoute } from "@/utils/tool.js"
import { ref, watchEffect } from "vue"

// 类型
const type = ref(-1)
// 内容
const text = ref("")
// 上传图片
const picture = ref("")

const userInfo = ref({
	username: "",
	nickname: ""
})
onMounted(async () => {
	const { userInfo: user } = await Tool.getUserInfo()
	userInfo.value = user
})

const options = ["景区服务", "景区门票", "产品体验", "产品功能", "其它问题"]

const loading = ref(false)

const bindPickerChange = e => {
	type.value = e.detail.value
}

const onTextAreaChange = e => {
	text.value = e.detail.value
}

const submitRequest = async params => {
	loading.value = true
	await request.post(`/feedback/add`, params)
	loading.value = false
	uni.showToast({
		title: "提交成功",
		duration: 1000
	})
	setTimeout(() => {
		Tool.goPage.back()
	}, 1000)
}

const formSubmit = e => {
	const formData = e.detail.value
	// 校验
	if (type.value === -1) {
		uni.showToast({
			icon: "none",
			title: "请选择反馈类型"
		})
		return
	}

	if (text.value.length === 0) {
		uni.showToast({
			icon: "none",
			title: "请输入反馈信息"
		})
		return
	}

	submitRequest({
		...formData,
		type: formData.type + 1 || 1,
		imgUrl: picture.value,
		accountNumber: userInfo.value.username,
		nickName: userInfo.value.nickname,
		storeId: getRoute.params().storeId
	})
}

watchEffect(() => {
	console.log(picture.value)
})
</script>
<style lang="scss" scoped>
.main {
	background: #f1f1f1;
	padding: 30rpx;
	height: 100%;

	.select {
		height: 110rpx;
		background: #ffffff;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 14rpx;

		.title {
			font-size: 30rpx;
			color: #14131f;
			&::before {
				width: 15rpx;
				font-size: 28rpx;
				color: #f43636;
				line-height: 40rpx;
				content: "*";
			}
		}
		.placeholder {
			font-size: 30rpx;
			color: rgba(20, 19, 31, 0.51);
		}

		.icon {
			margin-top: 4rpx;
		}
	}

	.wrap {
		margin: 20rpx 0;
		padding: 20rpx 30rpx;
		// height: 800rpx;
		height: max-content;
		background: #ffffff;
		border-radius: 12rpx;
		.textarea {
			.length {
				text-align: right;
				font-size: 28rpx;
				color: rgba(20, 19, 31, 0.51);
			}
		}

		.divider {
			border: 1rpx dashed #d6d6d6;
			margin: 30rpx 0;
		}
		.picture {
			.description {
				height: 30rpx;
				font-size: 26rpx;
				font-weight: 400;
				color: rgba(20, 19, 31, 0.5);
				line-height: 30rpx;
			}
			.upload {
				margin-top: 30rpx;
			}
		}
	}

	.contact {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		height: 110rpx;
		background: #ffffff;
		border-radius: 12rpx;
	}

	.sub {
		margin-top: 60rpx;
		display: flex;
		width: 100%;
		justify-content: center;
		.sub-btn {
			width: 600rpx;
			height: 88rpx;
			background: var(--theme-color);
			border-radius: 12rpx;
		}
	}
}
</style>
