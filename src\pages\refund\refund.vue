<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>申请退款</y-nav-bar>
	<view class="refund">
		<view class="y-list">
			<view class="y-list-item">
				<view class="y-list-item-left">商品金额</view>
				<view class="y-list-item-right">￥{{
					tParams.isFail == 1 ? tParams.refundAmount : tParams.refundOrder
				}}</view>
			</view>
			<view class="y-list-item">
				<view class="y-list-item-left">手续费</view>
				<view class="y-list-item-right">￥{{ tParams.isFail == 1 ? "0.00" : tParams.refundFee }}</view>
			</view>
			<view class="y-list-item">
				<view class="y-list-item-left">退款金额</view>
				<view class="y-list-item-right" :style="{ color: 'red' }">￥{{ tParams.refundAmount }}</view>
			</view>
		</view>
		<view class="y-list">
			<view class="y-list-item select">
				<view class="y-list-item-left">退款原因</view>
				<view class="y-list-item-right" @tap="onClick">
					<view :class="true ? 'cause' : 'cause active'">{{
						selectValue
					}}</view>
					<moreIcon class="icon" color="#000000" direction="toLeft" />
				</view>
			</view>
		</view>
		<y-button :disable="disabled" @tap="refund">提交</y-button>
		<y-popup v-model="isPopUpWin" title="请选择原因">
			<!-- <view class="">{{ticketInfo.note}}</view> -->
		</y-popup>
	</view>
</template>

<script setup>
import { reactive, ref, onBeforeMount, watch } from "vue"
import request from "@/utils/request.js"
import { onLoad } from "@dcloudio/uni-app"
const props = defineProps({})
const tParams = ref({})

const isPopUpWin = ref(false) //原因选择弹窗
const selectValue = ref("请选择") //原因
const state = reactive({
	rules: [],
	itemList: [
		"信息填错重新购买",
		"证件未带/带错",
		"景区关闭",
		"商家主动联系要求退订",
		"计划有变不去了",
		"其他原因"
	]
})
const disabled = ref(true)
onBeforeMount(async () => {
	const t = JSON.parse(uni.getStorageSync("refund_data"))
	tParams.value = {
		...t
	}
	console.log("tParams===", tParams)
	if (t.isFail != 1) {

		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		await checkRetreat()
		uni.hideLoading()
	}
})
watch(
	() => selectValue.value,
	value => {
		if (value != "请选择") {
			disabled.value = false
		}
	}
)
//选择原因
const onClick = () => {
	uni.showActionSheet({
		itemList: state.itemList, //不能超过 6 个
		success: function (res) {
			console.log(res)
			selectValue.value = state.itemList[res.tapIndex]
		},
		fail: function (res) {
			console.log(res.errMsg)
		}
	})
	// isPopUpWin.value=true
}
//检验退票
const checkRetreat = async () => {
	try {
		console.log("--tParams.value--")
		console.log(tParams.value)
		const { code, data } = await request.post(
			`/ticketRetreat/checkRetreat`,
			tParams.value.ticketList
		)
		state.rules = data
	} catch (err) {
		console.log(err)
	}
}
//退票
const refund = async () => {
	if (disabled.value) return
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})

		if (tParams.value.isFail === 1) {
			//退单
			const params = {
				orderId: tParams.value.orderId,
				remark: selectValue.value
			}
			const { code, data } = await request.put(`/order/orderRetreat`, params)
			uni.showToast({
				title: "退单成功！",
				icon: "success",
				mask: true,
				duration: 2000,
				success: () => {
					Tool.goPage.replace(
						`/pages/orderDetail/orderDetail?orderId=${tParams.value.orderId}`
					)
					// &refundId=${data.refundId}
				}
			})
		} else {
			//退票
			// let params = {
			// 	refundList: [],
			// 	type: 0, // 5 为窗口退票，0 为 h5 退票
			// 	remark: selectValue.value,
			// };
			// state.rules.map((n) => {
			// 	params.refundList.push({
			// 		payType: tParams.value.payType * 1, // 支付方式
			// 		ticketNumber: n.ticketId,
			// 	});
			// });
			const params = {
				remark: selectValue.value,
				refundSource: "H5"
			}
			params.data = tParams.value.ticketList.map(e => {
				return {
					ticketNumber: e
				}
			})
			const { code, data } = await request.post(`/order/ticketRefund`, params)
			uni.showToast({
				title: "退票成功！",
				icon: "success",
				mask: true,
				duration: 2000,
				success: () => {
					Tool.goPage.tab("order")
					// Tool.goPage.replace(
					// 	`/pages/orderDetail/orderDetail?orderId=${tParams.value.orderId}&refundId=${data.refundId}`
					// );
				}
			})
		}
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
</script>

<style lang="scss" scoped>
.refund {
	background: #f6f6f6;
	height: 100%;

	>.info-box {
		border-radius: 24rpx;

		>.item {
			display: flex;
			align-items: center;
			padding: 40rpx;
			justify-content: space-between;
			font-size: 34rpx;
			background: #ffffff;
			color: #050505;

			&.bottom-line {
				border-bottom: 1px solid rgb(157 157 157 / 39%);
			}
		}

		>.select {
			margin-top: 20rpx;

			.value {
				display: flex;
				justify-content: center;
				align-items: center;

				>.cause {
					margin-right: 15rpx;
					opacity: 0.5;
				}

				>.active {
					width: 180rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					opacity: 1;
				}
			}
		}

		.but {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 20rpx;
			height: 96rpx;
			font-size: 36rpx;
			font-weight: 500;
			color: #ffffff;
			background: #7ed3da;
			border-radius: 25rpx;
			margin-top: 187rpx;
		}
	}
}
</style>
