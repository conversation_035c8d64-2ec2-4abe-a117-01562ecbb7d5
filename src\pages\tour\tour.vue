<template>
  <view class="tour">
    <tab
      v-if="!lineId && !navPointId && show"
      ref="tabRef"
      :scenicName="scenicName"
      :isList="isList"
      @setIsList="isList = false"
      @setPointList="setPointList"
    />
    <view class="contant">
      <!-- 地图容器 -->
      <view id="container"></view>
      <!-- 地图控件 -->
      <view class="control">
        <view :style="`opacity: ${show ? 1 : 0};`" class="top">
          <view class="top--left">
            <!-- 天气 -->
            <view v-if="weatherSrc && weatherText" class="weather">
              <svg class="icon" aria-hidden="true">
                <use :xlink:href="`#${weatherSrc}`"></use>
              </svg>
              <text>{{ weatherText }}</text>
            </view>
            <!-- 调试文本 -->
            <!-- <view style="width: 100%; word-break: break-all;">{{text}}</view> -->
          </view>
          <view class="top--right">
            <!-- 卫星图/讲解 -->
            <view class="tool painted">
              <view class="tool--top" @click="baseMap">
                <image
                  :src="isSatellite ? $satelliteActive : $satellite"
                  mode="aspectFit"
                />
                <text>卫星图</text>
              </view>
              <view class="tool--center"></view>
              <view class="tool--bottom" @click="setExplain">
                <image
                  :src="isExplain ? $explainActive : $explain"
                  mode="aspectFit"
                />
                <text>讲解</text>
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="!lineId && !navPointId && show"
          class="bottom_center painted"
          @click="
            Tool.goPage.push(
              `/pages/tourLineList/tourLineList?guideId=${guideId}`
            )
          "
        >
          <view>
            <span>游览路线</span>
          </view>
        </view>
        <view class="bottom">
          <view
            class="bottom--top"
            :class="{ bottom_margin: lineId || navPointId || !lastPoint }"
          >
            <view class="bottom--top--left">
              <!-- 定位 -->
              <image
                :class="{ loading: loading }"
                class="loca painted"
                src="@/static/image/tour/tour_loca.svg"
                mode="aspectFit"
                @click="loading || local()"
              />
            </view>
            <view class="bottom--top--right">
              <!-- 缩放 -->
              <view v-if="show" class="zoom painted">
                <image
                  src="@/static/image/tour/tour_zoom_add.svg"
                  mode="aspectFit"
                  @click="map.zoomTo(map.getZoom() + 1)"
                />
                <image
                  src="@/static/image/tour/tour_zoom_sub.svg"
                  mode="aspectFit"
                  @click="map.zoomTo(map.getZoom() - 1)"
                />
              </view>
              <!-- 点位列表 -->
              <image
                v-if="!lineId && !navPointId && show"
                class="menu painted"
                src="@/static/image/tour/tour_menu.svg"
                mode="aspectFit"
                @click="isList = true"
              />
            </view>
          </view>
          <!-- 点位弹窗 -->
          <view
            v-if="!lineId && !navPointId"
            class="bottom--bottom point_pop painted"
            :class="{ point_show: lastPoint }"
          >
            <view class="point_pop--top">
              <view
                class="point_pop--top--left"
                @click="
                  lastPoint?.details.pointType < 6 &&
                    Tool.goPage.push(
                      `/pages/tourDetail/tourDetail?guideId=${guideId}&id=${lastPoint.id}&distance=${lastPoint.details.distance}`
                    )
                "
              >
                <text class="title">{{ lastPoint?.content }}</text>
                <text class="distance"
                  >距您{{
                    pointDistance([
                      lastPoint?.details.latitude,
                      lastPoint?.details.longitude,
                    ])
                  }}公里</text
                >
                <text v-if="lastPoint?.details.pointType < 6" class="content">{{
                  setPointIntroContent(lastPoint?.details)
                }}</text>
              </view>
              <audio-player
                v-if="lastPoint?.details.pointType < 6"
                :lastPoint="lastPoint"
              />
            </view>
            <view class="point_pop--bottom">
              <view class="point_pop--bottom--left" @click="closePointPop"
                >关闭</view
              >
              <view class="point_pop--bottom--center"></view>
              <view class="point_pop--bottom--right" @click="nav(lastPoint.id)"
                >前往</view
              >
            </view>
          </view>
          <!-- 路线弹窗 -->
          <view v-if="lineId" class="bottom--bottom line_pop painted">
            <view class="title">{{ lineDetail.lineName }}</view>
            <view class="tips">
              <text>{{ lineDetail.pointNumber }}个点位</text>
              <text>{{ lineDetail.estimateHour }}小时</text>
              <text>{{ lineDetail.estimateDistance || 0 }}公里</text>
            </view>
            <view class="list">
              <view
                v-for="(item, index) in linePointList"
                :key="index"
                :class="{ active: lastPoint?.index == item.index }"
                @click="clusterPointClick(item)"
              >
                <view class="num">{{ item.index }}</view>
                <view class="name">{{ item.content }}</view>
              </view>
            </view>
          </view>
          <!-- 航线弹窗 -->
          <view v-if="navPointId" class="bottom--bottom nav_pop painted">
            <view class="nav_pop--top">
              <text>距您{{ (navDetail.distance / 1000)?.toFixed(2) }}公里</text>
              <text>预计耗时{{ navDetail.duration || 0 }}分钟</text>
              <image
                src="@/static/image/tour/tour_refresh.svg"
                mode="aspectFit"
                :class="{ loading: loading }"
                @click="loading || getNavDetail()"
              />
            </view>
            <view class="nav_pop--bottom" @click="Tool.goPage.back()">
              <text>关闭</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import $pause from "@/static/image/tour/tour_pause.svg";
import $play from "@/static/image/tour/tour_play.svg";
import $explain from "@/static/image/tour/tour_tool_explain.svg";
import $explainActive from "@/static/image/tour/tour_tool_explain_active.svg";
import $satellite from "@/static/image/tour/tour_tool_satellite.svg";
import $satelliteActive from "@/static/image/tour/tour_tool_satellite_active.svg";
import "@/utils/iconfont.js";
import request from "@/utils/request.js";
import eventBus from "@/utils/eventBus.js";
import {
  debounce,
  getRoute,
  pointDistance,
  transformTime,
} from "@/utils/tool.js";
import { onHide, onUnload } from "@dcloudio/uni-app";
import { nextTick, onMounted, ref } from "vue";
import tab from "./component/tab";
import audioPlayer from "./component/audioPlayer";
const { guideId, lineId, navPointId } = getRoute.params();
const show = ref(false);
const text = ref(1);
const weatherSrc = ref();
const weatherText = ref();
const isSatellite = ref(false);
const isExplain = ref(false);
const lastPoint = ref(null);
const lineDetail = ref([]);
const lineList = ref([]);
const navDetail = ref({ distance: 0 });
const navList = ref([]);
const linePointList = ref([]);
const isPlay = ref(false);
const duration = ref(0);
const isList = ref(false);
const scenicName = ref();
const tabRef = ref(null);
const navTo = ref(); // 目标点位
const loading = ref(true); // 定位过程
const isGuideExplaining = ref(false); // 是否正在进行导览讲解
let timeId = null; // 轮询定时器
let innerAudioContext = null; // 音频对象
let isClickPoint = false; // 点位是否被点击
let map = null; // 地图实例
let locaPoint = null; // 当前定位点实例
let locaLine = null; // 航线实例
let markerLayer = null; // 点标记实例
// let circle = null // 圆形覆盖物实例
let markerCluster = null; // 聚合对象
let clusterBubbleList = []; // 聚合气泡集
let clusterPointList = []; // 聚合点位集
let bounds = null; // 地图范围
let center = null; // 中心点坐标（景区）
let pointId = "";
let pointList = [];

// 触发语音讲解
const triggerExplain = () => {
  for (const item of pointList) {
    if (item.distance * 1000 <= item.aiExplainTriggerRange) {
      setPointList(pointList, item.id);
      console.log(innerAudioContext.paused);
      if (innerAudioContext.paused) setTimeout(playAudio, 500);
      return;
    }
  }
};
// 语音讲解开关
const setExplain = () => {
  if (!window.navFrom) {
    uni.showToast({
      title: "请先开启定位功能",
      icon: "none",
    });
    return;
  }
  isExplain.value = !isExplain.value;
  if (isExplain.value) {
    triggerExplain();
  }
};
// 更新天气
const weather = () => {
  const skycon = {
    CLEAR_DAY: "icon-qingtian",
    CLEAR_NIGHT: "icon-qingtian",
    PARTLY_CLOUDY_DAY: "icon-Group",
    PARTLY_CLOUDY_NIGHT: "icon-Group",
    CLOUDY: "icon-yintian",
    LIGHT_HAZE: "icon-wumai",
    MODERATE_HAZE: "icon-wumai",
    HEAVY_HAZE: "icon-wumai",
    LIGHT_RAIN: "icon-xiaoyu",
    MODERATE_RAIN: "icon-zhongyu",
    HEAVY_RAIN: "icon-dayu",
    STORM_RAIN: "icon-baoyu",
    FOG: "icon-wu",
    LIGHT_SNOW: "icon-xiaoxue",
    MODERATE_SNOW: "icon-zhongxue",
    HEAVY_SNOW: "icon-daxue",
    STORM_SNOW: "icon-baoxue",
    DUST: "icon-Group1",
    SAND: "icon-Group1",
    WIND: "icon-Group1",
  };
  request
    .get(
      `https://prod.shukeyun.com/data/api/weather/realtime/?lng=${center.lng.toFixed(
        13
      )}&lat=${center.lat.toFixed(13)}`
    )
    .then(({ data }) => {
      weatherSrc.value = skycon[data.weather_english];
      weatherText.value = `${data.real_time_num}℃ ${data.weather}`;
    });
};
// 地图自适应点位范围
const self = (arr) => {
  const bounds = new TMap.LatLngBounds();
  // 扩大 bounds 范围
  arr.forEach(({ position }) => {
    bounds.extend(position);
  });
  // 设置地图可视范围
  map.fitBounds(bounds, {
    padding: { top: 100, bottom: 100, left: 50, right: 50 },
    maxZoom: 18,
  });
};

const setPointIntroContent = (data) => {
  const aiVoice = Tool.getAiVoice(data);
  return aiVoice.pointIntro;
};
// 点击地图点位
const clusterPointClick = (clusterPoint) => {
  isClickPoint = true;
  if (lastPoint.value?.id == clusterPoint.id) return;
  lastPoint.value && lastPoint.value.dom.classList.remove("pointBig");
  clusterPoint.dom.classList.add("pointBig");

  lastPoint.value = clusterPoint;

  map.panTo(clusterPoint.position);
};
// 关闭点位弹窗
const closePointPop = () => {
  if (!isClickPoint && lastPoint.value) {
    lastPoint.value.dom.classList.remove("pointBig");
    // 聚合点位不存在时销毁
    if (
      !lineId &&
      !navPointId &&
      !markerCluster
        .getClusters()
        .find(
          (v) =>
            v.geometries.length == 1 && v.geometries[0].id == lastPoint.value.id
        )
    ) {
      lastPoint.value.destroy();
      // 从聚合点位列表中移除
      const index = clusterPointList.findIndex(
        (item) => item.id == lastPoint.value.id
      );
      if (index != -1) clusterPointList.splice(index, 1);
    }
    lastPoint.value = null;
  } else {
    isClickPoint = false;
  }
};

// 切换卫星图
const baseMap = () => {
  isSatellite.value = !isSatellite.value;
  map.setBaseMap({
    type: isSatellite.value ? "satellite" : "vector",
    buildingRange: [14.5, 20],
  });
};
// 添加标记点（定位）、圆形区域（精度范围）position, radius
const addMarker = () => {
  locaPoint?.destroy();
  locaPoint = new LocaPoint({
    map,
    position: new TMap.LatLng(window.navFrom[0], window.navFrom[1]),
  });
  // markerLayer.setGeometries([])
  // markerLayer.add([{ position }])
  // radius && circle.setGeometries([{
  //   styleId: 'circle',
  //   center: position, // 圆形中心点坐标
  //   radius,	// 半径（单位：米）
  // }])
};
// 导航
const nav = (id) => {
  if (
    window.navFrom &&
    bounds.contains(new TMap.LatLng(window.navFrom[0], window.navFrom[1]))
  ) {
    Tool.goPage.push(`/pages/tour/tour?guideId=${guideId}&navPointId=${id}`);
  } else {
    uni.showToast({
      title: "您当前不在景区范围内",
      icon: "none",
    });
  }
};
// 定位
const local = () => {
  if (window.navFrom)
    var latLng = new TMap.LatLng(window.navFrom[0], window.navFrom[1]);
  if (window.navFrom && bounds.contains(latLng)) {
    map.panTo(latLng);
    return true;
  } else {
    uni.showToast({
      title: "您当前不在景区范围内",
      icon: "none",
    });
    map.fitBounds(bounds, { padding: 50 });
  }
};
// 获取航线数据
const getNavDetail = async () => {
  // 定位到当前位置
  if (!local()) return;
  // 获取航线
  const { data } = await request.get("/navigation/line/walking", {
    startLatitudeLongitude: window.navFrom.join(","),
    endLatitudeLongitude: navTo.value.join(","),
  });
  const ret = JSON.parse(data);
  navDetail.value = ret.result.routes[0];
  // 从结果中取出路线坐标串
  var coors = ret.result.routes[0].polyline,
    pl = [];
  // 坐标解压（返回的点串坐标，通过前向差分进行压缩，因此需要解压）
  var kr = 1000000;
  for (var i = 2; i < coors.length; i++) {
    coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
  }
  // 将解压后的坐标生成LatLng数组
  for (var k = 0; k < coors.length; k += 2) {
    pl.push(new TMap.LatLng(coors[k], coors[k + 1]));
  }
  // 设置航线
  locaLine.updateGeometries({ id: "pl", paths: pl });
};
// 播放音频
const playAudio = () => {
  if (!lastPoint.value?.details.aiAudioUrl.trim()) return;
  isPlay.value = !isPlay.value;
  console.log('playAudio');
  if (timeId) {
    clearInterval(timeId);
    timeId = null;
  }
  if (isPlay.value) {
    innerAudioContext.play();
    timeId = setInterval(() => {
      if (duration.value <= 0 && timeId) {
        clearInterval(timeId);
        timeId = null;
      } else {
        duration.value--;
      }
    }, 1000);
  } else {
    innerAudioContext.pause();
  }
};
// 音频监听
const audioListen = () => {
  // 音频对象
  innerAudioContext = uni.createInnerAudioContext();
  innerAudioContext.onEnded(() => {
    isPlay.value = false;
    duration.value = Tool.getAiVoice(lastPoint.value?.details).aiAudioDuration;
    timeId && clearInterval(timeId);
  });
};


// 位置监听
const locaListen = () =>
  new Promise((resolve, reject) => {
    // 初始化位置
    if (window.navFrom) {
      resolve();
    } else {
      uni.getLocation({
        type: "gcj02",
        isHighAccuracy: true, // 高精度
        highAccuracyExpireTime: 5000, // 超时
        success: (res) => {
          // text.value = '高精度定位成功：' + JSON.stringify(res)
          window.navFrom = [res.latitude, res.longitude];
          resolve();
        },
        fail: (err) => {
          // text.value = '高精度定位失败：' + JSON.stringify(err)
          reject();
        },
      });
    }
    // let i = 0
    // 更新位置
    uni.onLocationChange((res) => {
      // text.value = '位置监听：' + ++i + JSON.stringify(res)
      window.navFrom = [res.latitude, res.longitude];
      updataPointList();
      addMarker();
    });
    // 开启位置监听
    uni.startLocationUpdate({
      type: "gcj02",
      success: (res) => {
        // text.value = '开启位置监听成功' + JSON.stringify(res)
      },
      fail: (err) => {
        // text.value = '开启位置监听失败：' + JSON.stringify(err)
      },
    });
  });
// 罗盘监听
const compassListen = () => {
  uni.onCompassChange((res) => {
    nextTick(() => {
      // text.value = '罗盘监听：' + res.direction
      console.log("罗盘监听：", res);
      // 更新当前位置点位样式
      locaPoint?.dom
        .querySelector("img")
        .setAttribute("style", `transform: rotate(${res.direction}deg);`);
    });
  });
  uni.startCompass({
    success: (res) => {
      // text.value = '开启罗盘监听成功'
      console.log("开启罗盘监听成功");
    },
    fail: (err) => console.error("开启罗盘监听失败：", err),
  });
};
// 路线指令
const lineInstruct = () => {
  request.get("/navigation/line/info", { id: lineId }).then(({ data }) => {
    const list = JSON.parse(data.lineContent);
    const clusterPointList = [];
    const paths = list.map((item) => {
      item.distance = pointDistance([item.latitude, item.longitude]);
      const latLng = new TMap.LatLng(item.latitude, item.longitude);
      // 添加新点位
      if (item.pointId) {
        let clusterPoint = new Point({
          map,
          id: item.pointId,
          content: item.pointName,
          position: latLng,
          details: item,
          index: clusterPointList.length + 1,
        });
        // 路线点位点击事件
        clusterPoint.on("click", () => clusterPointClick(clusterPoint));
        clusterPoint.on("leftClick", () => {
          console.log("leftClick");
          Tool.goPage.push(
            `/pages/tourDetail/tourDetail?guideId=${guideId}&id=${clusterPoint.id}&distance=${clusterPoint.details.distance}`
          );
        });
        clusterPoint.on("rightClick", () => {
          console.log("rightClick");
          nav(clusterPoint.id);
        });
        clusterPointList.push(clusterPoint);
      }
      return latLng;
    });
    new TMap.MultiPolyline({
      map,
      styles: {
        default: new TMap.PolylineStyle({
          width: 8,
          borderWidth: 2,
          color: "#21b977",
          borderColor: "#178153",
          lineCap: "round",
          showArrow: true,
          arrowOptions: {
            height: 6,
          },
        }),
      },
      geometries: [{ paths }],
    });
    // 地图自适应
    self(clusterPointList);
    lineDetail.value = data;
    lineList.value = list;
    linePointList.value = clusterPointList;
  });
};
// 航线指令
const navInstruct = () => {
  // 获取目的地详情
  request.get("/navigation/point/info", { id: navPointId }).then(({ data }) => {
    navTo.value = [data.latitude, data.longitude];
    // 创建目的地点位
    new Point({
      map,
      id: data.id,
      content: data.pointName,
      position: new TMap.LatLng(data.latitude, data.longitude),
      details: { ...data, publicizeUrl: data.publicizeList?.[0] },
    }).dom.classList.add("pointBig");
    // 创建航线对象
    locaLine = new TMap.MultiPolyline({
      id: "pl",
      map,
      styles: {
        default: new TMap.PolylineStyle({
          width: 6,
          borderWidth: 2,
          color: "#21b977",
          borderColor: "#178153",
          lineCap: "round",
          showArrow: true,
          arrowOptions: {
            height: 6,
          },
        }),
      },
    });
    getNavDetail();
  });
};
// 监听聚合变化
const clusterChanged = debounce((clusters) => {
  console.log(clusters);
  // 销毁旧聚合簇生成的覆盖物
  if (clusterBubbleList.length) {
    clusterBubbleList.forEach((item) => item.destroy());
    clusterBubbleList = [];
  }
  if (clusterPointList.length) {
    // 移除不存在点位
    const sum = [];
    clusterPointList.forEach((item, index) => {
      const notNew =
        clusters.findIndex(
          (v) => v.geometries.length == 1 && v.geometries[0].id == item.id
        ) == -1;
      const notLast = item.id != lastPoint.value?.id;
      if (notNew && notLast) {
        item.destroy();
      } else {
        sum.push(item);
      }
    });
    clusterPointList = sum;
  }
  // 根据新的聚合簇数组生成新的覆盖物和点标记图层
  clusters.forEach(function (item) {
    if (item.geometries.length > 1) {
      // 聚合气泡
      // 创建聚合气泡
      let clusterBubble = new ClusterBubble({
        map,
        position: item.center,
        content: item.geometries.length,
      });
      // 气泡点击事件
      clusterBubble.on("click", () => {
        isClickPoint = true;
        map.fitBounds(item.bounds);
      });
      clusterBubbleList.push(clusterBubble);
    } else {
      // 聚合点位
      // 添加新点位
      if (
        clusterPointList.findIndex((v) => v.id == item.geometries[0].id) == -1
      ) {
        let clusterPoint = new Point({
          map,
          ...item.geometries[0],
        });
        const pointClick = () => {
          console.log('pointClick???????')
          if (pointId) {
            pointId = null;
            isClickPoint = false;
          } else {
            isClickPoint = true;
          }
          if (lastPoint.value?.id == clusterPoint.id) return;
          lastPoint.value && lastPoint.value.dom.classList.remove("pointBig");
          clusterPoint.dom.classList.add("pointBig");
          lastPoint.value = clusterPoint;
          
          // 触发点击事件，通知 audioPlayer 组件停止播放
          eventBus.emit('pointClick');
          
          // 音频处理
          // const src = clusterPoint.details.aiAudioUrl
          const src = Tool.getAiVoice(clusterPoint.details).aiAudioUrl;
          if (innerAudioContext.src != src) {
            if (timeId) {
              clearInterval(timeId);
              timeId = null;
            }
            isPlay.value = false;
            innerAudioContext.src = src;
            duration.value = Tool.getAiVoice(
              clusterPoint.details
            ).aiAudioDuration;
          } else if (!duration.value && duration.value != 0) {
            // 异常音频处理
            duration.value = Tool.getAiVoice(
              clusterPoint.details
            ).aiAudioDuration;
          }
        };
        // 点位点击事件
        clusterPoint.on("click", pointClick);
        clusterPointList.push(clusterPoint);
        // 执行点位弹窗指令
        if (pointId && clusterPoint.id == pointId) pointClick();
      }
    }
  });
}, 200);
// 设置聚合点位
const setPointList = (list, id, notUpdateView) => {
  pointList = list;
  console.log('pointList');
  console.log(pointList);
  // 不触发点位更新
  if (notUpdateView) return;
  pointId = id;
  // 激活点位
  let center = null;
  // 点位数组
  const paintArr = list.map((item) => {
    const obj = {
      id: item.id,
      type: item.pointType,
      content: item.pointName,
      position: new TMap.LatLng(item.latitude, item.longitude),
      details: item,
    };
    if (obj.id == id) center = obj.position;
    return obj;
  });
  // 地图自适应
  if (id) {
    // 拦截已激活点位列表渲染
    if (lastPoint.value?.id == id) return map.panTo(lastPoint.value.position);
    // 拦截已存在点位列表渲染
    for (const item of clusterPointList) {
      if (item.id == id) {
        map.panTo(item.position);
        item.onClick();
        return;
      }
    }
    // 携带点位弹窗
    map.easeTo({
      center,
      zoom: 18,
    });
  } else {
    self(paintArr);
  }
  // 关闭弹窗
  closePointPop();
  // 设置聚合点
  markerCluster.setGeometries(paintArr);
};
// 更新点位列表数据
const updataPointList = () => {
  
  // 更新点位列表（pointId:null 无点位弹窗，notUpdateView:true 不触发地图点位更新）
  tabRef.value.initPointList(null, true);
  // 弹窗点位局部更新
  clusterPointList.map((item) => {
    if (lastPoint.value && lastPoint.value.id == item.id) {
      lastPoint.value.details = {
        ...lastPoint.value.details,
        distance: item.details.distance,
      };
    }
  });
  // 触发最近点位语音播放
  isExplain.value && triggerExplain();
};
// 初始化数据、监听
const initData = () => {
  if (lineId) lineInstruct();
  locaListen().then(() => {
    loading.value = false;
    addMarker();
    if (navPointId) {
      // 导航
      navInstruct();
    } else if (lineId) {
      // 路线
      linePointList.value.map((item) => {
        item.details.distance = pointDistance([
          item.details.latitude,
          item.details.longitude,
        ]);
        item.dom.querySelector(
          ".distance"
        ).innerText = `距您${item.details.distance}公里`;
      });
    } else {
      // 点位
      // 首次进入景区范围提示
      if (!window.navFrom) {
        uni.showToast({
          title: "定位失败",
          icon: "none",
        });
      } else if (
        !bounds.contains(new TMap.LatLng(window.navFrom[0], window.navFrom[1]))
      ) {
        uni.showToast({
          title: "您当前不在景区范围内",
          icon: "none",
        });
      }
      updataPointList();
    }
  });
  audioListen();
  weather();
};
// 初始化地图（执行同步任务）
const initMap = async () => {
  // 获取景区数据
  const {
    data: { latitude, longitude, scenicRange, scenicName: title },
  } = await request.get(`/scenic/address/info/${guideId}`);
  scenicName.value = title;
  // 设置页面标题
  uni.setNavigationBarTitle({ title });
  // 创建地图范围（景区）
  const paths = [];
  bounds = new TMap.LatLngBounds();
  JSON.parse(scenicRange).map((item) => {
    const latLng = new TMap.LatLng(...item);
    paths.push(latLng);
    bounds.extend(latLng);
  });

  // 获取边界的东北角和西南角坐标
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();

  // 计算中心点
  const center = bounds.getCenter();

  // 计算当前边界的宽度和高度
  const width = ne.lng - sw.lng;
  const height = ne.lat - sw.lat;

  // 设置扩大的比例
  const scale = 2;

  // 计算新的边界坐标
  const newWidth = width * scale;
  const newHeight = height * scale;
  const newSw = new TMap.LatLng(
    center.lat - newHeight/2,
    center.lng - newWidth/2
  );
  const newNe = new TMap.LatLng(
    center.lat + newHeight/2,
    center.lng + newWidth/2
  );

  // 创建新的扩大后的边界
  const expandedBounds = new TMap.LatLngBounds(newSw, newNe);

  // 使用扩大后的边界
  map = new TMap.Map("container", {
    center,
    boundary: expandedBounds,
    viewMode: "2D", // 视图模式
    showControl: false, // 控件
    baseMap: {
      type: "vector", // {vector: '矢量图', satellite: '卫星图'}
      // features: ['base', 'building2d'], // 仅渲染：道路及底面 (base) + 2d 建筑物 (building2d)
      buildingRange: [14.5, 20], // 设置建筑物楼块的显示级别
    },
  });
  // 区域高亮
  map.enableAreaHighlight({ paths, shadeColor: "rgba(41,91,255,0.08)" });
  // 区域描边
  new TMap.MultiPolygon({
    map, // 显示多边形图层的底图
    styles: {
      // 多边形的相关样式
      polygon: new TMap.PolygonStyle({
        color: "rgba(0,0,0,0)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "rgba(41,91,255,1)", // 边线颜色
        borderWidth: 2, // 边线宽度
        borderDashArray: [10, 5], // 虚线数组
      }),
    },
    geometries: [
      {
        id: "polygon", // 多边形图形数据的标志信息
        styleId: "polygon", // 样式 id
        paths, // 多边形的位置信息
      },
    ],
  });
  // 自适应显示区域
  map.fitBounds(bounds, { padding: 50 });
  /** 初始化地图之后操作 */
  // 创建点标记
  markerLayer = new TMap.MultiMarker({ map });
  // // 创建圆形覆盖物
  // circle = new TMap.MultiCircle({map});
  // 创建点聚合对象
  markerCluster = new TMap.MarkerCluster({
    id: "cluster", // 图层 id
    map, // 设置点聚合显示在哪个 map 对象中
    enableDefaultStyle: false, // 使用默认样式
    minimumClusterSize: 2, // 最小聚合点数：2 个
    geometries: [],
    zoomOnClick: true, // 点击聚合数字放大展开
    // gridSize: 60, // 聚合算法的可聚合距离，即距离小于该值的点会聚合至一起，默认为 60，以像素为单位
    averageCenter: true, // 每个聚和簇的中心是否应该是聚类中所有标记的平均值
    maxZoom: 17, // 采用聚合策略的最大缩放级别，若地图缩放级别大于该值，则不进行聚合，标点将全部被展开
  });
  // 监听聚合簇变化
  markerCluster.on("cluster_changed", () =>
    clusterChanged(markerCluster.getClusters())
  );
  // 地图点击事件
  map.on("click", closePointPop);
  // 渲染组件
  show.value = true;
  initData();
};
// 加载地图 API 完成
const loadMap = () => {
  window.ClusterBubble || import("./clusterBubble.js");
  window.Point || import("./clusterPoint.js");
  window.LocaPoint || import("./locaPoint.js");
  initMap();
};
// 加载地图 API
const loadScript = () => {
  window.loadMap = loadMap;
  var script = document.createElement("script");
  script.type = "text/javascript";
  script.src =
    "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=geometry&callback=loadMap";
  document.body.appendChild(script);
};
// 清除对象
const clearn = () => {
  if (timeId) {
    clearInterval(timeId);
    timeId = null;
  }
  innerAudioContext?.pause();
  isPlay.value = false;
};
// 生命周期（dom 加载）
onMounted(window.TMap ? loadMap : loadScript);
// 生命周期（页面卸载）
onUnload(() => {
  map?.destroy();
  clearn();
});
// 生命周期（页面隐藏）
onHide(clearn);
</script>

<style lang="scss" scoped>
.tour {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .contant {
    width: 100%;
    height: 0;
    flex: 1;
    position: relative;
    overflow: hidden;
    #container {
      width: 100%;
      height: 100%;
    }
    .control {
      pointer-events: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .painted,
      .painted * {
        pointer-events: painted;
      }
      .top,
      .bottom .bottom--top {
        display: flex;
        justify-content: space-between;
        margin: 30rpx 20rpx;
      }
      .bottom {
        .bottom--top {
          align-items: flex-end;
          transition: 0.3s;
          > view {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-end;
            gap: 80rpx;
          }
        }
        .bottom_margin {
          margin-bottom: 80rpx;
        }
      }
      .bottom_center {
        position: absolute;
        left: 50%;
        bottom: 40rpx;
        transform: translateX(-50%);
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        background: #349fff;
        display: flex;
        > view {
          margin: auto;
          width: 144rpx;
          height: 144rpx;
          border: 2rpx solid #fff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          > span {
            width: 73rpx;
            font-size: 36rpx;
            font-weight: bold;
            color: #fff;
            line-height: 50rpx;
          }
        }
      }
      .weather {
        display: flex;
        flex-direction: column;
        align-items: center;
        > svg {
          width: 88rpx;
          height: 88rpx;
          background: #fff;
          box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          padding: 18rpx;
          box-sizing: border-box;
        }
        > text {
          margin-top: -17rpx;
          min-width: 120rpx;
          height: 40rpx;
          background: #fff;
          box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0, 0, 0, 0.5);
          border-radius: 20rpx;
          padding: 0 12rpx;
          text-align: center;
          line-height: 40rpx;
          font-size: 22rpx;
          color: #14131f;
          white-space: nowrap;
        }
      }
      .tool {
        width: 90rpx;
        height: 244rpx;
        background: #fff;
        box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
        border-radius: 14rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        .tool--top,
        .tool--bottom {
          width: 100%;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          > image {
            width: 50rpx;
            height: 50rpx;
          }
          > text {
            margin-top: 1rpx;
            font-size: 20rpx;
            color: #14131f;
            line-height: 28rpx;
          }
        }
        .tool--center {
          width: 68rpx;
          height: 1rpx;
          background: #bccbd9;
        }
      }
      .loca {
        width: 80rpx;
        height: 80rpx;
        background: #fff;
        box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
        border-radius: 50%;
        padding: 14rpx;
      }
      .loading {
        animation: loca-loading 2s infinite linear;
        @keyframes loca-loading {
          0% {
            transform: rotate3d(0, 0, 1, 0deg);
          }
          100% {
            transform: rotate3d(0, 0, 1, 360deg);
          }
        }
      }
      .zoom {
        width: 68rpx;
        height: 167rpx;
        background: #fff;
        box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
        border-radius: 14rpx;
        display: flex;
        flex-direction: column;
        > image {
          width: 100%;
          flex: 1;
          padding: 18rpx;
        }
      }
      .menu {
        width: 80rpx;
        height: 80rpx;
        background: #fff;
        box-shadow: 0rpx 2rpx 4rpx 0rpx #dbdbdb;
        border-radius: 50%;
        padding: 14rpx;
      }
      .point_pop {
        margin: 30rpx auto -211rpx;
        position: relative;
        width: 690rpx;
        background: #fff;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        transition: 0.3s;
        .point_pop--top {
          display: flex;
          justify-content: space-between;
          .point_pop--top--left {
            flex: 1;
            width: 0;
            padding: 45rpx 30rpx 35rpx;
            display: flex;
            flex-direction: column;
            .title {
              font-size: 36rpx;
              font-weight: bold;
              color: #090909;
              line-height: 36rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .distance {
              margin-top: 10rpx;
              font-size: 24rpx;
              color: #535353;
              line-height: 33rpx;
            }
            .content {
              height: 111rpx;
              margin-top: 13rpx;
              font-size: 26rpx;
              color: #535353;
              line-height: 37rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
          }
        }
        .point_pop--bottom {
          height: 88rpx;
          border-top: 1rpx solid rgba(151, 151, 151, 0.23);
          display: flex;
          align-items: center;
          > view {
            font-size: 30rpx;
          }
          .point_pop--bottom--left {
            flex: 1;
            height: 100%;
            line-height: 87rpx;
            text-align: center;
            color: #14131f;
          }
          .point_pop--bottom--center {
            width: 1rpx;
            height: 58rpx;
            background-color: rgba(151, 151, 151, 0.23);
          }
          .point_pop--bottom--right {
            flex: 1;
            height: 100%;
            line-height: 87rpx;
            text-align: center;
            color: #349fff;
          }
        }
      }
      .point_show {
        margin: 30rpx auto;
      }
      .line_pop {
        position: relative;
        width: 100%;
        height: 315rpx;
        background: #fff;
        border-radius: 44rpx 44rpx 0rpx 0rpx;
        padding: 40rpx 30rpx;
        .title {
          font-size: 36rpx;
          font-weight: bold;
          color: #090909;
          line-height: 36rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .tips {
          margin-top: 10rpx;
          font-size: 24rpx;
          color: #535353;
          line-height: 33rpx;
          > text:not(:first-child) {
            margin-left: 20rpx;
          }
        }
        .list {
          margin: 18rpx -12rpx;
          display: flex;
          overflow: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          > view {
            margin: 0 12rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            .num {
              width: 80rpx;
              height: 80rpx;
              background: #fff;
              border: 2rpx solid #459bf8;
              border-radius: 50%;
              font-size: 38rpx;
              font-weight: bold;
              color: #459bf8;
              text-align: center;
              line-height: 80rpx;
            }
            .name {
              margin-top: 24rpx;
              width: 104rpx;
              font-size: 26rpx;
              color: #000;
              text-align: center;
              line-height: 37rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .active {
            .num {
              background: #459bf8;
              color: #fff;
            }
          }
        }
      }
      .nav_pop {
        position: relative;
        margin: 30rpx auto;
        width: 556rpx;
        height: 206rpx;
        background: #fff;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        .nav_pop--top {
          flex: 1;
          display: flex;
          padding: 34rpx 45rpx;
          > text {
            font-size: 28rpx;
            color: #14131f;
            line-height: 33rpx;
            margin-right: 30rpx;
          }
          > image {
            margin-right: auto;
            width: 33rpx;
            height: 33rpx;
          }
        }
        .nav_pop--bottom {
          width: 100%;
          height: 80rpx;
          border-top: 1rpx solid rgba(151, 151, 151, 0.23);
          font-size: 30rpx;
          color: #14131f;
          text-align: center;
          line-height: 80rpx;
        }
      }
    }
  }
}
</style>

<style>
/* 天气 svg */
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
