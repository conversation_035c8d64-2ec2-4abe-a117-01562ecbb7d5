<template>
	<view class="my-check-in">
		<y-nav-bar title="我的打卡" />
		<view class="check-in-header">
			<view class="user-info">
				<image class="avatar" :src="imgHost + userInfo.avatar" />
				<text class="name">{{ userInfo.nickname }}</text>
			</view>
			<view class="stats">
				<view class="total-check-ins">
					<text>已累计打卡</text>
					<text class="count">{{ totalCheckIns }}</text>
					<text>次</text>
				</view>
				<view class="location-stats">
					<text>共走过 <text class="highlight">{{ cityCount }}</text> 个城市，<text class="highlight">{{
						locationCount
					}}</text> 个地点</text>
				</view>
			</view>
			<view class="map-button" @click="goToMap">
				<text>打卡地图</text>
				<y-svg class="arrow" name="arrow-right" />
			</view>
		</view>

		<view class="timeline">
			<template v-if="checkInData.length > 0">
				<view v-for="group in checkInData" :key="group.year" class="timeline-group">
					<view class="year-marker">
						<view class="line"></view>
						<text class="year">{{ group.year }} 年</text>
					</view>
					<view class="records">
						<view v-for="(record, index) in group.records" :key="index" class="timeline-item">
							<view class="timeline-line-container">
								<view class="dot">
									<view class="white-circle"></view>
								</view>
								<view class="line"></view>
							</view>
							<view class="item-content">
								<view class="item-header">
									<text class="date">{{ record.date }}</text>
									<text class="day">{{ record.day }}</text>
									<text class="time">{{ record.time }}</text>
									<view class="separator"></view>
									<view :class="['type-tag', record.type === '景区打卡' ? 'scenic-tag' : 'manual-tag']">
										<text class="type-text">{{ record.type }}</text>
									</view>
									<image @click.stop="deleteCheckIn(record)" v-if="record.deletable" class="delete-icon"
										src="@/static/image/trash-can-icon.png" />
								</view>
								<view class="location-card-box">
									<view class="location-card" @click="goToScenic(record)">
										<image v-if="record.location.image" class="location-image" :src="imgHost + record.location.image" />
										<image v-else class="location-image" src="@/static/image/default-img.png" mode="widthFix" />
										<view class="location-info">
											<text class="location-name ellipsis">{{ record.location.name }}</text>
											<text class="location-address">{{ record.location.address }}</text>
										</view>
										<image v-if="record.navigatable" class="arrow-right"
											src="@/static/image/check-in/detail-arrow.webp" />
									</view>
									<view class="location-card-img" v-if="record.msgUrl">
										<image mode="aspectFill" class="location-img" :src="record.msgUrl"
											@click="previewImage(record.msgUrl)" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<view v-if="checkInData.length === 0" class="empty-state">
				<image class="empty-image" src="@/static/image/check-in/checkin-list-default.webp" />
				<text class="empty-text">暂无打卡记录</text>
			</view>
		</view>

		<view class="add-check-in-btn" @click="addCheckIn">
			<image class="add-icon" src="@/static/image/check-in-icon.webp" />
			<text>添加打卡</text>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { imgSrc } from '@/utils/tool.js';
import { getEnv } from "@/utils/getEnv";


const totalCheckIns = ref(0);
const cityCount = ref(0);
const locationCount = ref(0);
const userInfo = ref({
	avatar: '',
	nickname: '游客',
	userId: ''
});
const imgHost = ref(getEnv().VITE_IMG_HOST);

const goToMap = () => {
	Tool.goPage.push('/pages/my/checkInMap');
};

const addCheckIn = () => {
	Tool.goPage.push('/pages/my/addCheckIn');
};

const checkInData = ref([]);

const processCheckInData = (rawData) => {
	if (!rawData || !Array.isArray(rawData) || rawData.length === 0) {
		return [];
	}

	// Sort raw data by createTime descending first
	rawData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

	const groupedByYear = rawData.reduce((acc, record) => {
		const createDate = new Date(record.createTime);
		const year = createDate.getFullYear().toString();

		if (!acc[year]) {
			acc[year] = {
				year: year,
				records: [],
			};
		}

		const month = createDate.getMonth() + 1;
		const dayOfMonth = createDate.getDate();
		const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
		const dayOfWeek = days[createDate.getDay()];
		const hours = createDate.getHours().toString().padStart(2, '0');
		const minutes = createDate.getMinutes().toString().padStart(2, '0');

		acc[year].records.push({
			date: `${month} 月 ${dayOfMonth} 日`,
			day: dayOfWeek,
			time: `${hours}:${minutes}`,
			type: record.scenicId ? '景区打卡' : '手动添加',
			location: {
				name: record.name || record.address || '未知地点',
				address: record.address || '暂无地址信息',
				image: record.scenicMsg,
			},
			deletable: true,
			navigatable: !!record.scenicId,
			scenicId: record.scenicId,
			id: record.id,
			msgUrl: record.msgUrl ? imgSrc(record.msgUrl) : ''
		});
		return acc;
	}, {});

	const sortedYears = Object.keys(groupedByYear).sort((a, b) => parseInt(b) - parseInt(a));

	return sortedYears.map((year) => groupedByYear[year]);
};

const goToScenic = (record) => {
	if (record.navigatable && record.scenicId) {
		Tool.goPage.push(`/pages/scenic/scenic?scenicId=${record.scenicId}`);
	}
};
// 删除打卡
// 预览图片
const previewImage = (url) => {
	uni.previewImage({
		urls: [url],
		current: url
	});
};

const deleteCheckIn = (record) => {
	uni.showModal({
		title: '确定删除记录吗',
		content: '删除后记录将无法恢复',
		confirmText: '确定删除',
		cancelText: '考虑一下',
		success: async (res) => {
			if (res.confirm) {
				try {
					await request.get(`/comment/deleteTClockIn/${record.id}`);
					uni.showToast({
						title: '删除成功',
						icon: 'success',
					});
					// 重新获取数据
					getCheckInInfo();
					getCheckInList();
				} catch (err) {
					console.error('删除打卡失败', err);
				}
			}
		},
	});
};

// 获取用户打卡信息
const getCheckInInfo = async () => {
	try {
		const { data } = await request.get(`/comment/statByScenicId/${userInfo.value.userId}`);
		console.log(data)
		totalCheckIns.value = data.clockNum
		cityCount.value = data.cityNum
		locationCount.value = data.addressNum
	} catch (err) {
		console.error('获取景点信息失败', err);
	}
};
// 获取打卡列表
const getCheckInList = async () => {
	try {
		const userData = await Tool.getUserInfo()
		checkInData.value = [];
		const res = await request.post('/comment/tClockInList', {
			userId: userData.userInfo.userId,
			pageSize: 1000
		})
		const data = res.data.data
		console.log('打卡数据', data)
		if (data && data.length > 0) {
			checkInData.value = processCheckInData(data);
		}
	} catch (error) {
		console.error('获取打卡数据失败', error);
		checkInData.value = [];
	}
}

const onInit = async () => {
	const userData = await Tool.getUserInfo()
	userInfo.value = userData.userInfo
	getCheckInInfo()
	getCheckInList()
}
onShow(onInit)
</script>

<style lang="scss" scoped>
.my-check-in {
	background-color: #fff;
	padding-bottom: 200rpx;
	min-height: 100%;

	.check-in-header {
		position: relative;
		padding: 40rpx;
		background-image: url('@/static/image/check-in/my-check-in-bg.webp');
		background-size: contain;
		background-position: center;
		color: #14131f;

		.user-info {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			.avatar {
				width: 56rpx;
				height: 56rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}

			.name {
				font-size: 32rpx;
				font-weight: 500;
			}
		}

		.stats {
			.total-check-ins {
				font-size: 32rpx;

				.count {
					font-size: 40rpx;
					font-weight: bold;
					color: #349fff;
					margin: 0 8rpx;
					font-weight: 600;
				}
			}

			.location-stats {
				font-size: 28rpx;
				margin-top: 10rpx;

				.highlight {
					color: #349fff;
					font-weight: bold;
					font-weight: 600;
				}
			}
		}

		.map-button {
			position: absolute;
			right: 40rpx;
			top: 200rpx;
			transform: translateY(-50%);
			background-color: #fff;
			border-radius: 30rpx;
			padding: 14rpx 20rpx;
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #349fff;

			.arrow {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}

	.timeline {
		padding: 0 40rpx;
		position: relative;

		.timeline-group {
			.year-marker {
				display: flex;
				align-items: center;
				margin: 30rpx 0;
				position: sticky;
				top: 0;
				background-color: #fff;
				z-index: 10;
				padding: 10rpx 0;

				.line {
					width: 6rpx;
					height: 36rpx;
					background-color: #349fff;
					border-radius: 4rpx;
					margin-right: 12rpx;
				}

				.year {
					font-size: 32rpx;
					font-weight: 500;
					color: #17181a;
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;

		.empty-image {
			width: 460rpx;
			height: 247rpx;
			margin-bottom: 30rpx;
		}

		.empty-text {
			font-size: 28rpx;
			color: #14131F;
		}
	}

	.records {
		position: relative;
		padding-left: 20rpx;
	}

	.timeline-item {
		display: flex;
		margin-bottom: 25rpx;
		position: relative;

		.timeline-line-container {
			position: absolute;
			left: -14rpx;
			top: 12rpx;
			width: 16rpx;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			.dot {
				position: relative;
				width: 16rpx;
				height: 16rpx;
				background-color: #349fff;
				border-radius: 50%;
				flex-shrink: 0;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.white-circle {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 6rpx;
				height: 6rpx;
				background-color: #fff;
				border-radius: 50%;
			}

			.line {
				position: absolute;
				top: 18rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 2rpx;
				height: 100%;
				background: repeating-linear-gradient(to bottom, #d8d8d8, #d8d8d8 4rpx, transparent 4rpx, transparent 8rpx);
			}
		}

		.item-content {
			flex: 1;
			padding-left: 20rpx;
		}

		.item-header {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #17181a;
			margin-bottom: 25rpx;

			.day,
			.time {
				margin-left: 10rpx;
			}

			.separator {
				width: 2rpx;
				height: 30rpx;
				background-color: #d8d8d8;
				margin: 0 16rpx;
			}

			.type-tag {
				padding: 1rpx 16rpx;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				height: 34rpx;

				.type-text {
					font-size: 22rpx;
					font-weight: 500;
					letter-spacing: 0.19rpx;
					white-space: nowrap;
					line-height: 30rpx;
				}
			}

			.manual-tag {
				background: linear-gradient(270deg, #94D9FF 0%, #7491FF 100%, #7491FF 100%);

				.type-text {
					color: #FFFFFF;
				}
			}

			.scenic-tag {
				background: linear-gradient(270deg, #F1B896 0%, #F9DDC7 100%);

				.type-text {
					color: #65300F;
				}
			}

			.delete-icon {
				width: 30rpx;
				height: 30rpx;
				margin-left: auto;
			}
		}

		.location-card-box {
			border: 2rpx solid #eaeaea;
			background-color: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
		}

		.location-card-img {
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 1rpx solid #eaeaea;
			display: flex;
			justify-content: start;

			.location-img {
				width: 214rpx;
				height: 214rpx;
				border-radius: 8rpx;
				object-fit: cover;
				object-position: center;
			}
		}

		.location-card {
			display: flex;
			align-items: center;

			.location-image {
				width: 84rpx;
				height: 84rpx;
				border-radius: 8rpx;
				margin-right: 16rpx;
				flex-shrink: 0;
			}

			.location-info {
				flex: 1;
				overflow: hidden;
				min-width: 0;
				/* 确保 flex 子元素可以正确收缩 */
				width: 0;
				/* 强制宽度为 0，让 flex-grow 决定实际宽度 */

				.location-name {
					font-size: 28rpx;
					font-weight: 500;
					color: #14131f;
					width: 100%;
					max-width: 100%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					display: block;
					box-sizing: border-box;
				}

				.location-address {
					font-size: 24rpx;
					color: #999;
					margin-top: 8rpx;
					white-space: nowrap;
					overflow: hidden;
					width: 100%;
					max-width: 100%;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
					display: block;
					text-overflow: ellipsis;
				}
			}

			.arrow-right {
				width: 32rpx;
				height: 32rpx;
				margin-left: 20rpx;
				flex-shrink: 0;
			}
		}
	}

	.add-check-in-btn {
		position: fixed;
		bottom: 60rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 310rpx;
		height: 92rpx;
		background-color: #fff;
		border-radius: 44rpx;
		box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.08);
		border: 2rpx solid #e1e1e1;
		font-size: 32rpx;
		font-weight: 500;
		color: #17181a;

		.add-icon {
			width: 48rpx;
			height: 48rpx;
			margin-right: 12rpx;
		}
	}
}
</style>
