<template>
	<view>
		<view class="ticket-no-realname">
			<uni-icons :style="{ opacity: QRCodes.length > 1 ? 1 : 0 }" type="left" size="30" color="#000"
				@click="toggleQrcode('left')" />
			<view @click="emits('onQrClick')">
				<y-ticket-qrcode :qr-str="QRCodes[activeQrIndex].printStr" :status="QRCodes[activeQrIndex].ticketStatus"
					:ticket-id="QRCodes[activeQrIndex].ticketNumber" :order-id="QRCodes[activeQrIndex].orderId" />
			</view>
			<uni-icons :style="{ opacity: QRCodes.length > 1 ? 1 : 0 }" type="right" size="30" color="#000"
				@click="toggleQrcode('right')" />
		</view>
		<view v-if="QRCodes.length > 1" style="text-align: center; margin-top: 10rpx">{{ activeQrIndex + 1 }}/{{
			QRCodes.length
		}}</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, computed, onMounted, watch } from "vue"
const props = defineProps({
	QRCodes: {
		type: Array,
		default: () => []
	}
})
const emits = defineEmits(["onQrClick"])
const activeQrIndex = ref(0)
// 切换二维码
const toggleQrcode = (type) => {
	const length = props.QRCodes.length
	if (type === "left") {
		activeQrIndex.value = activeQrIndex.value - 1
		if (activeQrIndex.value < 0) {
			activeQrIndex.value = length - 1
		}
	} else {
		activeQrIndex.value = activeQrIndex.value + 1
		if (activeQrIndex.value >= length) {
			activeQrIndex.value = 0
		}
	}
}

</script>
<style lang="scss" scoped>
.ticket-no-realname {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
