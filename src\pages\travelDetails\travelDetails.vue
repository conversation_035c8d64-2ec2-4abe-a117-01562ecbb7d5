<template>
  <view class="travelDetails">
    <view id="container"> </view>
    <view class="popup" :style="`max-height: calc(100vh - ${maxHeight}px)`">
      <!-- @touchstart="touchStart"
        @touchmove="touchMove"
        @touchend="touchEnd" -->
      <view class="top" @click="setPopup" @touchstart="touchStart" @touchmove="touchMove">
        <view></view>
      </view>
      <view class="scroll" @scroll="scroll">
        <view class="info">
          <view class="info--title">
            <view class="title">{{
              travel.userJourneyDto.journeyName || "行程名称"
            }}</view>
            <image v-if="isEdit" class="edit" src="@/static/image/travel/edit.svg" mode="aspectFit" @click="
              Tool.goPage.push(
                '/pages/travelInfo/travelInfo?journeyId=' +
                travel.userJourneyDto.journeyId
              )
              " />
            <picker class="date" mode="date" @change="bindDateChange" :disabled="!isEdit">{{
              travel.userJourneyDto.journeyStartDate || "出发日期"
            }}</picker>
          </view>
          <view class="info--content" :class="{ more: isMore }" @click="isMore = !isMore">
            <view :style="`height: ${isMore ? Tool.toPx(40) : infoHeight}px;`"></view>
            <view v-if="infoHeight > Tool.toPx(40)" class="btn">{{
              isMore ? "展开" : "收起"
            }}</view>
            <view :style="`margin-top: ${-(isMore
              ? Tool.toPx(40)
              : infoHeight)}px;`">{{ infoContent }}</view>
          </view>
        </view>
        <view v-if="!isEdit && goodsList.length" class="goods">
          <view class="title">商品推荐</view>
          <view class="content">
            <view v-for="(item, index) in goodsList" :key="item.keyName" class="item" @click="goGoods(item)">
              <image class="cover" :src="imgHost + item.pictureUrl.split(',')[0]" mode="aspectFill" />
              <view class="title">{{ item.keyName }}</view>
              <view class="price">¥{{ item.lowestPrice }} 起</view>
              <view class="tips">{{
                suggestType[item.journeyGoodsSuggestType]
              }}</view>
            </view>
          </view>
        </view>
        <view class="tab" :class="{
          border__bottom: isEdit || isScroll,
          border__top: !isEdit && !isScroll,
        }">
          <view v-if="!isEdit" class="tabs">
            <view v-for="(item, index) in ['日行程', '文章推荐']" :class="`item__${index}`" :key="item" @click="setTab(index)">
              {{ item }}</view>
            <view class="line"></view>
          </view>
          <view v-if="tabIndex == 0" class="dates" :class="isEdit ? 'padding__bottom' : 'padding__top padding__bottom'">
            <!-- <view class="left">日行程</view> -->
            <view v-if="travel.userJourneyDto.journeyDateList?.length" class="box">
              <view v-for="(item, dateIndex) in travel.userJourneyDto
                .journeyDateList" :key="item.dateSortNumber" :class="{ active: dateIndex == datesIndex }"
                @click="setDates(dateIndex)">第{{
                  item.dateSortNumber }}天</view>
            </view>
            <view v-if="isEdit" class="right" @click="addDate">+</view>
          </view>
        </view>
        <view v-if="tabIndex == 0" class="list">
          <view v-for="(item, index) in travel.userJourneyDto.journeyDateList" :key="index" class="list--item">
            <view class="list--title">
              <view class="index">第{{ item.dateSortNumber }}天</view>
              <view class="title">{{ item.journeyCity }}</view>
              <image v-if="isEdit" class="del" src="@/static/image/travel/del_blue.svg" mode="aspectFit"
                @click="del(index)" />
              <!-- <uni-icons class="icon" type="top" color="#000" size="40rpx" /> -->
            </view>
            <view class="list--content">
              <draggable group="list" :list="item.journeyPlaceList" :forceFallback="true" :animation="200"
                handle=".handle" ghost-class="ghost" drag-class="drag" @end="onEnd">
                <template #item="{ element: placeItem, index: placeIndex }">
                  <view class="list--content--box">
                    <view v-if="placeIndex" class="list--content--tips" @click="nav(placeItem)">
                      {{ placeItem.distance && getDistance(placeItem.distance) }} 导航 >
                    </view>
                    <view class="list--content--item">
                      <view class="title">{{ placeItem.journeyPlaceName }}</view>
                      <view v-if="placeItem.placeTypeDescription" class="tag">{{
                        placeItem.placeTypeDescription
                      }}</view>
                      <template v-if="isEdit">
                        <uni-icons class="handle" style="margin-left: auto;" type="bars" size="40rpx"
                          color="#b8bed7"></uni-icons>
                        <image class="sub" src="@/static/image/travel/sub.svg" mode="aspectFit"
                          @click="delPlace(index, placeIndex)" />
                      </template>
                    </view>
                  </view>
                </template>
              </draggable>
            </view>
          </view>
          <view v-if="!travel.userJourneyDto.journeyDateList?.length" class="list--blank">
            <view class="content">还未添加地点或日期哦</view>
            <view class="tips">请设置出发日期或点击下方添加地点～</view>
          </view>
        </view>
        <!-- 列表 -->
        <view v-if="tabIndex == 1" class="list__article">
          <view v-for="item in articleList" :key="item.articleTitle" class="list--item__article"
            @click="goArticle(item)">
            <view class="article">
              <view class="title">{{ item.articleTitle }}</view>
              <view class="sub">
                <view class="time">{{ item.releaseDate }}</view>
                <image class="eye" src="@/static/image/message/eye.svg" mode="scaleToFill" />
                <view class="count">{{
                  item && item.readCount >= 100
                    ? (item.readCount / 1000).toFixed(1) + "k"
                    : item.readCount
                }}</view>
              </view>
              <view class="userBox">
                <image class="user" src="@/static/image/message/user.svg" mode="scaleToFill" />
                <view class="author">{{ item.author }}</view>
              </view>
            </view>
            <image class="img" :src="imgHost + (item.pictureUrl || '').split(',')[0] || '-'" @click=""
              mode="aspectFill" />
          </view>
        </view>
      </view>
      <view class="button">
        <view v-if="!isEdit" class="left" @click="(isEdit = true), (tabIndex = 0)">编辑行程</view>
        <view v-if="isEdit || (topic_id && !travel.userJourneyDto.journeyId)" class="right"
          @click="setData({ dateSortNumber: false, update: true })">{{ topic_id && !isEdit ? "加入行程" : "保存" }}</view>
        <image v-if="isEdit" class="addButton" src="@/static/image/travel/add.svg" mode="aspectFit"
          @click="addAddress" />
      </view>
    </view>
  </view>
</template>
<script setup>
import { useTravelStore } from "@/stores/travel";
import { suggestType } from "@/utils/constant.js";
import request from "@/utils/request.js";
import { setJWeixin } from "@/utils/tool.js";
import draggable from "vuedraggable";
import { getEnv } from "@/utils/getEnv";

setJWeixin()
const { journeyId, topic_id, answer_round, edit, storeId } =
  Tool.getRoute.params();
const travel = useTravelStore();
const isEdit = ref(edit);
const showMore = ref(false); // 是否显示展开
const isMore = ref(false); // 展开/收起
const isPopup = ref(false);
const maxHeight = ref(Tool.toPx(460));
const infoHeight = ref(0);
const isScroll = ref(false);
const goodsList = ref([]);
const tabIndex = ref(0);
const datesIndex = ref(0);
const articleList = ref([]);
const imgHost = ref(getEnv().VITE_IMG_HOST);
const list = ref([{ id: 1, name: '贵州' }, { id: 2, name: '昆明' }, { id: 3, name: '北流' }, { id: 4, name: '毕节' }]);
let map; // 地图实例
let polyLine; // 路线实例
let markerLayer; // 点位实例
let offset = 0;
let start = 0;
let dom = null;

//跳转第三方导航
const nav = (v) => {
  jWeixin.openLocation({
    latitude: Number(v.latitude),
    longitude: Number(v.longitude),
    name: v.journeyPlaceName,
    address: v.journeyCityName
  })
}
const touchStart = (e) => {
  offset = e.touches[0].clientY - maxHeight.value;
};
const touchMove = (e) => {
  const v = e.touches[0].clientY - offset
  if (v >= Tool.toPx(120) && v <= Tool.toPx(460)) {
    maxHeight.value = e.touches[0].clientY - offset;
  }
};
const onEnd = () => {
  setData({ placeSortNumber: true })
};
const scroll = () => {
  const s = document.querySelector(".scroll").getBoundingClientRect();
  const t = document.querySelector(".tab").getBoundingClientRect();
  isScroll.value = s.top >= t.top - 1;
};
const setPopup = () => {
  isPopup.value = !isPopup.value;
  maxHeight.value = isPopup.value ? Tool.toPx(120) : Tool.toPx(460);
};
const setTab = (index) => {
  tabIndex.value = index;
  nextTick(() => {
    const { offsetLeft, offsetWidth } = document.querySelector(
      `.item__${index}`
    );
    document.querySelector(".line").style.transform = `translateX(${offsetLeft + offsetWidth / 2
      }px)`;
  });
};
const setDates = (dateIndex) => {
  document.querySelectorAll('.list--title')[dateIndex].scrollIntoView({
    behavior: 'smooth',
    block: 'center'
  })
  datesIndex.value = dateIndex;
  setMap();
};
const setData = async ({
  dateSortNumber = true, // 更新日行程
  placeSortNumber = false, // 更新点位（导航）
  update = false, // 更新远端数据
}) => {
  const arr = travel.userJourneyDto.journeyDateList;
  if (dateSortNumber)
    for (const key in arr) {
      arr[key].dateSortNumber = +key + 1;
      if (placeSortNumber)
        for (const key2 in arr[key].journeyPlaceList) {
          arr[key].journeyPlaceList[key2].placeSortNumber = +key2 + 1;
          if (+key2) {
            try {
              const { longitude: a, latitude: b } =
                arr[key].journeyPlaceList[+key2 - 1];
              const { longitude: c, latitude: d } =
                arr[key].journeyPlaceList[+key2];
              const O = {};
              const D = TMap.geometry.computeDistance([
                new TMap.LatLng(b, a),
                new TMap.LatLng(d, c),
              ]);
              if (D < 2000) {
                O.type = "walking";
                O.label = "步行";
              } else {
                O.type = "driving";
                O.label = "驾车";
              }
              await request
                .post("/navigation/line/common", {
                  domain: "https://apis.map.qq.com",
                  interfacePath: "/ws/direction/v1/" + O.type,
                  parameter: `from=${b},${a}&to=${d},${c}`,
                })
                .then((res) => {
                  const { distance, duration, polyline } = parse(res.data)
                    .result.routes[0];
                  arr[key].journeyPlaceList[key2].distance = JSON.stringify({
                    O,
                    distance,
                    duration,
                    polyline,
                  });
                });
            } catch (error) { }
          } else {
            arr[key].journeyPlaceList[key2].distance = "";
          }
        }
    }
  if (update) {
    request
      .post("/user/journey/update", travel.userJourneyDto)
      .then(({ data }) => {
        travel.userJourneyDto = data;
        isEdit.value = false;
        setTab(tabIndex.value);
        uni.showToast({
          icon: "none",
          title: "已更新",
        });
      });
  }
  if (placeSortNumber) setMap();
};
const parse = (v) => {
  let obj = {};
  try {
    obj = JSON.parse(v);
  } catch (error) { }
  return obj;
};
const getDistance = (v) => {
  const {
    O = {
      type: "walking",
      label: "步行",
    },
    distance,
    duration,
  } = parse(v);
  return `${+distance < 1000
    ? distance + "米"
    : Math.round(distance / 100) / 10 + "公里"
    } ${O.label +
    (+duration < 60
      ? duration + "分钟"
      : Math.round(duration / 60) + "小时" + (duration % 60) + "分钟")
    }`;
};
const bindDateChange = (e) => {
  travel.userJourneyDto.journeyStartDate = e.detail.value;
};
const addDate = () => {
  if (!travel.userJourneyDto.journeyDateList) {
    travel.userJourneyDto.journeyDateList = [];
  }
  travel.userJourneyDto.journeyDateList.push({
    dateSortNumber: travel.userJourneyDto.journeyDateList.length + 1,
  });
};
const del = (index) => {
  uni.showModal({
    content: `删除【第${index + 1}天】行程？`,
    success: function (res) {
      if (res.confirm) {
        travel.userJourneyDto.journeyDateList.splice(index, 1);
        setData({});
      }
    },
  });
};
const delPlace = (index, placeIndex) => {
  const arr = travel.userJourneyDto.journeyDateList[index].journeyPlaceList;
  uni.showModal({
    content: `删除【${arr[placeIndex].journeyPlaceName}】？`,
    success: function (res) {
      if (res.confirm) {
        arr.splice(placeIndex, 1);
        setData({ placeSortNumber: true });
      }
    },
  });
};
const addAddress = () => {
  if (!travel.userJourneyDto.journeyDateList?.length) addDate();
  Tool.goPage.push(
    "/pages/travelAddress/travelAddress?journeyId=" +
    travel.userJourneyDto.journeyId
  );
};
const getGoodsArticle = (type) => {
  const journeyPlaceNames = [];
  travel.userJourneyDto.journeyDateList?.map(({ journeyPlaceList }) => {
    journeyPlaceList.map((item) => {
      journeyPlaceNames.push(item.journeyPlaceName);
    });
  });
  request
    .get(`/user/journey/${type}/suggest`, {
      journeyPlaceNames,
      storeId,
    })
    .then(({ data }) => {
      if (type == "goods") goodsList.value = data;
      if (type == "article") articleList.value = data;
    });
};
const goGoods = (item) => {
  const { journeyGoodsSuggestType } = item;
  if (journeyGoodsSuggestType === "COMBO_TICKET") {
    Tool.goPage.push(`/pages/scenic/scenic?storeGoodsId=${item.keyId}`);
  }
  if (journeyGoodsSuggestType === "SCENIC") {
    Tool.goPage.push(`/pages/scenic/scenic?scenicId=${item.keyId}`);
  }
  if (journeyGoodsSuggestType === "TRAVEL_CARD") {
    Tool.goPage.push(
      `/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.keyId}`
    );
  }
};
const goArticle = (item) => {
  const url = item.externalUrl.trim()
  if (url) {
    window.location.href = url.includes("https")
      ? url.trim()
      : `https://${url.trim()}`;
  } else {
    Tool.goPage.push(`/pages/articleDetail/articleDetail?id=${item.articleId}`);
  }
};
const setMap = () => {
  const placeList =
    travel?.userJourneyDto?.journeyDateList?.[datesIndex.value]
      ?.journeyPlaceList || [];
  const geometries = [];
  const markerGeometries = [];
  const paths = [];
  placeList.forEach(
    (
      { distance, latitude, longitude, journeyPlaceName, externalId },
      index
    ) => {
      if (distance) {
        // 坐标解压
        const {
          O = {
            type: "walking",
            label: "步行",
          },
          polyline: coors = [],
        } = parse(distance);
        let pl = [];
        let kr = 1000000;
        for (let i = 2; i < coors.length; i++) {
          coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
        }
        for (let i = 0; i < coors.length; i += 2) {
          pl.push(new TMap.LatLng(coors[i], coors[i + 1]));
        }
        geometries.push({
          id: `polyLine_${datesIndex.value}_${index}`,
          styleId: O.type,
          paths: pl,
        });
      }
      const position = new TMap.LatLng(latitude, longitude);
      markerGeometries.push({
        id: externalId,
        position,
        content: journeyPlaceName,
      });
      paths.push(position);
    }
  );
  // 渲染路线
  polyLine.setGeometries(geometries);
  // 渲染点位
  markerLayer.setGeometries(markerGeometries);
  // 设置地图可视范围
  if (paths.length) {
    const bounds = new TMap.LatLngBounds();
    paths.forEach((position) => bounds.extend(position));
    map.fitBounds(bounds, {
      padding: {
        top: 50,
        bottom: innerHeight - (46 / 75) * innerWidth + 50,
        left: 50,
        right: 50,
      },
      maxZoom: 17,
    });
  }
};
const initData = async () => {
  travel.userJourneyDto = {};
  if (journeyId) {
    await request
      .get("/user/journey/detail", { journeyId })
      .then(({ data }) => {
        travel.userJourneyDto = data;
        setMap();
      });
  } else if (topic_id) {
    const {
      userInfo: { userId },
    } = await Tool.getUserInfo();
    await reqAi
      .get(`/scenic_route_planning/history_ner/${userId}`, {
        topic_id,
        answer_round,
      })
      .then(({ data }) => {
        const journeyDateList = Object.keys(data.format_route).map(
          (item, index) => ({
            dateSortNumber: index + 1,
            journeyCity: data.route_city,
            journeyPlaceList: data.format_route[item].map((item2, index2) => {
              const [longitude, latitude] =
                data.pio_route[item][index2].split(",");
              return {
                journeyCityName: data.route_city,
                journeyPlaceName: item2.name,
                placeTypeDescription: item2.type.split(";")[0],
                latitude,
                longitude,
                placeSortNumber: index2 + 1,
              };
            }),
          })
        );
        travel.userJourneyDto = {
          aiLinkId: data.route_id,
          journeyName: data.route_name,
          journeyRemark: data.route_remark,
          journeyDateList,
        };
        setData({ placeSortNumber: true });
      });
  }
  getGoodsArticle("goods");
  getGoodsArticle("article");
};
// 初始化地图（执行同步任务）
const initMap = async () => {
  // 创建地图
  map = new TMap.Map("container", {
    // center, // 中心点坐标
    viewMode: "2D", // 视图模式
    showControl: false, // 控件
    baseMap: {
      type: "vector", // {vector: '矢量图', satellite: '卫星图'}
      buildingRange: [14.5, 20], // 设置建筑物楼块的显示级别
    },
  });
  // 创建路线
  polyLine = new TMap.MultiPolyline({
    map: map,
    styles: {
      default: new TMap.PolylineStyle({
        width: 8,
        borderWidth: 2,
        color: "#21b977",
        borderColor: "#fff",
        lineCap: "round",
        showArrow: true,
        arrowOptions: {
          height: 6,
        },
      }),
      walking: new TMap.PolylineStyle({
        width: 8,
        borderWidth: 2,
        color: "#3777FF",
        borderColor: "#fff",
        lineCap: "round",
        showArrow: true,
        arrowOptions: {
          height: 6,
        },
      }),
    },
  });
  // 创建点位
  markerLayer = new TMap.MultiMarker({
    map: map,
    styles: {
      default: new TMap.MarkerStyle({
        // 点标注的相关样式
        // width: 34, // 宽度
        // height: 46, // 高度
        // anchor: { x: 17, y: 23 }, // 标注点图片的锚点位置
        color: "#333", // 标注点文本颜色
        size: 16, // 标注点文本文字大小
        direction: "bottom", // 标注点文本文字相对于标注点图片的方位
        offset: { x: 0, y: 8 }, // 标注点文本文字基于direction方位的偏移属性
        strokeColor: "#fff", // 标注点文本描边颜色
        strokeWidth: 2, // 标注点文本描边宽度
      }),
    },
  });
  initData();
};
// 加载地图 API 完成
const loadMap = () => {
  if (!isEdit.value) setTab(0);
  initMap();
};
// 加载地图 API
const loadScript = () => {
  window.loadMap = loadMap;
  let script = document.createElement("script");
  script.type = "text/javascript";
  script.src =
    "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=geometry&callback=loadMap";
  document.body.appendChild(script);
};

// 计算属性
const infoContent = computed(() => {
  const places = travel.userJourneyDto.journeyPlaces
    ? `${travel.userJourneyDto.journeyDays}天 | ${travel.userJourneyDto.journeyPlaces}个地点`
    : "";
  return `${places || "暂无地点"} | ${travel.userJourneyDto.journeyRemark || "写点什么，记录这次旅行～"
    }`;
});
watch(infoContent, () => {
  isMore.value = false;
  nextTick(() => {
    setTimeout(() => {
      const h = document.querySelector(".info--content").offsetHeight;
      infoHeight.value = h - Tool.toPx(40);
      if (h > Tool.toPx(80)) isMore.value = true;
    });
  });
});

// 生命周期（dom加载）
onMounted(window.TMap ? loadMap : loadScript);
onUnload(() => {
  map?.destroy();
});
onShow(() => {
  if (travel.update) {
    travel.update = false;
    setMap();
  }
});
</script>
<style lang="scss" scoped>
.travelDetails {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;

  #container {
    width: 100%;
    height: 100%;
  }

  .popup {
    position: absolute;
    bottom: 0;
    width: 100%;
    min-height: calc(100vh - 460rpx);
    background: #fff;
    border-radius: 16rpx 16rpx 0rpx 0rpx;
    display: flex;
    flex-direction: column;
    overflow: auto;
    transition: 0.2s;

    >view {
      flex-shrink: 0;
    }

    .top {
      height: 40rpx;

      >view {
        margin: 18rpx auto;
        width: 76rpx;
        height: 8rpx;
        background: #c8c8c8;
        border-radius: 4rpx;
      }
    }

    .scroll {
      flex: 1;
      overflow: auto;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .info {
      padding: 15rpx 30rpx;

      .info--title {
        display: flex;
        align-items: center;

        .title {
          font-weight: bold;
          font-size: 34rpx;
          color: #14131f;
        }

        .edit {
          margin: 0 18rpx;
          width: 50rpx;
          height: 50rpx;
          flex-shrink: 0;
        }

        .date {
          margin-left: auto;
          font-size: 26rpx;
          color: #349fff;
          white-space: nowrap;
        }
      }

      .info--content {
        margin-top: 20rpx;
        font-size: 26rpx;
        color: rgba(20, 19, 31, 0.64);
        line-height: 40rpx;
        word-break: break-all;

        .btn {
          float: right;
          color: #14131f;
        }
      }

      .more {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .goods {
      padding: 15rpx 0 15rpx 30rpx;

      .title {
        font-weight: bold;
        font-size: 34rpx;
        color: #14131f;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .content {
        margin: -5rpx 0 -30rpx -30rpx;
        display: flex;
        gap: 20rpx;
        padding: 30rpx;
        overflow: auto;

        .item {
          flex-shrink: 0;
          width: 274rpx;
          position: relative;
          display: flex;
          flex-direction: column;
          border-radius: 16rpx 16rpx 0rpx 0rpx;
          font-weight: bold;
          border-radius: 8rpx 16rpx 16rpx;
          box-shadow: 0 2px 4px 0 rgba($color: #b1b1b1, $alpha: 0.5);
          overflow: hidden;

          .cover {
            width: 100%;
            height: 134rpx;
            background: #ccc;
          }

          .title {
            font-size: 30rpx;
            color: #14131f;
            padding: 20rpx 20rpx 0;
          }

          .price {
            font-size: 26rpx;
            color: #f43636;
            padding: 10rpx 20rpx;
          }

          .tips {
            position: absolute;
            top: 0;
            left: 0;
            width: 81rpx;
            height: 32rpx;
            background: linear-gradient(270deg, #f1b896 0%, #f9ddc7 100%);
            font-size: 22rpx;
            color: #65300f;
            text-align: center;
            line-height: 32rpx;
            border-radius: 6rpx;
          }
        }
      }
    }

    .tab {
      margin-top: 15rpx;
      position: sticky;
      top: -1px;
      z-index: 1;
      background: #fff;

      .tabs {
        padding: 0 30rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        gap: 60rpx;
        position: relative;

        .item {
          font-size: 30rpx;
          color: rgba(20, 19, 31, 0.68);
        }

        .line {
          content: "";
          position: absolute;
          bottom: 0;
          left: -36rpx;
          width: 72rpx;
          height: 10rpx;
          background: #459bf8;
          border-radius: 5rpx;
          transition: 0.2s;
        }
      }

      .dates {
        padding: 0 30rpx;
        display: flex;
        gap: 20rpx;

        .left,
        .right,
        .box>view {
          width: 130rpx;
          height: 60rpx;
          background: rgba(52, 159, 255, 0.1);
          border-radius: 8rpx;
          font-size: 28rpx;
          color: #349fff;
          text-align: center;
          line-height: 60rpx;
          flex-shrink: 0;
        }

        .right {
          font-size: 40rpx;
          font-weight: bold;
        }

        .box {
          display: flex;
          gap: 20rpx;
          overflow: auto;

          .active {
            background: #349fff;
            color: #fff;
          }
        }
      }

      .padding__bottom {
        padding-bottom: 20rpx;
      }

      .padding__top {
        padding-top: 20rpx;
      }
    }

    .shadow {
      border-bottom: 1rpx solid rgba(151, 151, 151, 0.3);
    }

    .border__top {
      border-top: 1rpx solid rgba(151, 151, 151, 0.3);
    }

    .border__bottom {
      border-bottom: 1rpx solid rgba(151, 151, 151, 0.3);
    }

    .list {
      flex: 1;
      padding: 30rpx 30rpx 134rpx;
      display: flex;
      flex-direction: column;

      .list--title {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        font-weight: bold;
        color: #14131f;
        display: flex;
        white-space: nowrap;

        .title {
          margin-left: 26rpx;
          overflow: auto;
        }

        .del {
          margin: 0 18rpx;
          width: 32rpx;
          height: 34rpx;
        }

        .icon {
          margin-left: auto;
        }
      }

      .list--content {
        margin-left: 16rpx;
        padding-left: 32rpx;
        position: relative;

        .ghost {
          opacity: .5;
        }

        .drag {
          opacity: 1 !important;

          .list--content--tips,
          .list--content--item::before {
            display: none;
          }
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 42rpx;
          border-left: 2rpx dashed #d4d4d4;
          width: 2rpx;
          height: calc(100% - 84rpx);
        }

        .list--content--box {
          position: relative;
          display: flex;
          flex-direction: column;
        }

        .list--content--tips {
          height: 84rpx;
          line-height: 84rpx;
          font-size: 24rpx;
          color: rgba(20, 19, 31, 0.5);
          position: absolute;
          top: -42rpx;

          &::before {
            content: "";
            position: absolute;
            top: 33rpx;
            left: -42rpx;
            width: 18rpx;
            height: 18rpx;
            background: #ffffff;
            border: 3rpx solid #349fff;
            box-sizing: border-box;
            border-radius: 50%;
          }
        }

        .list--content--item {
          margin: 42rpx 0;
          width: 100%;
          height: 88rpx;
          background: #f5f6fa;
          border-radius: 9rpx;
          display: flex;
          align-items: center;
          padding: 0 16rpx 0 30rpx;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: -53rpx;
            width: 40rpx;
            height: 40rpx;
            background: #fff url("@/static/image/travel/pic.svg") no-repeat center/contain;
          }

          .title,
          .tag {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .title {
            font-weight: bold;
            font-size: 28rpx;
            color: #14131f;
          }

          .tag {
            margin: 0 20rpx;
            min-width: 81rpx;
            height: 32rpx;
            background: linear-gradient(270deg, #f1b896 0%, #f9ddc7 100%);
            border-radius: 3rpx;
            font-weight: bold;
            font-size: 22rpx;
            color: #65300f;
            text-align: center;
            // line-height: 32rpx;
            padding: 0 18rpx;
          }

          .sub {
            margin-left: 20rpx;
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      .list--blank {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .content {
          font-weight: bold;
          font-size: 34rpx;
          color: #050505;
        }

        .tips {
          margin-top: 10rpx;
          font-size: 26rpx;
          color: rgba(20, 19, 31, 0.5);
        }
      }
    }

    .list__article {
      padding: 10rpx 30rpx;

      .list--item__article {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 0;
        width: 100%;

        .article {
          width: 396rpx;
          display: flex;
          flex-direction: column;

          .title {
            font-size: 30rpx;
            font-weight: bold;
            color: #090909;
            line-height: 42rpx;
            width: 396rpx;
            max-height: 84rpx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .sub {
            display: flex;
            align-items: center;
            margin-top: auto;
            color: rgba(20, 19, 31, 0.5);
            font-size: 24rpx;

            .eye {
              width: 32rpx;
              height: 32rpx;
              margin-left: 45rpx;
              margin-right: 2rpx;
            }
          }

          .userBox {
            margin-top: 20rpx;
            display: flex;
            align-items: center;

            .user {
              height: 36rpx;
              width: 36rpx;
              margin-right: 8rpx;
            }

            .author {
              height: 32rpx;
              font-size: 23rpx;
              font-weight: 400;
              color: #14131f;
              line-height: 32rpx;
            }
          }
        }

        .img {
          border-radius: 8rpx;
          margin: 12rpx 0;
          width: 272rpx;
          height: 153rpx;
        }
      }

      .list--item__article:not(:last-child) {
        border-bottom: 2rpx solid rgba(191, 198, 209, 0.25);
      }
    }

    .button {
      width: 100%;
      display: flex;
      padding: 15rpx;
      border-top: 1px solid rgba(151, 151, 151, 0.3);

      >view {
        margin: 15rpx;
        flex: 1;
        height: 88rpx;
        border-radius: 12rpx;
        text-align: center;
        line-height: 88rpx;
        font-weight: bold;
        font-size: 36rpx;
      }

      .left {
        background: rgba(52, 159, 255, 0.17);
        color: #349fff;
      }

      .right {
        background: rgba(52, 159, 255, 1);
        color: #fff;
      }

      .addButton {
        position: absolute;
        right: 19rpx;
        bottom: 172rpx;
        width: 80rpx;
        height: 80rpx;
      }
    }
  }
}
</style>
