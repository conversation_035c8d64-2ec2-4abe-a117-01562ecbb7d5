<template>
	<y-nav-bar backgroundColor="#349FFF" solid>{{ title }}</y-nav-bar>
	<view class="result-page">
		<image
			class="result-icon"
			src="@/static/image/my/set-suceess-icon.png"
			mode="widthFix"></image>
		<view class="status">设置成功</view>
		<view class="tip">
			<text>{{ countDownTime }}</text> 秒自动返回，
			<text @click="goRedirect">立即返回</text>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted, computed } from "vue"
import { logOut, getRoute, wxAuthorize } from "@/utils/tool.js"
import request from "@/utils/request.js"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"
import { onShow } from "@dcloudio/uni-app"

const goRedirect = () => {
	uni.redirectTo({
		url: getRoute.params().redirect
	})
}
const title = ref("设置成功")
//倒计时
const countDownTime = ref(3)
onMounted(async () => {
	title.value = getRoute.params().title
	const countDown = () => {
		const timer = setTimeout(() => {
			if (countDownTime.value === 0) {
				goRedirect()
			} else {
				countDownTime.value = countDownTime.value - 1
				countDown()
			}
		}, 1000)
	}
	countDown()
})
</script>
<style lang="scss" scoped>
.result-page {
	background-color: #fff;
	min-height: 100vh;
	text-align: center;
	.result-icon {
		margin-top: 245rpx;
		margin-bottom: 12rpx;
		width: 238rpx;
		height: 238rpx;
	}
	.status {
		margin-bottom: 20rpx;
		font-size: 48rpx;
		font-weight: 600;
		color: #060131;
		line-height: 48rpx;
	}
	.tip {
		font-size: 26rpx;
		font-weight: 400;
		color: #999999;
		line-height: 26rpx;
		text {
			color: #3c93ff;
		}
	}
}
</style>
