<template>
	<y-nav-bar backBtn solid>{{ order.pageTitle }}</y-nav-bar>
	<view class="order-detail">
		<view class="order-status">
			<view class="status">
				<template v-if="payLoading">
					<y-loading-text :text="payLoading" />
				</template>
				<template v-else>
					{{
						orderStatus[
						routerParams.refundId
							? order.refundOrderList[0].refundStatus
							: order.orderStatus
						]
					}}
				</template>

				<text v-if="mainOrder.orderStatus == 20" class="price"><text class="util">¥</text>{{ mainOrder.payAmount
				}}</text>
				<view v-if="!routerParams.refundId && orderRefundList.length > 0" class="order-status-refund"
					@click="refundOrderVisible = true">
					<view class="num">退单 {{ orderRefundList.length }} 次</view>
					<uni-icons type="forward" color="#14131F" size="15"></uni-icons>
				</view>
			</view>
			<view v-if="showService">请
				<text class="service-btn" @click="goService">联系客服</text>
				进行处理
			</view>
			<text v-if="mainOrder.orderStatus == 20 && !payLoading" class="count-down">
				剩余
				<text style="color: #f43636">{{
					`00:${payCountDown.minute}:${payCountDown.second}`
				}}</text>
				订单自动取消
			</text>
		</view>
		<view class="order-info panel-box">
			<view class="title"> 订单信息 </view>
			<view v-if="routerParams.refundId" class="order__item">
				<view class="order__item-title">退单号</view>
				<view>>{{ routerParams.refundId }}</view>
			</view>
			<view class="order__item" @click="goOrderDetail">
				<view class="order__item-title">订单号</view>
				<view>{{ mainOrder.orderGroupId }}
					<uni-icons v-if="routerParams.refundStatus" style="margin-left: auto" type="forward" color="#fff"
						size="20"></uni-icons>
				</view>
				<y-svg style="width: 30rpx; height: 30rpx" name="copy-icon" @click="copyText(mainOrder.orderGroupId)" />
			</view>
			<view class="order__item">
				<view class="order__item-title">订单金额</view>
				<view style="color: #f43636">¥{{ mainOrder.payAmount }}</view>
			</view>
			<view v-if="order.refundDetail.refundId" class="order__item">
				<view class="order__item-title">退款金额</view>
				<view>¥{{ order.refundDetail.refundAmount }}</view>
				<view @click="popup.open()" style="margin-left: auto; color: var(--theme-color)">详情</view>
			</view>
			<view class="order__item">
				<view class="order__item-title">创建时间</view>
				<view>{{ order.createTime }}</view>
			</view>
			<view v-if="routerParams.refundStatus == 51" class="order__item">
				<view class="order__item-title">到账时间</view>
				<view>{{ orderRefundList[0].refundTime }}</view>
			</view>
			<template v-if="order.needIdentity">
				<view style="height: 30rpx"></view>
				<view class="ticket-tip">
					<view class="left"> 需要携带身份证入园 </view>
					<view v-if="order.certId" class="right" @tap="
						Tool.goPage.push(
							`/pages/blockchain/blockchainTransactions?certId=${order.certId}`
						)
						">
						查看区块链交易证书
					</view>
				</view>
			</template>
		</view>
		<!-- 出库失败图片 -->
		<view v-if="[32, 33].includes(order.orderStatus)" class="ticket-content">
			<image src="@/static/image/icon-noData.png"></image>
		</view>
		<view v-else>
			<!-- 套票信息 -->
			<l-compose-ticket v-if="order.isCompose" :order="order" />
			<!-- 单票信息 -->
			<l-single-ticket v-else :order="order" :refundTicketList="refundTicketList" @onCheck="setRefundTicket"
				:showRefund="order.showRefund" :isRefundOrder="routerParams.refundStatus ? true : false" />
			<!-- 取票人 -->
			<template v-if="JSON.stringify(order.getTicketMan) !== '{}'">
				<y-collect-ticket-man v-model="order.getTicketMan" />
			</template>
		</view>
		<!-- 发行信息 -->
		<view v-if="mainOrder.orderPublishVO" class="order-info panel-box">
			<view class="title"> 发行信息 </view>
			<view class="order__item">
				<view class="order__item-title">发行方</view>
				<view>{{ mainOrder.orderPublishVO?.scenicName }}</view>
			</view>
			<view class="order__item">
				<view class="order__item-title">收藏者</view>
				<view>{{ mainOrder.orderPublishVO?.collectName }}</view>
			</view>
			<view class="order__item">
				<view class="order__item-title">存证证书</view>
				<view style="color: #1890ff" @click="goBlockchainCertificate(mainOrder.orderPublishVO?.txId)">
					{{ Tool.hideMiddleChars(mainOrder.orderPublishVO?.txId || '') }}
				</view>
			</view>
			<!-- <view class="order__item">
				<view class="order__item-title">智旅链查询</view>
				<view style="color: #1890ff" @click="
						copyText(
							`${chainUrl}/#/tx_list?tx=${mainOrder.orderPublishVO?.txId}`
						)
					">
					{{ chainUrl }}
				</view>
			</view> -->
		</view>
		<!-- 底部操作栏 -->
		<OrderDetailFooter v-if="!payLoading" :order="order" ref="footer" :isCheckAll="isCheckAll"
			:refundTicketList="refundTicketList" @onCheck="setRefundTicket" @reload="init" />
		<y-popup v-model="showBookContent" type="reserve" title="预订须知">
			<view v-html="bookContent" class="rich-content"></view>
		</y-popup>
		<y-popup v-model="refundOrderVisible">
			<l-order-item v-for="(n, i) in order.refundOrderList" :key="i" :order="n" :orderId="order.orderGroupId" />
		</y-popup>
		<uni-popup ref="popup" type="bottom">
			<view class="chain-record-pop">
				<view class="pop-title">
					<uni-icons type="left" size="30rpx" @click="popup.close()"></uni-icons>
					<view class="title">退单详情</view>
				</view>
				<view style="padding-top: 45rpx; display: flex; align-items: center"><text
						style="color: #6e6e6e; font-size: 28rpx; margin-right: 26rpx">退单号</text>{{
							order.refundDetail.refundId }}
					<y-svg style="width: 30rpx; height: 30rpx" name="copy-icon" @click="copyText(order.refundDetail.refundId)" />
				</view>
				<view class="pop-content">
					<view class="tips"></view>

					<view class="content">
						<view v-for="(item, index) in order.refundDetail.refundFlow" :key="index" class="item">
							<view class="info-time">
								<view class="info-time-dot">
									<view></view>
								</view>
								<view class="info-title">{{ item.title }}</view>
							</view>
							<view v-if="item.content" class="info-type">{{
								item.content
							}}</view>
							<view v-if="item.type === 'refundFailure'" class="info-type">请
								<text @click="Tool.goPage.push('/pages/my/contactUs')" class="text-btn">
									联系客服 </text>进行处理
							</view>
							<view v-if="item.time" class="info-date">{{ item.time }}</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script setup>
import { orderStatus, ticketType } from "@/utils/constant.js"
import request from "@/utils/request.js"
import {
	copyText,
	markdownToHtml,
	objToUrlPath
} from "@/utils/tool.js"
import { onBackPress, onLoad, onShow } from "@dcloudio/uni-app"
import dayjs from "dayjs"
import {
	computed,
	onUnmounted,
	reactive,
	ref,
	watch
} from "vue"
import OrderDetailFooter from "./component/orderDetailFooter.vue"
import { getEnv } from "@/utils/getEnv";

const chainUrl = getEnv().VITE_CHAIN_URL
const props = defineProps({})
const routerParams = reactive({})
const payLoading = ref("")
const popup = ref(null)
let timer = null
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
})
onUnmounted(() => {
	timer = null
})

const popupList = ref([
	{
		triggerAt: "退款成功",
		optType: 1,
		minerName: "测试",
		receiverName: "测试",
		ticketsStatus: 1
	}
])

const mainOrder = reactive({})
const childrenOrder = reactive({})
const orderRefundList = reactive([])

// 展示联系客服
const showService = computed(() => [32, 33].includes(mainOrder.orderStatus))
// 联系客服
const goService = () => {
	Tool.goPage.push("/pages/my/contactUs")
}
// 检测支付状态
const checkPayStatus = async () => {
	// 支付结算回调有延迟
	payLoading.value = "支付成功，正在出票"
	const { data } = await request.get(
		`/order/GroupTicketOrder?orderId=${routerParams.orderId}`
	)
	if (data.orderStatus === "20") {
		//支付结算状态
		timer = setTimeout(() => {
			checkPayStatus()
		}, 2000)
	} else {
		location.reload()
	}
}

//返回时返回首页
onBackPress(() => {
	Tool.goPage.tab("home")
	return true
})

const refundOrderVisible = ref(false)
const footer = ref(null)
const isCheckAll = ref(false) //是否全选

const rest = ref(false) //二维码显示

// 支付倒计时
const payCountDown = reactive({
	minute: "00",
	second: "00"
})
const refundTicketList = ref([]) //退票列表
const order = reactive({
	isTravelCard: false, // 是否是权益卡
	isSingle: false, // 是否是单票
	isCompose: false, // 是否是组合票
	composeInfo: {}, // 组合票基础信息
	ticketInfo: {}, // 单票基础信息
	travelCardInfo: {}, // 权益卡基础信息
	refundOrderList: [], // 退票列表
	refundDetail: {}, // 退单详情
	createTime: "", // 创建时间
	pageTitle: "", // 页面标题
	orderStatus: "", // 订单状态
	ticketNumber: 0, // 门票数量
	certId: "", // 区块链交易证书 id
	ticketName: [], // 门票名称
	enterTime: "", //入园时间
	orderType: "", //订单类型
	orderGroupId: "", // 主订单 id
	tradeNo: "", // 结算系统交易号
	ticketPackageInfo: [], // 套票数据
	composeTicketList: [], // 组合票门票列表
	touristInfo: [], // 游客信息
	showRefund: false, // 是否展示退票
	getTicketMan: {}, // 取票人
	merchantId: "", // 商户 id
	refundAmount: 0, // 退款金额
	refundFee: 0, // 退款手续费
	needIdentity: false, // 此订单是否需要身份证入园
	isSingleMorePeople: false, // 单票是否一票多人
	showQRCode: false, // 是否展示二维码
	showFace: false, // 是否展示人脸
	isRefund: true // 是否可以退票
})

watch(
	() => order.refundOrderList,
	value => {
		//出库失败时是否需要退款
		if (
			value.length > 0 &&
			(order.orderStatus == 32 || order.orderStatus == 33)
		) {
			order.isRefund = false
		} else order.isRefund = true
	}
)
function arraysAreEqual(array1, array2) {
	if (array1.length !== array2.length) {
		return false
	}
	array1 = array1.sort()
	array2 = array2.sort()
	for (let i = 0; i < array1.length; i++) {
		if (array1[i] !== array2[i]) {
			return false
		}
	}
	return true
}

/**
 * 设置退票列表
 * @param {Object} type all:全选，none:全不选，空：单个选择
 * @param {Object} list 设置的退票列表
 */
const setRefundTicket = ({ type, list }) => {
	const ticketList = [] // 所有票 id 集合
	if (order.isSingle) {
		order.touristInfo.forEach(element => {
			ticketList.push(element.ticketNumber)
		})
	} else if (order.isCompose) {
		console.log(order)
		order.composeTicketList.forEach(item => {
			item.orderTicketInfoList?.forEach(e => {
				ticketList.push(e.ticketNumber)
			})
		})
	}

	if (!type) {
		refundTicketList.value = list
	} else if (type == "all") {
		refundTicketList.value = ticketList
	} else if (type == "none") {
		refundTicketList.value = []
	}
	isCheckAll.value = arraysAreEqual(refundTicketList.value, ticketList)
}
//取消订单
const onCancelOrder = async orderId => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.put(`/order/orderCancel`, {
			orderId: orderId
		})
		uni.showToast({
			title: "订单取消成功！",
			icon: "none",
			duration: 2000
		})
		//刷新页面
		Tool.goPage.tab("order")
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

const onTimeDown = async endTime => {
	const nowTime = dayjs().valueOf()
	const surplusTime = (endTime - nowTime) / 1000

	if (surplusTime > 0) {
		const m = parseInt(surplusTime / 60)
		const s = parseInt(surplusTime % 60)
		payCountDown.minute = m < 10 ? "0" + m : m
		payCountDown.second = s < 10 ? "0" + s : s
		setTimeout(() => {
			onTimeDown(endTime)
		}, 1000)
	} else {
		onCancelOrder(mainOrder.orderGroupId)
	}
}
// // 是否展示区块链交易
// const onShowBlock = orderInfo => {
// 	let isShow = false;
// 	if (
// 		(order.isSingle && !routerParams.refundId && order.orderStatus == 30)
// 	) {
// 		// 单票
// 		order.certId = childrenOrder.order.certId;
// 		const { orderTicketInfoList } = childrenOrder.orderProductTicket[0];
// 		if (orderTicketInfoList.length > 0) {
// 			orderTicketInfoList.map(e => {
// 				if (e.ticketStatus == "4" || !e.ticketStatus) isShow = true;
// 			});
// 			isShow = !isShow;
// 		} else {
// 			isShow = true;
// 		}
// 	} else if (routerParams.refundId && orderRefundList.length > 0) {
// 		orderInfo.certId = orderRefundList[0].certId;
// 		isShow = true;
// 	}
// 	return orderInfo.certId && isShow;
// };
//跳转订单详情
const goOrderDetail = () => {
	const { refundStatus, storeId, refundId, ...params } = routerParams
	if (refundStatus) {
		//只有退单可以跳
		Tool.goPage.push("/pages/orderDetail/orderDetail?" + objToUrlPath(params))
	}
}
// 获取检票规则
const getCheckRule = async checkId => {
	try {
		const { code, data } = await request.get(`/ticketCheck/info`, {
			id: checkId
		})
		return data.identityTypeList
	} catch (err) {
		console.log(err)
	}
	return null
}


//获取订单
const getOrderInfo = async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.get(
			`/order/GroupTicketOrder?orderId=${routerParams.orderId}`
		)

		const { orderInfo: chilrenOrderList, ...rest } = data
		Object.assign(childrenOrder, chilrenOrderList[0])

		const isCompose = chilrenOrderList[0].orderProductTicket.some(
			item => item.ticketType === 6
		)
		// 设置 tradeNo
		order.tradeNo = data.orderInfo[0].order.tradeNo
		// 判断订单类型
		if (data.orderType === "JQTC") {
			// 此订单为权益卡
			order.isTravelCard = true
			order.travelCardInfo = data.orderInfo[0].orderTravelCard[0]
		} else if (isCompose) {
			// 此订单为组合票
			order.isCompose = true
			// 设置组合票信息（易旅宝没有购物车功能，所以只有一个子订单）
			order.composeInfo = data.orderInfo[0].orderProductTicket.find(
				item => item.ticketType === 6
			)
			// 设置组合票的门票列表
			order.composeTicketList = []
			data.orderInfo.forEach(item => {
				const list = item.orderProductTicket.filter(
					item => item.ticketType !== 6
				)
				order.composeTicketList.push(...list)
			})
		} else {
			// 此订单为单票
			order.isSingle = true
			order.ticketInfo = data.orderInfo[0].orderProductTicket[0] // 设置单票信息
			console.log('order.ticketInfollllllllllllllllll');
			console.log(order.ticketInfo);

		}

		Object.assign(mainOrder, rest)

		// 订单创建时间
		order.createTime = dayjs(data.createTime).format("YYYY-MM-DD HH:mm:ss")
		// 设置主订单 ID
		order.orderGroupId = data.orderGroupId

		// 设置退票列表
		order.refundOrderList = data.orderInfo[0].orderRefund.map(n => ({
			...n,
			refundTime: dayjs(n.refundTime).format("YYYY-MM-DD HH:mm:ss")
		}))
		// 设置退单详情
		if (order.refundOrderList.length > 0) {
			order.refundDetail = {
				refundId: order.refundOrderList[0].refundId,
				refundAmount: order.refundOrderList[0].refundAmount,
				refundFlow: []
			}
			const { orderStatus } = data.orderInfo[0]
			// 退款中
			if (orderStatus?.refundStatus >= "50") {
				order.refundDetail.refundFlow.push({
					title: "退款中"
				})
			}
			// 退款成功
			if (orderStatus?.refundStatus === "51") {
				order.refundDetail.refundFlow.push({
					// time: orderStatus?.refundTime,
					title: "退款成功",
					content: `¥${order.refundOrderList[0]?.refundAmount} 已原路返回`
				})
			}
			// 退款失败
			if (orderStatus?.refundStatus === "52") {
				order.refundDetail.refundFlow.push({
					title: "退款失败",
					type: "refundFailure"
				})
			}
			// 反转数组
			order.refundDetail.refundFlow.reverse()
			// 最新的状态添加时间
			if (order.refundDetail.refundFlow[0])
				order.refundDetail.refundFlow[0].time = dayjs(
					orderStatus?.refundTime
				).format("YYYY-MM-DD HH:mm:ss")
		}

		// 设置标题
		order.pageTitle = routerParams.refundId ? "退单详情" : "订单详情"
		// 设置订单状态
		order.orderStatus = data.orderStatus
		// 设置门票数量
		if (order.isTravelCard) {
			order.ticketNumber = 1
		} else if (order.isSingle) {
			order.ticketNumber = data.orderInfo[0].orderProductTicket[0].num
		} else {
			order.ticketNumber = order.composeTicketList.reduce(
				(a, n) => a + n.num,
				0
			)
		}

		// 设置区块链交易证书 id
		if (routerParams.refundId && orderRefundList.length > 0) {
			order.certId = order.refundOrderList[0].certId
		} else if (order.orderStatus === 30) {
			order.certId = data.orderInfo[0].order.certId
		}

		// 支付倒计时
		if (data.orderStatus == 20) {
			const halfHour = 30 * 60 * 1000
			const endTime = dayjs(order.createTime).valueOf() + halfHour
			onTimeDown(endTime)
		}

		// 设置此订单的票名
		if (order.isCompose) {
			order.ticketName = [order.composeInfo.productName]
		} else if (order.isTravelCard) {
			const { scenicName, productSkuName } = order.travelCardInfo
			order.ticketName = [scenicName, productSkuName]
		} else {
			const { scenicName, productSkuName, ticketType } = order.ticketInfo
			order.ticketName = [scenicName, productSkuName]
		}

		// 设置入园时间
		if (order.isSingle) {
			order.enterTime = order.ticketInfo.day
			if (order.ticketInfo.timeShareBeginTime) {
				// 分时
				order.enterTime += ` ${order.ticketInfo.timeShareBeginTime}-${order.ticketInfo.timeShareEndTime}`
			} else if (order.ticketInfo.validityDay) {
				// 有效期
				order.enterTime += ` (${order.ticketInfo.validityDay}天有效)`
			}
		} else if (order.isCompose) {
			order.enterTime = order.composeInfo.day
		}

		// 设置类型
		if (data.orderType === "JQTC") {
			order.orderType = "权益卡"
		} else if (order.isCompose) {
			order.orderType = "组合套票"
		} else {
			order.orderType = ticketType[order.ticketInfo.productType]
		}

		// 设置套票数据
		if (order.isCompose) {
			//组合票
			const comTicketNum = order.composeInfo.num // 组合票的张数
			const comTicketList = Array(comTicketNum).fill([])
			const copyComposeTicketList = JSON.parse(
				JSON.stringify(order.composeTicketList)
			)

			// 组合票门票列表
			order.composeTicketList.forEach(ticket => {
				// 是否一票多人
				let isMorePeople = false
				if (ticket.orderTicketInfoList.length > 0) isMorePeople = ticket.orderTicketInfoList[0].issueTicketType === 1
				ticket.isMorePeople = isMorePeople
				ticket.orderId = data.orderGroupId
			})


			order.ticketPackageInfo = comTicketList.map(comTicket => {
				const oneCom = []
				copyComposeTicketList.map(n => {
					// 是否一票多人
					let isMorePeople = false

					if (n.orderTicketInfoList.length > 0) {
						isMorePeople = n.orderTicketInfoList[0].issueTicketType === 1
					}

					const forLength = isMorePeople ? 1 : n.num / comTicketNum
					// 一张组合票里，一票多人的人数
					const morePeopleNum = isMorePeople ? n.num / comTicketNum : 0

					for (let i = 0; i < forLength; i++) {
						let obj = {
							showRefund: false,
							morePeopleNum,
							scenicId: n.scenicId,
							ticketType: n.ticketType,
							productType: n.productType,
							productName: n.productName,
							productSkuId: n.productSkuId,
							timeShareBeginTime: n.timeShareBeginTime,
							timeShareEndTime: n.timeShareEndTime,
							timeShareId: n.timeShareId,
							validityDay: n.validityDay,
							orderId: data.orderGroupId,
							scenicInfoVo: n.scenicInfoVo
						}
						if (n.orderTicketInfoList.length > 0) {
							const outTicket = n.orderTicketInfoList.splice(0, 1)[0]
							obj = {
								...outTicket,
								...obj
							}
						}
						oneCom.push(obj)
					}
				})
				return [...oneCom]
			})

		}

		// 设置单票实名信息
		if (order.isSingle) {
			order.touristInfo = order.ticketInfo.orderTicketInfoList
			order.touristInfo.map((n, i) => {
				n.orderId = data.orderGroupId
				n.index = i
				n.showRefund = false
				n.scenicId = order.ticketInfo.scenicId
			})
		}

		// 设置取票人数据
		if (mainOrder.orderType != "JQTC") {
			const { pilotName, pilotIdentity, pilotPhone } =
				data.orderInfo[0].orderTicket
			const ticketMan = {}
			if (pilotName) ticketMan.name = pilotName
			if (pilotIdentity) ticketMan.identity = pilotIdentity
			if (pilotPhone) ticketMan.mobile = pilotPhone
			order.getTicketMan = ticketMan
		}

		// 查询检票规则
		if (order.isCompose) {
			// 组合票
			const { ticketPackageInfo } = order
			if (ticketPackageInfo && ticketPackageInfo.length > 0) {
				for (let i = 0; i < ticketPackageInfo.length; i++) {
					const item = ticketPackageInfo[i]
					for (let j = 0; j < item.length; j++) {
						const e = item[j]
						if (e.checkId) {
							const identityTypeList = await getCheckRule(e.checkId)
							if (identityTypeList) {
								e.identityTypeList = identityTypeList
								if (identityTypeList.includes("身份证")) {
									order.needIdentity = true
								}
							}
						}
					}
				}
				setComposeTicketStatus()

				order.composeTicketList.forEach(ticket => {
					if (ticket.checkId) {
						getCheckRule(ticket.checkId).then(identityTypeList => {
							if (identityTypeList) {
								ticket.identityTypeList = identityTypeList || []
								ticket.showFace = false // 默认不展示人脸
								ticket.showQRCode = false // 默认不展示二维码
								ticket.showRealName = false // 默认不是实名
								if (identityTypeList.includes("身份证")) {
									order.needIdentity = true
									ticket.showRealName = true
								}
								if (
									// ticket.ticketStatus == 0 &&
									ticket.identityTypeList.includes("人脸")
								) {
									// 展示人脸
									ticket.showFace = true
								}
								if (
									ticket.identityTypeList.includes("票") &&
									ticket.printStr
								) {
									// 展示二维码
									ticket.showQRCode = true
								}
							}
						})
					}
				})

			}
		} else if (order.isSingle) {
			// 单票
			order.touristInfo.forEach(item => {
				item.showQRCode = false
				if (item.checkId) {
					getCheckRule(item.checkId).then(identityTypeList => {
						if (!identityTypeList) return
						item.identityTypeList = identityTypeList
						// 是否展示二维码
						if (identityTypeList.includes("票") && item.printStr) {
							item.showQRCode = true
						}
						// 是否展示人脸
						if (item.identityTypeList.includes("人脸")) {
							item.showFace = true
						}
						if (identityTypeList.includes("身份证")) {
							order.needIdentity = true
						}
						// 是否实名
						if (item.showFace || order.needIdentity) {
							item.isRealName = true
						}
					})
				} else {
					item.showQRCode = false
					item.showFace = false
					item.isRealName = false
				}
			})
		}
		console.log(order)
		// 单票是否一票多人
		if (order.isSingle && order.touristInfo.length > 0) {
			console.log("单票是否一票多人", data, order.touristInfo)
			order.isSingleMorePeople = order.touristInfo[0].issueTicketType === 1
		}

		console.log("处理过后的订单详情：")
		console.log(order)

		order.merchantId = data.orderInfo[0].order.merchantId
		order.totalAmount = data.totalAmount
		order.payType = data.payType
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

// 组合票是否展示、人脸、二维码
const setComposeTicketStatus = () => {

	order.ticketPackageInfo.forEach(item => {
		item.forEach(e => {
			e.showFace = false  // 默认不展示人脸
			e.showQRCode = false // 默认不展示二维码


			if (
				e.ticketStatus == 0 &&
				e.identityTypeList &&
				e.identityTypeList.includes("人脸")
			) {
				// 展示人脸
				e.showFace = true
			}
			if (
				e.identityTypeList &&
				e.identityTypeList.includes("票") &&
				e.printStr
			) {
				// 展示二维码
				e.showQRCode = true
			}
			console.log('productName', e.productName, e.showQRCode);
		})
	})

}

// 初始化
const init = async () => {
	await getOrderInfo()

	if (routerParams.payloading == 1 && order.orderStatus === "20")
		await checkPayStatus()
}

//预订须知
const bookContent = ref("")
const showBookContent = ref(false)
const bookExplain = async e => {
	console.log(e)
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { code, data } = await request.get(`/simpleTicket/info/${e.goodsId}`)
	uni.hideLoading()
	bookContent.value = markdownToHtml(data.notice)
	showBookContent.value = true
}

// 跳转到区块链证书页面
const goBlockchainCertificate = (txId) => {
	if (!txId) return
	Tool.goPage.push(`/pages/blockchainCertificate/blockchainCertificate?txId=${txId}`)
}

onShow(init)
</script>
<style lang="scss" scoped>
.order-detail.unPayTheme {
	--color1: #fff;
	--color2: #7ed3da;
}

.order-detail.payTheme {
	--color1: #fff;
	--color2: #61b4fc;
}

.order-detail {
	margin-bottom: 100rpx;
	min-height: 100%;
	padding: 40rpx 30rpx 150rpx;
	background: #f1f1f1;

	.panel-box {
		overflow: hidden;
		position: relative;
		background-color: #fff;
		padding: 30rpx;
		margin: 20rpx 0;
		border-radius: 12rpx;
	}

	.order-status {
		.status {
			display: flex;

			font-size: 50rpx;
			font-weight: 600;
			color: #14131f;

			.price {
				margin-left: 18rpx;
				font-size: 54rpx;
				color: #f43636;
			}

			.util {
				font-size: 42rpx;
			}
		}

		.order-status-refund {
			display: flex;
			align-items: center;
			margin-left: 20rpx;

			.num {
				border-radius: 5rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 20rpx;
				color: #fff;
				margin-right: 18rpx;
				font-size: 24rpx;
				background: var(--theme-color);
				height: 44rpx;
			}
		}

		.count-down {
			margin-top: 20rpx;
			color: #2f587d;
			font-size: 28rpx;
		}
	}

	.order-info {
		>.title {
			color: #14131f;
			padding-bottom: 30rpx;
			margin-bottom: 30rpx;
			font-size: 34rpx;
			font-weight: 500;
			border-bottom: 2rpx solid rgb(228, 228, 228, 0.4);
		}

		>.order__item {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #14131f;
			word-break: break-all;

			&:not(:last-child) {
				margin-bottom: 20rpx;
			}

			.order__item-title {
				font-size: 28rpx;
				min-width: 140rpx;
				color: #6e6e6e;
				margin-right: 24rpx;
			}
		}
	}

	.ticket-info {
		.ticket-title {
			display: flex;
			justify-content: space-between;
			font-size: 38rpx;
			font-weight: 500;
			color: #050505;
			margin-bottom: 23rpx;
		}

		>.ticket__item {
			display: flex;

			font-size: 28rpx;
			color: #14131f;

			&:not(:last-child) {
				margin-bottom: 20rpx;
			}

			.ticket__item-title {
				display: flex;
				justify-content: space-between;
				font-size: 28rpx;
				width: 117rpx;
				color: #6e6e6e;
				margin-right: 26rpx;
			}
		}

		.ticket-list {
			padding: 30rpx 20rpx;
			background-color: #f3f7fd;

			border-radius: 12rpx;

			.ticket-list-title {
				font-size: 34rpx;
				font-weight: 500;
				padding-bottom: 30rpx;
			}

			.ticket-item {
				padding-top: 30rpx;
				border-top: 2rpx solid rgb(228, 228, 228, 0.6);

				.title {
					font-size: 30rpx;
					font-weight: 500;
					color: #050505;
				}

				.yuyue {
					margin-top: 16rpx;
					font-size: 26rpx;
					font-weight: 300;
					color: #050505;
				}

				.ticket-realname {
					display: flex;
					margin: 20rpx 0;
					font-size: 28rpx;

					text:first-child {
						color: #6e6e6e;
						width: 110rpx;
					}

					text:last-child {
						font-weight: 400;
						color: #14131f;
					}
				}

				.ticket-img {
					margin: 20rpx 0;
					display: flex;

					.icon {
						width: 80rpx;
						height: 80rpx;
						margin-right: 43rpx;
					}
				}
			}
		}
	}

	.ticket-tip {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #65300f;
		height: 56rpx;
		background-color: #fff5e8;
		padding-left: 30rpx;

		.left {
			display: flex;
			align-items: center;
		}

		.right {
			display: flex;
			align-items: center;
			color: #1c78e9;
		}
	}

	.ticket-content {
		border-radius: 20rpx;
		text-align: center;
		background-color: #fff;

		image {
			width: 458rpx;
			height: 386rpx;
			margin: 210rpx 0 340rpx 0;
		}
	}
}

.service-btn {
	color: var(--theme-color);
}

.chain-record-pop {
	width: 750rpx;
	height: 1248rpx;
	max-height: 80vh;
	background: #fff;
	border-radius: 36rpx 36rpx 0 0;
	padding: 0 30rpx;
	display: flex;
	flex-direction: column;

	.pop-title {
		width: 100%;
		height: 96rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);

		.title {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-weight: bold;
			font-size: 36rpx;
			color: #000;
		}

		.right {
			font-size: 33rpx;
			color: #349fff;
		}
	}

	.pop-content {
		margin-top: 58rpx;
		position: relative;
		width: 100%;
		height: 0;
		flex: 1;

		.tips {
			position: absolute;
			top: 0;
			left: 16rpx;
			width: 2rpx;
			height: 100%;
			background: #349fff;
			opacity: 0.3;
		}

		.content {
			height: 100%;
			overflow: scroll;
			padding: 0 0 55rpx 74rpx;

			.item {
				margin-bottom: 55rpx;

				.info-time {
					position: relative;
					font-size: 28rpx;

					.info-time-dot {
						position: absolute;
						top: 50%;
						left: -74rpx;
						transform: translateY(-50%);
						width: 34rpx;
						height: 34rpx;
						background: #fff;
						border: 2rpx solid #349fff;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 50%;

						>view {
							width: 18rpx;
							height: 18rpx;
							background: #349fff;
							border-radius: 50%;
						}
					}

					.info-title {
						font-size: 30rpx;
						color: #000000;
						font-weight: 500;
						font-weight: bold;
					}
				}

				.info-date {
					color: #14131f;
					font-size: 24rpx;
					margin-top: 20rpx;
					opacity: 0.5;
				}

				.info-type {
					margin-top: 20rpx;
					font-size: 28rpx;
					color: #050505;

					.text-btn {
						color: var(--theme-color);
					}
				}
			}
		}
	}
}
</style>
