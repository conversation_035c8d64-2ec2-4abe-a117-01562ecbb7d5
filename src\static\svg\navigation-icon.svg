<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="导览" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="景区详情-1" transform="translate(-584.000000, -772.000000)">
            <g id="402导航" transform="translate(584.201010, 772.201010)">
                <g transform="translate(19.798990, 19.798990) rotate(45.000000) translate(-19.798990, -19.798990) translate(5.798990, 5.798990)">
                    <rect id="矩形" fill="var(--theme-color, black)" fill-rule="nonzero" opacity="0" x="0" y="0" width="28" height="28"></rect>
                    <polygon id="路径" fill="var(--theme-color, black)" fill-rule="nonzero" points="14 1.75 4.33125 25.33125 5.25 26.25 14 22.378125 22.771875 26.25 23.690625 25.33125"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>