<style lang="scss" scoped>
.error-main {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.error-btn {
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		background-color: var(--theme-color);
		border-radius: 8rpx;
		width: 200rpx;
		height: 60rpx;
	}
}
</style>
<template>
	<view class="error-main">
		<image class="icon" src="@/static/image/500.png" mode="scaleToFill" />
		<view>抱歉，服务器出错了</view>
		<view class="error-btn" @click="() => $router.go(-1)">重试</view>
	</view>
</template>

<script setup lang="ts">
console.log("500.ts")
</script>
