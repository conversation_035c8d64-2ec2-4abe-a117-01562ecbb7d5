// 事件总线，用于发布订阅模式
export default {
  events: {},
  // 订阅事件
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = []
    }
    this.events[eventName].push(callback)
  },
  // 发布事件
  emit(eventName, data) {
    if (this.events[eventName]) {
      this.events[eventName].forEach(callback => callback(data))
    }
  },
  // 取消订阅
  off(eventName, callback) {
    if (!callback) {
      delete this.events[eventName]
    } else if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(cb => cb !== callback)
    }
  }
} 