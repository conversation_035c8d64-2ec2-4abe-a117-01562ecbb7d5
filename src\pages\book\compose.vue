<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>门票预订</y-nav-bar>
	<view class="book">
		<!-- 预定信息 -->
		<view class="book-num">
			<view class="title">
				<view>{{ composeInfo.name }}</view>
			</view>
			<view class="enter-time" v-for="(item, index) in ticketDayList" :key="index">
				<view class="com-title" v-if="item.text">{{ item.text }}</view>
				<view class="title">
					<y-font-weight>入园时间</y-font-weight>
				</view>
				<l-calendar v-model:day="item.day" :dateRange="composeInfo._dateRange" :price="composeInfo.totalPrice" />
			</view>
			<view class="add-number">
				<view class="left">
					<y-font-weight>购买数量</y-font-weight>
				</view>
				<y-number v-model="ticketNum" :numRange="numRange" />
			</view>
			<view class="book-notice">
				<view class="left" v-if="composeInfo?.isRealName == 1">
					需要携带身份证入园
				</view>
				<view class="right" @tap="popModel(false)" v-if="composeInfo.note">
					预订须知 <uni-icons type="forward" color="#65300F"></uni-icons>
				</view>
			</view>
			<y-popup v-model="isPopUpWin" type="reserve" title="预订须知">
				<view v-if="composeInfo.note" class="rich-content">
					<div v-html="composeInfo.note"></div>
				</view>
				<y-empty v-else>暂无内容</y-empty>
			</y-popup>
		</view>
		<!-- 套票信息 -->
		<ticketPackageInfo v-model:ticketList="ticketList" :composeInfo="composeInfo" :ticketNum="ticketNum"
			@onChangeComposeTimePeriod="onChangeComposeTimePeriod" />
		<!-- 取票人 -->
		<y-collect-ticket-man edit v-model="collectTicketMan" />
		<!-- 占位 -->
		<view class="solid"></view>
		<!-- 合计 -->
		<view class="total-box">
			<view class="left">
				<view class="money">
					<text class="unit">¥</text>
					{{ (composeInfo.totalPrice * ticketNum).toFixed(2) }}
				</view>
				<view class="number">共计：{{ ticketNum }} 张</view>
			</view>
			<view class="right" @tap="toPay">去下单</view>
		</view>
	</view>
</template>
<script setup>
import { reactive, ref, watch, onMounted } from "vue"
import request from "@/utils/request.js"
import ticketPackageInfo from "./component/ticketPackageInfo.vue"
import { onLoad } from "@dcloudio/uni-app"
import { hexToRgb, getWeekDate, markdownToHtml, compareTimes } from "@/utils/tool.js"

import dayjs from "dayjs"
import { goodsType, ticketType } from "@/utils/constant.js"
import { getEnv } from "@/utils/getEnv";

const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
})
const ticketNum = ref(0) //购买的票数
const numRange = ref([1, 99])
const ticketStock = reactive({
	// 票库存
	num: 0
})
const ticketList = ref([]) //组合商品信息
const composeInfo = ref([]) //组合票信息

// 景区---当前选中的时段信息
const timePeriodField = ref({})

// 组合票 --- 分时时段信息
let timeLaws = reactive({})
// 库存变化，更新可购买数量
watch(
	() => ticketStock.num,
	() => {
		setTicketNumRange()
	}
)

const ticketDayList = ref([])
// 打开
watch(() => composeInfo.value, (val) => {
	const { composeTimeSwitch } = val
	const list = []
	if (composeTimeSwitch) {
		// 集合时间
		list.push({
			id: '',
			text: "",
			day: dayjs().format("YYYY-MM-DD")
		})
	} else {
		// 非集合时间
		ticketList.value.forEach(e => {
			list.push({
				id: e.goodsId,
				text: e._title,
				day: dayjs().format("YYYY-MM-DD")
			})
		})
	}
	ticketDayList.value = list
})

const isPopUpWin = ref(false)
const popModel = async () => {
	isPopUpWin.value = true
}

let userData = {}
//取票人
const collectTicketMan = reactive({ name: "", identity: "", mobile: "" })
//获取组合票
const init = async () => {
	userData = await Tool.getUserInfo()
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	try {
		const { data } = await request.get(
			`/appTicket/composeGoodsDetail/${routerParams.storeGoodsId}`
		)
		const { composeGoodsInfo, goodsDetail } = data
		let isIncludeOwner = false // 是否包含仅限本人购买的票
		const endTimeList = []
		goodsDetail.map(e => {
			if (e.buyOwner) {
				checkAutonym()
				isIncludeOwner = true
			}
			if (e.timeShareEndTime) endTimeList.push(e.timeShareEndTime)
		})

		if (endTimeList.length > 0) setCanBuyDate(endTimeList)
		// 套票组件用到的数据
		ticketList.value = goodsDetail.map(element => {
			return {
				...element,
				_title: `${element.name}-${ticketType[element.proType]}`,
				_goodsType: goodsType[element.goodsType],
				_isRealName: element.isRealName == 1,
				num: element.num,
				timeShare:
					element.timeShareId == 0
						? `${element.validityDay}天有效`
						: `${element.timeShareBeginTime}-${element.timeShareEndTime}`,
				realNameInfo: [],
				timeLaws: element?.timeLaws || [],
			}
		})

		composeInfo.value = {
			_day: dayjs().format("YYYY-MM-DD"),
			...composeGoodsInfo
		}


		ticketNum.value = 1
		ticketStock.num = 99
		setTicketNumRange()
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

// 检测实名状态
const checkAutonym = () => {
	let isAutonym = true
	if (!userData.realNameInfo?.idNumber) {
		isAutonym = false
		uni.showModal({
			title: "当前登录用户未实名",
			confirmText: "去实名",
			success: obj => {
				if (obj.confirm) {
					Tool.goPage.replace(`/pages/certification/certification`)
				} else if (obj.cancel) {
					Tool.goPage.back()
				}
			}
		})
	}
	return isAutonym
}

// 设置可选入园时间，不可购买过期的票
const setCanBuyDate = endTimeList => {
	const now = dayjs()
	const isTomorrow = endTimeList.some(e =>
		now.isAfter(dayjs(`${now.format("YYYY-MM-DD")} ${e}`))
	)
	const tomorrow = now.add(1, "day").format("YYYY-MM-DD")
	if (isTomorrow && composeInfo.value._dateRange?.[0] !== tomorrow) {
		composeInfo.value._dateRange = [tomorrow]
		composeInfo.value._day = tomorrow
	}
}
// 设置购买数量范围
const setTicketNumRange = () => {
	const minList = ticketList.value.map(item => item.minPeople / item.num)
	const maxList = ticketList.value.map(item => item.maxPeople / item.num)
	const min = Math.max(...minList)
	const max = Math.max(...maxList)

	if (min > 0) numRange.value[0] = min
	if (max > 0) numRange.value[1] = max
	if (min > 1) ticketNum.value = min
}

//组合票下单数据
const composeMergeData = () => {
	const { storeId } = routerParams
	// 添加组合票 - 单票信息
	console.log(ticketList.value);

	const productTicket = ticketList.value.map((n, i) => {
		const realNameInfo = n.realNameInfo.map(e => {
			return {
				name: e.name,
				identity: e.idCard,
				checkType: n.checkType,
				faceImageUrl: e.faceImageUrl
			}
		})

		// 分时时段
		const timeShareId = (timeLaws?.[n.goodsId]?.[composeInfo.value._day] || []).filter((_tp) => _tp.active)?.[0]?.timeShareId || '';

		// 获取票的游玩时间
		const { composeTimeSwitch } = composeInfo.value
		let day = ''
		if (composeTimeSwitch) {
			day = ticketDayList.value[0].day
		} else {
			day = ticketDayList.value.find(e => e.id === n.goodsId)?.day
		}

		return {
			isComposeSimpleGood: 1,
			// applicationCode: n.collectionCode, // 收款场景编码
			day: dayjs(day).format("YYYY-MM-DD"), // 游玩时间
			num: n.num * ticketNum.value, // 购买数量
			parentProductId: routerParams.storeGoodsId, // 组合票产品 id
			productId: n.goodsId, // 产品 ID
			timeShare: n.timeShareId
				? `${n.timeShareBeginTime}~${n.timeShareEndTime}`
				: "", //分时预约名称
			realNameInfo, // 实名身份证
			productSkuId: n.goodsId, // 产品 skuId
			isCompose: 0, // 是否组合票 1 为组合票，0 为单票
			timeShareId,
		}
	})
	// 添加组合票信息
	productTicket.push({
		isComposeSimpleGood: 0,
		day: dayjs(composeInfo.value._day).format("YYYY-MM-DD"),
		num: ticketNum.value,
		productId: routerParams.storeGoodsId,
		isCompose: 1 // 是否组合票 1 为组合票，0 为单票
	})
	// 结算回调地址
	const returnUrl = `${getEnv().VITE_MALL_HOST
		}/#/pages/orderDetail/orderDetail?payloading=1&storeId=${storeId}&orderId=`

	// 获取系统来源
	const sourceType = Tool.getSystemSource()

	return {
		orderType: "JQMP", // 订单类型
		agentId: composeInfo.value.distributorId, //代理商 id 供应商
		sourceType, // 渠道
		scenicTicket: {
			pilotName: collectTicketMan.name, // 领票人姓名
			pilotPhone: collectTicketMan.mobile.split(" ").join(""), // 领票人电话
			pilotIdentity: collectTicketMan.identity // 领票人身份证
		},
		productTicket, //产品列表
		storeId, // 店铺 id
		returnUrl
	}
}

//信息检验
const examine = params => {
	if (!ticketStock.num && !routerParams.storeGoodsId) {
		uni.showModal({
			content: "库存不足",
			showCancel: false
		})
		return false
	}
	let errorInfo = "" //错误信息
	let identityList = []
	let showFaceTip = false //是否展示人脸提示弹窗
	//组合票
	// params.productTicket.map(n => {
	// 	if (n.realNameInfo && n.realNameInfo.length > 0) {
	// 		n.realNameInfo.map(e => {
	// 			identityList.push(e.identity)
	// 		})
	// 	}
	// })
	// state.composetTouristInfo.forEach(e => {
	// 	e.forEach(item => {
	// 		if (item.isRealName) {
	// 			if (item.realNameInfo.length === 0) errorInfo = "请填写完整的身份信息"
	// 		}
	// 		const onlyFace =
	// 			item.checkType.includes(1) && item.checkType.length === 1

	// 		if (onlyFace) {
	// 			const needFace = item.realNameInfo.some(t => t.faceImageUrl === "")
	// 			if (needFace) {
	// 				errorInfo = "请上传人脸识别照片"
	// 			}
	// 		}
	// 		if (!showFaceTip) {
	// 			showFaceTip = item.checkType.includes(1) && item.checkType.length > 1
	// 		}
	// 	})
	// })
	console.log('数据校验', params, ticketList.value);
	// 校验提交的实名信息数量
	const needRealNameNum = ticketList.value.reduce((sum, item) => {	// 需要实名的数量
		return item._isRealName ? sum + item.num * ticketNum.value : sum;
	}, 0);
	const renlNameNum = params.productTicket.reduce((sum, item) => {    // 实际实名的数量
		const length = item.realNameInfo?.length
		return length > 0 ? sum + length : sum;
	}, 0);
	const diffNum = needRealNameNum - renlNameNum

	if (diffNum > 0) {
		errorInfo = `还有${diffNum}位游客信息未填写`
	}


	if (errorInfo) {
		uni.showModal({
			content: errorInfo,
			showCancel: false
		})
	}

	const isPass = errorInfo === ""

	return { isPass, showFaceTip }
}
//去下单
const toPay = async () => {
	if (ticketStock.num == 0) return
	const params = composeMergeData()

	const { isPass, showFaceTip } = examine(params)
	if (!isPass) return
	if (showFaceTip) {
		await showFaceTipModal(
			"门票支持多种入园方式，若选择人脸入闸，请及时录入人脸信息"
		)
	}
	uni.showLoading({
		mask: true,
		title: "易旅宝"
	})
	try {
		console.log('下单参数', params);

		const { code, data } = await request.post("/order/create", params)
		Tool.goPage.push(`/pages/orderDetail/orderDetail?orderId=${data.orderId}`)
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

function showFaceTipModal(content) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			content: content,
			showCancel: false,
			success: function (res) {
				if (res.confirm) {
					resolve()
				}
			}
		})
	})
}

// 组合票 设置分时时段信息，用于创建订单
const onChangeComposeTimePeriod = (_timeLaws) => {
	timeLaws = _timeLaws;
}
// 初始化
onMounted(init)
</script>

<style lang="scss" scoped>
.book {
	padding: 36rpx 40rpx 0;
	margin-bottom: 180rpx;
	background: linear-gradient(180deg, #bbd9ff 0%, rgba(217, 233, 255, 0) 100%);

	.total-box {
		position: fixed;
		bottom: 0;
		left: 0%;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 140rpx;
		z-index: 3;
		padding-left: 57rpx;
		padding-right: 40rpx;
		background-color: #fff;

		.left {
			.money {
				font-size: 54rpx;
				font-weight: 600;
				color: #f43636;

				.unit {
					font-size: 42rpx;
				}
			}

			.number {
				font-size: 24rpx;
				font-weight: 400;
				color: #050505;
				line-height: 33rpx;
			}
		}

		.right {
			padding: 10rpx 54rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			font-size: 32rpx;
			font-weight: 600;
			color: #ffffff;
			line-height: 45rpx;
			letter-spacing: 1rpx;
			background: #ff9201;
			border-radius: 44rpx;

			.residue {
				font-size: 22rpx;
				font-weight: 400;
				color: #ffffff;
				line-height: 30rpx;
			}
		}
	}
}

.solid {
	height: 140rpx;
}

.book-num {
	margin-bottom: 20rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;

	.travel-card-img {
		width: 100%;
	}

	>.title {
		display: flex;
		justify-content: space-between;
		margin: 30rpx;
		padding-bottom: 30rpx;
		font-size: 38rpx;
		font-weight: 600;
		color: #050505;
		line-height: 44rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
	}

	.enter-time {
		margin: 30rpx 30rpx 0 30rpx;
		padding-bottom: 25rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);

		>.title {
			margin-bottom: 25rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #050505;
			line-height: 32rpx;
		}

		.calendar {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.calendar__list {
				flex: 1;
				display: flex;
				overflow: scroll;
				-ms-overflow-style: none;
				scrollbar-width: none;

				&::-webkit-scrollbar {
					display: none;
				}
			}

			.calendar__more {
				color: var(--theme-color);
				font-weight: 400;
				font-size: 26rpx;
				width: 90rpx;
				height: 164rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 3;
				box-shadow: -4px 0px 8px -2px rgba(219, 219, 219, 0.5);
			}

			.active {
				background: rgba(var(--theme-bg), 0.1) !important;
				border: 1px solid var(--theme-color) !important;
			}

			.item {
				flex: none;
				position: relative;
				display: flex;
				align-items: center;
				flex-direction: column;
				justify-content: center;
				padding: 0 15rpx;
				overflow: hidden;
				width: 136rpx;
				height: 164rpx;
				margin-right: 16rpx;
				border-radius: 8rpx;
				background: #fbfbfb;
				border: 1px solid #e1e1e2;

				.date {
					font-size: 25rpx;
					font-weight: 400;
					color: #050505;
					line-height: 38rpx;
				}

				.price {
					font-size: 32rpx;
					font-weight: 500;
					color: #f43636;
					line-height: 40rpx;

					.unit {
						font-size: 24rpx;
					}
				}
			}
		}
	}

	.book-notice {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #fff5e8;
		height: 56rpx;
		padding: 0 30rpx;
		color: #65300f;
		border-bottom-left-radius: 19rpx;
		border-bottom-right-radius: 19rpx;

		.left {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 33rpx;
		}

		.right {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 33rpx;

			>.icon {
				// width: 20rpx;
				margin-left: 8rpx;
			}
		}
	}

	.add-number {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 0 26rpx;
		margin: 0rpx 30rpx 0 30rpx;

		.left {
			font-size: 32rpx;
			font-weight: 500;
			color: #050505;
			line-height: 45rpx;
		}

		.right {
			display: flex;
			align-items: center;

			.across,
			.vertical {
				position: absolute;
				width: 22rpx;
				height: 4rpx;
				background-color: #fff;
				border-radius: 2rpx;
			}

			.add,
			.subtract {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50rpx;
				height: 50rpx;
				background: var(--theme-color);
				border-radius: 8rpx;

				&.disable {
					background-color: #f0f0f0;

					.across,
					.vertical {
						background-color: #c5c5c5;
					}
				}
			}

			.vertical {
				transform: rotate(90deg);
			}

			.number {
				margin: 0 30rpx;
			}
		}
	}

	.time-period {
		margin: 30rpx 30rpx 0 30rpx;
		padding-bottom: 25rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);

		.title {
			font-size: 28rpx;
		}

		.period-list {
			display: flex;
			flex-wrap: wrap;
			padding: 16rpx 0 0 0;

			.period-item-box {
				display: flex;
				margin: 0 0 16rpx 0;
				width: 33.3%;
				font-size: 22rpx;

				.period-item {
					padding: 4rpx 8rpx;
					background: #fbfbfb;
					border: 1px solid #e1e1e2;
					border-radius: 0.25rem;
				}

				.active {
					background: rgba(var(--theme-bg), 0.1) !important;
					border: 1px solid var(--theme-color) !important;
				}

				.disabled {
					background-color: #f0f0f0;
				}
			}
		}
	}
}

.com-title {
	margin: 30rpx 0 20rpx;
	color: #349FFF;
	font-size: 30rpx;
	font-weight: 500;
}
</style>
