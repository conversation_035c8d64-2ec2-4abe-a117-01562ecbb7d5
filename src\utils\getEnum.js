// 枚举对接接口
export const getArrFun = async ()=>{
	 const {data} = await request.exchangeHost(`/system/enums/enum/getEnumList?systemId=e-commerce`);
	 const {orderStatus} = data
	 return {
		orderStatus
	 }
}

// 接口对接后获取orderStatus枚举方法
export const orderStatusFun = ()=>{
	const enumArr = JSON.parse(localStorage.getItem('enumList'))
	const obj = {}
	 enumArr.orderStatus.forEach(item=>{
		obj[item.num] = item.message
	})
	return obj
}