<style lang="scss" scoped>
.ai-setting {
	right: 30rpx;
	width: 38rpx !important;
	height: 38rpx !important;
}
.setting__popup {
	width: 556rpx;
	background-color: #fff;
	border-radius: 15rpx;
	.setting__top {
		display: flex;
		justify-content: space-around;
		height: 160rpx;
		border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
	}

	.setting__item {
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #fff;

		color: #14131f;
		font-size: 28rpx;
		.setting__item__img {
			width: 52rpx;
			height: 52rpx;
			margin-bottom: 17rpx;
		}
	}
}
.setting__close {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 30rpx;
}
</style>
<template>
	<div>
		<image @click="open" class="ai-setting" src="@/static/image/ai/setting-icon.png" mode="widthFix" />
	</div>
	<uni-popup ref="popup" :mask-click="false" mask-background-color="rgba(0,0,0,0.4)" type="center">
		<div class="setting__popup">
			<div class="setting__top">
				<div class="setting__item" @click="onOpenVoice">
					<template v-if="openVoice">
						<image class="setting__item__img" src="@/static/image/ai/voice_open-icon.png" mode="widthFix" />
						<div>已打开</div>
					</template>
					<template v-else>
						<image class="setting__item__img" src="@/static/image/ai/voice_close-icon.png"
							mode="widthFix" />
						<div>已关闭</div>
					</template>
				</div>
				<div class="setting__item" @click="clearDialogue">
					<image class="setting__item__img" src="@/static/image/ai/dialogue-icon.png" mode="widthFix" />
					<div>清空对话</div>
				</div>
				<div class="setting__item" @click="goPage.push('/pages/my/userAgreement')">
					<image class="setting__item__img" src="@/static/image/ai/ysxy-icon.png" mode="widthFix" />
					<div>《隐私协议》</div>
				</div>
			</div>
			<div class="setting__close" @click="close">关闭</div>
		</div>
	</uni-popup>
</template>

<script setup>
import { ref, onMounted } from "vue"
const emits = defineEmits(["open", 'changeVoice'])
onMounted(() => {
	openVoice.value = uni.getStorageSync("voice") !== "close"
})
const openVoice = ref(true)
// 打开关闭声音
const onOpenVoice = () => {
	openVoice.value = !openVoice.value
	uni.setStorageSync("voice", openVoice.value ? "open" : "close")
	emits("changeVoice", openVoice.value)

}
// 清空对话
const clearDialogue = () => {
	emits("clearDialogue")
}
const popup = ref(null)
const open = () => {
	popup.value.open()
	emits("open", true)
}
const close = () => {
	popup.value.close()
	emits("open", false)
}
</script>
