<template>
	<template v-if="!props.cityKey">
		<scroll-view scroll-y="true" scroll-with-animation="true" :scroll-into-view="activeLetter" class="location-list"
			:class="[props.modelValue ? 'show' : 'hide']">
			<view class="shortcut">
				<view class="title">
					<text class="left">当前定位</text>
					<text class="right" @click="getLocation(true)">重新定位</text>
				</view>
				<view class="shortcut-box">
					<view class="city-box" @click="onChangeCity(curLocatin)">
						<image class="icon" src="@/static/image/location-icon.png" mode=""></image>
						{{ curLocatin.addressName }}
					</view>
				</view>
				<view class="title">
					<text class="left">热门城市</text>
				</view>
				<view class="shortcut-box">
					<view v-for="(item, index) in hotCity" :key="item.addressName" @click="onChangeCity(item)" class="city-box">
						{{ item.addressName }}</view>
				</view>
				<view v-for="(list, letter, i) in cityList" :key="letter" class="city-group">
					<view class="letter" :id="letter">{{ letter.toUpperCase() }}</view>
					<template v-for="(item, index) in list.data" :key="index">
						<view class="city-item" @click="onChangeCity(item)">{{
							item.addressName
						}}</view>
					</template>
				</view>
			</view>
		</scroll-view>
		<view class="indexed-list" v-show="props.modelValue">
			<template v-for="(list, letter, i) in cityList" :key="letter">
				<view class="index-item" @tap="onChangeIndex(letter)">{{
					letter.toUpperCase()
				}}</view>
			</template>
		</view>
	</template>
	<template v-if="props.cityKey">
		<view class="location-list" :class="[props.modelValue ? 'show' : 'hide']">
			<view class="city-group">
				<template v-for="(item, index) in resultCity" :key="item.addressName">
					<view class="city-item" @click="onChangeCity(item)">{{
						item.addressName
					}}</view>
				</template>
			</view>
		</view>
	</template>
</template>
<script setup>
import { reactive, ref, onBeforeMount, watch } from "vue"
import request from "@/utils/request.js"
import { getAddressByTx } from "@/utils/tool.js"

//热门城市
const hotCityByOriginal = [
	"香港",
	"北京",
	"上海",
	"广州",
	"杭州",
	"厦门",
	"武汉",
	"长沙",
	"重庆"
]
const hotCity = reactive([
	{
		addressName: "香港",
		addressId: 810100
	},
	{
		addressName: "北京市",
		addressId: 110100
	},
	{
		addressName: "上海市",
		addressId: 310100
	},
	{
		addressName: "广州市",
		addressId: 440100
	},
	{
		addressName: "杭州市",
		addressId: 330100
	},
	{
		addressName: "厦门市",
		addressId: 350200
	},
	{
		addressName: "武汉市",
		addressId: 420100
	},
	{
		addressName: "长沙市",
		addressId: 430100
	},
	{
		addressName: "重庆市",
		addressId: 500100
	}
])

const props = defineProps({
	//显示隐藏
	modelValue: {
		type: Boolean,
		default: false
	},
	//选中的城市
	city: {
		type: Object,
		default: () => { }
	},
	cityKey: {
		type: String,
		default: ""
	}
})
const emits = defineEmits(["update:modelValue", "update:city"])

//选择城市
const onChangeCity = city => {
	const cityParams = {
		cityName: city.addressName,
		cityCode: city.addressId
	}
	emits("update:city", cityParams)
	emits("update:modelValue", false)
}

// 获取定位
const curLocatin = ref({
	addressName: "",
	addressId: ""
})
// const locationText = ref('定位中')
const getLocation = async isReset => {
	console.log(process.env.NODE_ENV)
	uni.showLoading({
		title: "定位中"
	})
	setTimeout(() => {
		uni.hideLoading()
	}, 3000)
	if (process.env.NODE_ENV !== "development") {
		// 开发环境
		const params = {
			lat: 22.6,
			lng: 114.02
		}

		const adData = await getAddressByTx(params)
		console.log("adData====")
		const addressInfo = adData.ad_info
		console.log(adData)

		try {
			addressInfo.city_code = addressInfo.city_code.split("156").join("") * 1
		} catch (e) {
			//TODO handle the exception
		}

		curLocatin.value = {
			addressName: addressInfo.city,
			addressId: addressInfo.city_code
		}
		onChangeCity(curLocatin.value)
		// uni.hideLoading()
		if (isReset) {
			uni.showToast({
				title: "已重新定位"
			})
		}
	} else {
		uni.getLocation({
			type: "wgs84",
			success: async res => {
				console.log(res)
				console.log("当前位置的经度：" + res.longitude)
				console.log("当前位置的纬度：" + res.latitude)
				const params = {
					lat: res.latitude,
					lng: res.longitude
				}
				const adData = await getAddressByTx(params)
				uni.hideLoading()
				console.log(adData)
				const addressInfo = adData.ad_info
				try {
					addressInfo.city_code =
						addressInfo.city_code.split("156").join("") * 1
				} catch (e) {
					//TODO handle the exception
				}
				curLocatin.value = {
					addressName: addressInfo.city,
					addressId: addressInfo.city_code
				}
				onChangeCity(curLocatin.value)
				if (isReset) {
					uni.showToast({
						title: "已重新定位"
					})
				}
			},
			fail: function (err) {
				console.log("error:")
				console.log(err)
				uni.hideLoading()
			}
		})
	}
}

// 索引事件
const activeLetter = ref("")
const onChangeIndex = letter => {
	console.log(letter)
	activeLetter.value = letter
}

onBeforeMount(async () => {
	try {
		await getCityList()
		// 获取定位
		await getLocation()
	} catch (e) {
		console.error(e)
		//TODO handle the exception
	}
})
//获取城市
const cityList = ref([])
const getCityList = async () => {
	try {
		const { code, data } = await request.get(`/address/city`)
		// const {code1,data1} =await request.get(`/address/info`)
		console.log("data.value")
		console.log(data.value)
		cityList.value = data.value
	} catch (err) {
		console.info(err)
	}
}

//搜索城市
const resultCity = ref([])
watch(
	() => props.cityKey,
	val => {
		console.log(val)
		console.log(cityList.value)
		let resCity = []
		if (val) {
			const city = cityList.value
			for (let key in city) {
				const list = city[key].data.filter(
					e => e.addressName.indexOf(val) !== -1
				)
				resCity = [...resCity, ...list]
			}
		}
		resultCity.value = resCity
	}
)
</script>
<style lang="scss" scoped>
.location-list {
	// display: none;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 100;
	border-top-left-radius: 60rpx;
	$transition-time: 0.2s;
	transition: all $transition-time;

	&.show {
		height: calc(100vh - 131rpx);
	}

	&.hide {
		height: 0;
	}

	.shortcut {
		padding: 30rpx 40rpx;

		.title {
			display: flex;
			justify-content: space-between;
			margin-top: 10rpx;
			margin-bottom: 20rpx;

			.left {
				font-size: 30rpx;
				font-weight: 500;
				color: #0d0d0d;
			}

			.right {
				font-size: 24rpx;
				font-weight: 400;
				color: #1c78e9;
			}
		}

		.shortcut-box {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -17rpx;

			.city-box {
				flex: none;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 200rpx;
				height: 70rpx;
				margin: 0 17rpx 30rpx;
				font-size: 30rpx;
				color: #0d0d0d;
				background-color: #f4f4f4;
				border-radius: 12rpx;

				.icon {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}

	.city-group {
		padding-right: 20rpx;

		.letter {
			display: flex;
			align-items: center;
			margin: 0 -40rpx;
			padding-left: 40rpx;
			height: 50rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #0d0d0d;
			background-color: #f4f4f4;
		}

		.city-item {
			display: flex;
			align-items: center;
			height: 94rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #040404;

			&:not(:last-child) {
				border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);
			}
		}
	}
}

.indexed-list {
	position: fixed;
	right: 30rpx;
	bottom: 50%;
	transform: translateY(50%);
	z-index: 200;
	line-height: 37rpx;
	font-size: 26rpx;
	font-weight: 500;
	color: #7ed3da;
	transition: 0.2s;
}
</style>
