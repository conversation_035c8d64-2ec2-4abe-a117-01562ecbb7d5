<template>
  <view>
    <!-- 权益票列表 -->
    <view class="ticket" v-for="(n, i) in ticketList" :key="i">
      <view class="ticket-list">
        <view class="item" v-for="(e, k) in n.list" :key="k">
          <view class="title">
            <view class="left">{{ e.goodsName }}</view>
            <view class="right">
              <text class="unit">¥</text>{{ e.storeGoodsPrice
              }}<text class="up">起</text>
            </view>
          </view>
          <view class="time" v-if="e.validityDay">
            <text>{{ `${e.validityDay} 天有效` }}</text>
          </view>

          <view class="tags-row">
            <view class="tag">
              <text v-for="(tag, tagIndex) in e.labels" :key="tagIndex">
                <text v-if="tagIndex !== 0">/</text> {{ tag.name }}
              </text>
            </view>
            <view class="tag nft" v-if="e.isDigit == '1'">NFT</view>
          </view>

          <view class="reserve">
            <view v-if="e.ticketRemark" class="left" @tap="handleBooking(e)">
              <text>预订须知</text>
              <uni-icons type="forward" size="12" color="#1C78E9"></uni-icons>
            </view>
            <view v-else class="left"></view>
            <view
              class="right"
              :class="{ 'disabled': !isTravelCardPurchased }"
              style="position: absolute; right: 30rpx; bottom: 30rpx;"
              @tap="handleSubmit(e)">立即预订
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'

const props = defineProps({
  // 票列表数据
  ticketList: {
    type: Array,
    default: () => []
  },
  isTravelCardPurchased: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits(['submit', 'booking'])

// 预订须知点击事件
const handleBooking = (e) => {
  emit('booking', e)
}

// 立即预订点击事件
const handleSubmit = (e) => {
  emit('submit', e)
}
</script>

<style lang="scss" scoped>
// 单票样式
.ticket {
  background-color: #fff;
  margin-bottom: 20rpx;

  
  .ticket-list {
    .item {
      position: relative;
      margin: 0;
      padding: 34rpx 30rpx;
      background-color: #fff;
      border-radius: 12rpx;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

      &:not(:last-child) {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 30rpx; /* 左侧留白距离 */
          right: 30rpx; /* 右侧留白距离 */
          height: 1px;
          background-color: #E9E9E9;
        }
      }

      > .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        .left {
          margin-right: 8rpx;
          font-size: 32rpx;
          font-weight: 500;
          color: #000000;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .right {
          flex: none;
          font-size: 42rpx;
          font-weight: 600;
          color: #f43636;
          line-height: 42rpx;

          .unit {
            font-size: 28rpx;
          }

          .up {
            font-size: 28rpx;
            color: #6b6b6b;
            font-weight: 400;
          }
        }
      }

      .time {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        font-weight: 400;
        color: #14131F;
        margin-bottom: 12rpx;
        
        text {
          margin-left: 6rpx;
        }
      }

      .tags-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 12rpx;
        
        .tag {
          display: inline-block;
          padding: 4rpx 12rpx;
          font-size: 22rpx;
          font-weight: 400;
          color: #ff9201;
          border: 1rpx solid #ff9201;
          border-radius: 6rpx;
          margin-right: 10rpx;
          background-color: rgba(255, 146, 1, 0.05);
          
          &.nft {
            color: #1C78E9;
            border-color: #1C78E9;
            background-color: rgba(28, 120, 233, 0.05);
          }
        }
      }

      .reserve {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          font-weight: 400;
          color: #1c78e9;
          line-height: 33rpx;

          .icon {
            width: 14rpx;
            margin-left: 8rpx;
          }
        }

        .right {
          display: inline-block;
          padding: 12rpx 30rpx;
          font-size: 26rpx;
          font-weight: 500;
          color: #ffffff;
          line-height: 37rpx;
          letter-spacing: 1px;
          background: #FF9201;
          border-radius: 34rpx;
          box-shadow: 0 4rpx 8rpx rgba(255, 146, 1, 0.3);
        }
      }
    }
  }
}
.disabled {
  background: #dfdfdf !important;
  box-shadow: none !important;
}
</style>
