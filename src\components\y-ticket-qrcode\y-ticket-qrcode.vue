<template>
	<view class="qrcode-box" @click="goTicketDetail">
		<!-- <uqrcode
			ref="qrcodeRef"
			class="qrcode"
			canvas-id="qrcode"
			:value="qrStr"
			:size="466"
			:start="false"
			@complete="onQrcodeComplete"
			sizeUnit="rpx">
			<template v-slot:error="{ error }">
				<text style="color: red">{{ error.errMsg }}</text>
			</template></uqrcode
		> -->
		<qrcode-vue :value="qrStr" :size="233" level="M" />
		<view v-if="ticketStatus.isDisabled" class="lose-effectiveness">
			<image
				v-if="ticketStatus.isRefund"
				class="icon"
				src="@/static/image/refund_qrcode.png"
				mode="widthFix" />
			<image
				v-if="ticketStatus.isExpired"
				class="icon"
				src="@/static/image/expired_qrcode.png"
				mode="widthFix" />
			<image
				v-if="ticketStatus.isUsed"
				class="icon"
				src="@/static/image/used_qrcode.png"
				mode="widthFix" />
		</view>
	</view>
	<view
		class="ticket-number"
		:style="{ opacity: ticketStatus.isDisabled ? 0.57 : 1 }"
		>{{ ticketNumber }}</view
	>
</template>

<script setup>
import { computed } from "vue"

import QrcodeVue from "qrcode.vue"
const props = defineProps({
	qrStr: {
		type: String,
		default: ""
	},
	status: {
		type: Number,
		default: 0
	},
	ticketId: {
		type: String,
		default: ""
	},
	// 有此值，可跳转到详情页
	orderId: {
		type: String,
		default: ""
	}
})

const qrcodeRef = ref()

// watch(
// 	() => props.qrStr,
// 	() => {
// 		if (props.qrStr) {
// 			setTimeout(() => {
// 				qrcodeRef.value.remake()
// 			}, 50)
// 		}
// 	},
// 	{ immediate: true }
// )

// const onQrcodeComplete = e => {
// 	console.log("二维码生成", e ? "成功" : "失败")
// }

const ticketNumber = computed(() => {
	if (props.ticketId.length < 20) return props.ticketId

	// 取前面 10 位和后面 9 位，中间用...代替
	return `${props.ticketId.slice(0, 10)}...${props.ticketId.slice(-9)}`
})
const ticketStatus = computed(() => {
	const isExpired = props.status == "2"
	const isUsed = props.status == "3"
	const isRefund = props.status == "4"

	return {
		isDisabled: isExpired || isUsed || isRefund,
		isExpired,
		isUsed,
		isRefund
	}
})

// 跳转票详情
const goTicketDetail = () => {
	if (!props.orderId || !props.ticketId) return
	Tool.goPage.push(
		`/pages/ETicket/ETicket?ticketNumber=${props.ticketId}&orderId=${props.orderId}`
	)
}
</script>

<style lang="scss" scoped>
.qrcode-box {
	position: relative;
	display: flex;
	justify-content: center;
	// .qrcode{
	// 	width: 466rpx;
	// 	height: 466rpx;
	// }
	.lose-effectiveness {
		background-color: rgb(255, 255, 255, 0.8);
		position: absolute;
		top: 0;
		left: 0;
		width: 233px;
		height: 233px;
		.icon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 200rpx;
			height: 200rpx;
		}
	}
}
.ticket-number {
	margin-top: 30rpx;
	text-align: center;
	color: #050505;
}
</style>
