<template>
	<div
		class="longpress"
		@touchstart="startTouch"
		@touchmove="moveTouch"
		@touchend="endTouch">
		<slot />
	</div>
</template>

<script>
export default {
	props: {
		// 长按事件
		timeout: {
			type: Number,
			default: 1000
		}
	},
	data() {
		return {
			touchStartTime: null,
			touchEndTime: null,
			isLongPress: false,
			isMove: false
		}
	},
	methods: {
		startTouch(event) {
			event.preventDefault() // 阻止默认行为
			this.$emit("startpress")
			// this.touchStartTime = Date.now()
			this.isLongPress = true
			this.isMove = false
			const _this = this
			setTimeout(() => {
				if (_this.isLongPress && !_this.isMove) {
					// 执行长按事件的逻辑
					console.log("长按事件触发")
					_this.$emit("longpress")
				}
			}, this.timeout)
		},
		endTouch(event) {
			event.preventDefault() // 阻止默认行为
			this.$emit("longpressEnd")
			// this.touchEndTime = Date.now()
			// this.checkLongPress()
			// this.isLongPress = false
		},
		moveTouch(event) {
			// 移动了指定距离，就不再是长按事件
			this.isMove = true
		}
	}
}
</script>

<style lang="scss" scoped>
.longpress {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
</style>
