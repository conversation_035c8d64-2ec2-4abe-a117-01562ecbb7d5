<template>
	<view class="travel-note">
		<view class="header">
			<view class="title">攻略游记</view>
			<view class="more" @click="Tool.goPage.push(`/pages/travelNotes/travelNotes`)">更多</view>
		</view>

		<view v-if="isReady">
			<custom-waterfalls-flow :value="articleList" :listStyle="{
				width: '335rpx'
			}">
			</custom-waterfalls-flow>
		</view>
		<view v-if="articleList.length === 0">暂无数据 </view>
	</view>
</template>
<script lang="ts" setup>
import Waterfall from "@/components/custom-waterfalls-flow/custom-waterfalls-flow.vue"
import request from "@/utils/request.js"
import { getRoute } from "@/utils/tool.js"
import dayjs from "dayjs"
import { defineComponent, onMounted, ref } from "vue"
import { getEnv } from "@/utils/getEnv";

defineComponent({
	"custom-waterfalls-flow": Waterfall
})

interface ArticleListItem {
	id: string
	articleName: string
	articleAuthor: string
	publishTime: string
	readCount: number
	publicizeImgUrl: string
	createTime: string
	articleType: number
}
const host = getEnv().VITE_IMG_HOST
const articleList = ref<ArticleListItem[]>([])
const isReady = ref(false)

const fetchArticleData = async () => {
	try {
		const params = {
			articleTypeList: [1, 4],
			storeId: getRoute.params().storeId
		}
		const { data = [] } = await request.get(`/article/h5/home/<USER>
		articleList.value = data.map(i => ({
			...i,
			image: host + (i.publicizeImgUrl || "").split(",")[0],
			createTime: dayjs(i.createTime).format("YYYY.MM.DD")
		}))
		isReady.value = true
	} catch (error) {
		console.log(error)
	}
}

onMounted(() => {
	fetchArticleData()
})
</script>
<style lang="scss" scoped>
.travel-note {
	margin: 40rpx 30rpx;

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title {
			height: 50rpx;
			font-size: 36rpx;
			font-weight: 500;
			color: #14131f;
			line-height: 50rpx;
		}

		.more {
			height: 37rpx;
			font-size: 26rpx;
			color: rgba(20, 19, 31, 0.6);
			line-height: 37rpx;
		}
	}

	.content {
		margin-top: 14rpx;
		display: flex;
		justify-content: space-between;

		.item {
			height: auto;
			width: 335rpx;
			background-color: orange;
		}

		.img {
			width: 100%;
		}
	}
}
</style>
