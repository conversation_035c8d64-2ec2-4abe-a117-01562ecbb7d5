<template>
  <view class="tour_search">
    <view class="search_box">
      <view class="input_box">
        <input v-model="value" :maxlength="10" placeholder="" placeholder-class="input-placeholder" @input="input" />
        <image src="@/static/image/tour/tour_search.svg" mode="aspectFit" />
        <uni-icons class="del_icon" type="closeempty" size="20" @tap="value = ''" v-if="value.trim()"></uni-icons>
      </view>
      <!-- <text @click="Tool.goPage.back()">取消</text> -->
      <view style="margin-left: 16rpx" @click="searchData">搜索</view>
    </view>
    <view>
      <view class="content_box" v-if="historyList.length">
        <view class="title">
          <text>搜索历史</text>
          <image src="@/static/image/tour/tour_del.svg" mode="aspectFit" @click="clearn" />
        </view>
        <view class="list">
          <view v-for="(item, index) in historyList" :key="index" @click="toFilterS(item.searchWord)">{{ item.searchWord }}</view>
        </view>
      </view>
      <view class="content_box">
        <view class="title" v-if="hotList.length > 0">
          <text>热门推荐</text>
        </view>
        <view class="list">
          <view v-for="(item, index) in hotList" :key="index" @click="toFilterS(item.text)">{{ item.text }}</view>
        </view>
      </view>
    </view>
    <!-- <view v-else>
      <view v-if="Object.keys(searchObj).length" class="result_box">
        <view v-for="(item, index) in searchObj" :key="index">
          <view class="title" v-html="tabEnum[index]"></view>
          <view class="item" v-for="(v, k) in item" :key="k" @click="clickItem(index, v)">
            <view v-html="v.pointName.replaceAll(value, `<span style='color: #349FFF;'>${value}</span>`)"></view>
          </view>
        </view>
      </view>
      <view v-else class="empty">暂无结果，换个词试试吧</view>
    </view> -->
  </view>
</template>

<script setup>
import { onMounted, ref, nextTick } from "vue";
import { getRoute } from "@/utils/tool.js";
import request from "@/utils/request.js";

const { guideId } = getRoute.params();
const value = ref("");
const hotList = ref([]);
const historyList = ref([]);
const searchObj = ref({});
const hotAutoWords = ref([]);
let timeId = null;
const tabEnum = {
  1: "景点",
  2: "住宿",
  3: "餐饮",
  4: "购物",
  5: "娱乐",
  6: "卫生间",
  7: "停车场",
  8: "出入口",
  9: "服务点",
  10: "乘车点",
  11: "售票处",
  12: "医务室",
  13: "母婴室",
  14: "其它",
};

const input = (e) => {
  clearTimeout(timeId);
  timeId = setTimeout(() => {
    e.detail.value && search(e.detail.value);
  }, 500);
};
const searchData = async () => {
  if (value.value.trim().length > 0) {
    const res = await request.yltPost("/help-backend/search/history/" + getRoute.params().storeId, { searchWord: value.value });
    if (res.code === 20000) {
      getHistory();
      toFilterS(value.value);
    }
  } else {
    uni.showToast({
      title: `请输入搜索内容`,
      icon: "none",
    });
  }
};
// const vagueSearch = (pointName) => {
//   request.get("/navigation/point/search/list", { pointName, scenicId: guideId }).then(({ data }) => {
//     searchObj.value = data;
//   });
// };
// const clickItem = (pointType, v) => {
//   try {
//     let sum = [];
//     const history = uni.getStorageSync("search_history");
//     if (history) sum = history;
//     const index = sum.indexOf(v.pointName);
//     if (index != -1) sum.splice(index, 1);
//     sum.unshift(v.pointName);
//     uni.setStorageSync("search_history", sum);
//     uni.setStorageSync("pointObj", { pointType, pointId: v.id });
//     console.log({ pointType, pointId: v.id });
//     Tool.goPage.back();
//   } catch (e) {}
// };
const clearn = async () => {
  const res = await request.yltDelete("/help-backend/search/history/" + getRoute.params().storeId);
  if (res.code === 20000) {
    getHistory();
  }
};
const toFilterS = (value) => {
  Tool.goPage.push(`/pages/gloabSearch/filterSearch?searchValue=${value}`);
};
const getHistory = () => {
  request.yltGet("/help-backend/search/history/" + getRoute.params().storeId).then(({ data }) => {
    historyList.value = data;
  });
};

onMounted(() => {
  getHistory();
  const hotWordList = sessionStorage.getItem("hotWordList");
  const hotWordType = sessionStorage.getItem("hotWordType");
  if (hotWordList) {
    const list = JSON.parse(hotWordList);
    const newHotList = list.filter((item) => {
      if (item.text) {
        return item;
      }
    });
    if (hotWordType == "manual") {
      hotList.value = [...newHotList];
    } else {
      request.yltGet(`/help-backend/search/history/${getRoute.params().storeId}/hotWords`).then((res) => {
        if (res.code === 20000) {
          hotAutoWords.value = res.data.map((item) => {
            return {
              text: item,
            };
          });
          hotList.value = [...hotAutoWords.value, ...newHotList];
        }
      });
    }
  }
});
</script>

<style lang="scss" scoped>
.tour_search {
  min-height: 100vh;
  background: #fff;
  .search_box {
    width: 750rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
    padding: 0 30rpx;
    .input_box {
      flex: 1;
      height: 60rpx;
      background: #f0f0f0;
      border-radius: 30rpx;
      position: relative;
      > input {
        width: 100%;
        height: 100%;
        padding: 0 20rpx 0 77rpx;
        font-size: 28rpx;
        &.input-placeholder {
          color: #999;
        }
      }
      > image {
        position: absolute;
        top: 50%;
        left: 20rpx;
        transform: translateY(-50%);
        width: 50rpx;
        height: 50rpx;
      }
      .del_icon {
        position: absolute;
        right: 18rpx;
        top: 10rpx;
      }
    }
    > text {
      margin-left: 35rpx;
      font-size: 28rpx;
      color: #000;
      line-height: 40rpx;
      white-space: nowrap;
    }
  }
  .content_box {
    margin: 30rpx;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > text {
        font-size: 30rpx;
        font-weight: bold;
        color: #0d0d0d;
        line-height: 38rpx;
      }
      > image {
        width: 27rpx;
        height: 27rpx;
      }
    }
    .list {
      margin: 20rpx 0 40rpx;
      display: flex;
      flex-wrap: wrap;
      gap: 30rpx;
      > view {
        width: 210rpx;
        height: 70rpx;
        background: #f4f4f4;
        border-radius: 4rpx;
        text-align: center;
        line-height: 70rpx;
        font-size: 30rpx;
        color: #0d0d0d;
        padding: 0 15rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .result_box {
    .title {
      margin: 30rpx 30rpx 0;
      font-size: 30rpx;
      font-weight: bold;
      color: #0d0d0d;
      line-height: 38rpx;
    }
    .item {
      margin: auto;
      width: 690rpx;
      height: 94rpx;
      border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #040404;
      line-height: 94rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .empty {
    margin: 250rpx auto;
    font-size: 28rpx;
    color: #14131f;
    text-align: center;
  }
}
</style>
