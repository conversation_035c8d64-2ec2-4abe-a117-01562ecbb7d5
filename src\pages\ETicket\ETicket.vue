<template>
	<y-nav-bar :backgroundColor="'#61B4FC'" backBtn solid>电子票</y-nav-bar>
	<view class="e-ticket">
		<view class="ticket-box">
			<view class="ticket-body">
				<view class="info">
					<view class="ticket-title">{{ ticketInfo.name }} {{ ticketInfo.productName }}</view>
					<view class="ticket-item">
						<text class="key"> 入园日期： </text>
						{{ ticketInfo.day }}&nbsp;{{ ticketInfo.validityDay }}
					</view>
					<view class="ticket-item">
						<text class="key"> 入园方式： </text>
						{{ ticketInfo?.identityTypeList }}
					</view>
					<view class="ticket-item">
						<text class="key"> 票状态： </text>
						{{ ticketStatus[ticketInfo?.ticketStatus] }}
					</view>
					<view class="ticket-item">
						<text class="key"> 票号： </text>
						{{
							`${routerParams.ticketNumber.length > 20
								? routerParams.ticketNumber.slice(0, 10) +
								"..." +
								routerParams.ticketNumber.slice(-10)
								: routerParams.ticketNumber
							}`
						}}
						<y-svg style="width: 30rpx; height: 30rpx" name="copy-icon" @click="copyText(routerParams.ticketNumber)" />
					</view>
					<view class="ticket-item">
						<text class="key"> 可入园天数： </text>
						{{ ticketInfo.availableDays }}
					</view>
					<view class="ticket-item">
						<text class="key"> 是否首日激活： </text>
						{{ ticketInfo.isActivate }}
					</view>
					<view class="ticket-item">
						<text class="key"> 使用方式： </text>
						{{ ticketInfo.useCountText }}
					</view>
					<view class="ticket-item">
						<text class="key"> 人数： </text>
						{{ ticketInfo.playerNum }}人
					</view>
					<view class="ticket-item">
						<text class="key"> 已核销次数： </text>
						{{ ticketInfo.usedCount }}
					</view>
				</view>
				<view class="line-box">
					<!-- <image
						class="line"
						src="@/static/image/order/e-ticket-line.png"
						mode=""></image> -->
				</view>

				<view class="qr-code">
					<view v-if="checkImg.isQrcode" class="code">
						<y-ticket-qrcode :qr-str="printStr" :status="ticketInfo.ticketStatus" />
					</view>
					<view v-else-if="checkImg.isIdCard">
						<image src="@/static/image/identity-tip.png" mode="widthFix" />
						<view style="
								color: rgba(0, 0, 0, 0.8);
								font-size: 28rpx;
								margin-top: 20rpx;
							">需要携带身份证入园</view>
					</view>
					<view v-else>
						<image style="width: 346rpx; height: 346rpx" src="@/static/image/face-tip.png" mode="widthFix" />
					</view>
				</view>
			</view>
		</view>
		<view class="ticket-box-bg"></view>
		<view class="ticket-box-bg1"></view>
		<view v-if="ticketInfo.isChainTicket == 1" class="chain-record-btn" @click="popup.open()">
			<img src="@/static/image/order/chainRecord.png" />
			<view>门票溯源</view>
		</view>
		<uni-popup ref="popup" type="bottom">
			<view class="chain-record-pop">
				<view class="pop-title">
					<uni-icons type="left" size="30rpx" @click="popup.close()"></uni-icons>
					<view class="title">门票溯源</view>
					<view class="right">共计{{ popupList.length }}条记录</view>
				</view>
				<view class="pop-content">
					<view class="tips"></view>
					<view class="content">
						<view v-for="(item, index) in popupList" :key="item.txHash" class="item">
							<view class="info-time">
								<view class="info-time-dot">
									<view></view>
								</view>
								<view class="info-time-content">{{
									dayjs(item.triggerAt * 1000).format("YYYY-MM-DD HH:mm:ss")
								}}</view>
							</view>
							<view class="info-type">{{ chainRecordType[item.optType] }}</view>
							<view class="info-list">
								<view class="info-list-item" v-if="[1].includes(item.optType)">
									<view class="info-list-item-table">铸造方：</view>
									<view class="info-list-item-content">{{
										item.minerName
									}}</view>
								</view>
								<view class="info-list-item" v-if="[3, 4, 6, 7].includes(item.optType)">
									<view class="info-list-item-table">发送方：</view>
									<view class="info-list-item-content">{{
										item.senderName
									}}</view>
								</view>
								<view class="info-list-item" v-if="[3, 4, 6, 7].includes(item.optType)">
									<view class="info-list-item-table">接受方：</view>
									<view class="info-list-item-content">{{
										item.receiverName
									}}</view>
								</view>
								<view class="info-list-item" v-if="[1, 3, 4, 5, 6, 7, 8].includes(item.optType)">
									<view class="info-list-item-table">门票状态：</view>
									<view class="info-list-item-content">{{
										ticketStatus[item.ticketsStatus]
									}}</view>
								</view>
								<view class="info-list-item">
									<view class="info-list-item-table">交易哈希：</view>
									<view class="info-list-item-content" style="color: #1890ff" @click="copyText(item.txHash)">{{
										item.txHash.length > 20
											? item.txHash.slice(0, 10) +
											"..." +
											item.txHash.slice(-10)
											: item.txHash
									}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script setup>
import { computed, reactive, ref, onMounted } from "vue"
import request from "@/utils/request.js"
import { onLoad } from "@dcloudio/uni-app"
import { ticketStatus, ticketType, goodsType } from "@/utils/constant.js"
import dayjs from "dayjs"
import { copyText } from "@/utils/tool.js"
name: "e-ticket"
const props = defineProps({
	tabList: {
		type: Array,
		default: () => []
	}
})
const popup = ref(null)
const popupList = ref([])
const chainRecordType = {
	1: "门票铸造",
	2: "价格策略设置",
	3: "门票采购",
	4: "门票购买",
	5: "门票核销",
	6: "门票退订",
	7: "门票采购退单",
	8: "门票过期"
}
const routerParams = reactive({})

const qrCodeWidth = uni.upx2px(466)

onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
})

const ticketInfo = ref({
	name: "",
	productName: "",
	isChainTicket: 0,
	identityTypeList: "",
	day: "",
	ticketStatus: "",
	validityDay: ""
})
const printStr = ref("")

const checkImg = computed(() => {
	return {
		isQrcode: ticketInfo.value?.identityTypeList.indexOf("票") > -1,
		isFace: ticketInfo.value?.identityTypeList.indexOf("人脸") > -1,
		isIdCard: ticketInfo.value?.identityTypeList.indexOf("身份证") > -1
	}
})

onMounted(async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { data } = await request.get(
			`/order/GroupTicketOrder?orderId=${routerParams.orderId}`
		)
		// 是否组合票
		const isCompose = data.orderInfo[0].orderProductTicket.some(
			item => item.ticketType === 6
		)

		let orderInfo = {}
		let ticketData = {}
		if (isCompose) {
			//组合票
			let order = []
			data.orderInfo.forEach(e => order.push(...e.orderProductTicket))

			order.map(n => {
				if (n.ticketType != 6) {
					//非 6 是组合票里的单票信息
					n.orderTicketInfoList.map(e => {
						if (e.ticketNumber == routerParams.ticketNumber) {
							orderInfo = n
							printStr.value = e.printStr
							ticketData = e
						}
					})
				}
			})
		} else {
			//单票
			orderInfo = data.orderInfo[0].orderProductTicket[0]
			ticketData = orderInfo.orderTicketInfoList.find(
				e => e.ticketNumber == routerParams.ticketNumber
			)
			printStr.value = ticketData.printStr
		}
		// 获取检票规则
		const identityTypeList = await getCheckRule(ticketData.checkId)
		ticketInfo.value = {
			day: orderInfo.day,
			name: orderInfo.scenicName,
			productName: `${orderInfo.productSkuName} - ${ticketType[orderInfo.productType]
				}（${goodsType[orderInfo.ticketType]}）`,
			isChainTicket: ticketData.isChainTicket,
			identityTypeList,
			availableDays: ticketData.availableDays + "天",
			isActivate: ticketData.isActivate == 1 ? "是" : "否",
			useCountText:
				ticketData.useType == 1 ? "一共" : "每天" + ticketData.useCount + "次",
			usedCount: ticketData.usedCount + "次",
			playerNum: ticketData.playerNum,

			ticketStatus: ticketData.ticketStatus,
			validityDay:
				orderInfo.timeShareEndTime == "" || orderInfo.timeShareEndTime == null
					? `(${orderInfo.validityDay}天有效）`
					: `${orderInfo.timeShareBeginTime} - ${orderInfo.timeShareEndTime}`
		}

		if (ticketData.isChainTicket == 1) {
			const { data: chainData } = await request.get(
				`/blockChain/ticket/traceRecord?ticketId=${routerParams.ticketNumber}`
			)
			popupList.value = chainData.filter(item => item.optType != 2).reverse()
		}
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
})

// 获取检票规则
const getCheckRule = async id => {
	try {
		const { code, data } = await request.get(`/ticketCheck/info`, { id: id })
		return data.identityTypeList
			? data.identityTypeList.map(item => item).join("、")
			: "-"
	} catch (err) {
		console.log(err)
	}
	return null
}
</script>
<style lang="scss" scoped>
.e-ticket {
	min-height: 100%;
	padding-bottom: 20rpx;
	background: linear-gradient(180deg,
			var(--linear-gradient0) 0%,
			var(--linear-gradient1) 100%);

	.ticket-box {
		margin: 0 auto;
		padding-top: 30rpx;
		width: 673rpx;

		.sceinc-title {
			display: flex;
			align-items: center;
			padding: 35rpx;
			// height: 126rpx;
			color: #fff;
			line-height: 54rpx;
			background: #40a0f3;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			font-size: 40rpx;
			font-weight: 600;
		}

		.ticket-body {
			display: flex;
			flex-direction: column;

			>.info {
				border-radius: 24rpx 24rpx 0rpx 0rpx;
				padding: 38rpx 35rpx;
				background: radial-gradient(circle at top left, #fff 10px, #fff 0) top left,
					radial-gradient(circle at top right, #fff 10px, #fff 0) top right,
					radial-gradient(circle at bottom right, transparent 10px, #fff 0) bottom right,
					radial-gradient(circle at bottom left, transparent 10px, #fff 0) bottom left;
				background-size: 51% 51%;
				background-repeat: no-repeat;

				.ticket-title {
					margin-bottom: 25rpx;
					font-size: 34rpx;
					font-weight: 600;
					color: #050505;
					line-height: 50rpx;
					letter-spacing: 2rpx;
				}

				.ticket-item {
					display: flex;
					margin-bottom: 14rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #050505;
					line-height: 34rpx;
					letter-spacing: 1rpx;
					opacity: 0.8;

					.key {
						flex: none;
						font-family: none;
					}
				}
			}

			>.line-box {
				display: flex;
				height: 3rpx;
				margin: 0 10px;
				background-color: #fff;
				border: 1px dashed #eee;

				>.line {
					margin: 0 15rpx;
					height: 3rpx;
					width: 100%;
				}
			}

			>.qr-code {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding-top: 80rpx;
				padding-bottom: 80rpx;
				margin-top: -1rpx;
				text-align: center;
				border-radius: 0rpx 0rpx 24rpx 24rpx;
				background: radial-gradient(circle at top left,
						transparent 10px,
						#fff 0) top left,
					radial-gradient(circle at top right, transparent 10px, #fff 0) top right,
					radial-gradient(circle at bottom right, #fff 10px, #fff 0) bottom right,
					radial-gradient(circle at bottom left, #fff 10px, #fff 0) bottom left;
				background-size: 51% 51%;
				background-repeat: no-repeat;

				.code {
					// width: 466rpx;
					// height: 466rpx;
				}

				.name {
					margin: 30rpx 0 50rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #050505;
					line-height: 34rpx;
				}
			}
		}
	}

	.ticket-box-bg {
		width: 617rpx;
		height: 231rpx;
		margin: -200rpx auto 0;
		background: rgba(255, 255, 255, 0.6);
		// background-color: red;
		border-radius: 24rpx;
		z-index: 2;
	}

	.ticket-box-bg1 {
		width: 555rpx;
		height: 198rpx;
		margin: -160rpx auto 0;
		background: rgba(255, 255, 255, 0.32);
		// background-color: blue;
		border-radius: 24rpx;
		z-index: 1;
	}

	.chain-record-btn {
		margin: 70rpx auto;
		width: 350rpx;
		height: 88rpx;
		background: #349fff;
		border-radius: 3rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20rpx;
		font-weight: bold;
		font-size: 36rpx;
		color: #fff;

		>img {
			margin-bottom: -5rpx;
			height: 45rpx;
		}
	}

	.chain-record-pop {
		width: 750rpx;
		height: 1248rpx;
		max-height: 80vh;
		background: #fff;
		border-radius: 36rpx 36rpx 0 0;
		padding: 0 30rpx;
		display: flex;
		flex-direction: column;

		.pop-title {
			width: 100%;
			height: 96rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: relative;
			border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);

			.title {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-weight: bold;
				font-size: 36rpx;
				color: #000;
			}

			.right {
				font-size: 33rpx;
				color: #349fff;
			}
		}

		.pop-content {
			position: relative;
			width: 100%;
			height: 0;
			flex: 1;

			.tips {
				position: absolute;
				top: 0;
				left: 16rpx;
				width: 2rpx;
				height: 100%;
				background: #349fff;
				opacity: 0.3;
			}

			.content {
				height: 100%;
				overflow: scroll;
				padding: 0 0 55rpx 74rpx;

				.item {
					margin-top: 55rpx;

					.info-time {
						position: relative;
						font-size: 28rpx;

						.info-time-dot {
							position: absolute;
							top: 50%;
							left: -74rpx;
							transform: translateY(-50%);
							width: 34rpx;
							height: 34rpx;
							background: #fff;
							border: 2rpx solid #349fff;
							display: flex;
							justify-content: center;
							align-items: center;
							border-radius: 50%;

							>view {
								width: 18rpx;
								height: 18rpx;
								background: #349fff;
								border-radius: 50%;
							}
						}
					}

					.info-type {
						margin-top: 20rpx;
						font-weight: bold;
						font-size: 30rpx;
						color: #14131f;
					}

					.info-list {
						.info-list-item {
							margin-top: 16rpx;
							display: flex;
							font-size: 28rpx;

							.info-list-item-content {
								color: rgba(5, 5, 5, 0.5);
							}
						}
					}
				}
			}
		}
	}
}
</style>
