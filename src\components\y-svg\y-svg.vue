<template>
  <svg aria-hidden="true">
    <use :xlink:href="'#' + name"></use>
  </svg>
</template>

<script>
export default {
  name: "y-svg",
  props: {
    name: String,
  },
  created() {
    const getEle = (id) => document.getElementById(id);
    const setSvg = () => {
      document.body.insertAdjacentHTML(
        "beforeend",
        `<svg
          id="svg"
          aria-hidden="true"
          style="position: absolute; width: 0px; height: 0px; overflow: hidden"
        ></svg>`
      );
      return getEle("svg");
    };
    const svg = getEle("svg") || setSvg();
    if (!getEle(this.name)) {
      fetch(`static/svg/${this.name}.svg`).then((res) => {
        res.text().then((data) => {
          // 单色图标
          // data = data.replace(/fill="#\w+"/g, 'fill="currentColor"');
          // // 多色图标
          // data = data.replace(/fill="#\w+"/g, (match) => {
          //   // 获取匹配到的颜色值
          //   const colorValue = match.match(/#(\w+)/)[1]
          //   const opacityValue = parseInt(colorValue.slice(-2), 16) / 255
          //   return `fill="currentColor" opacity="${opacityValue}"`
          // })
          // 自动注入
          svg.insertAdjacentHTML(
            "beforeend",
            `<symbol id="${this.name}" ${data.slice(
              data.indexOf("<svg") + 4,
              data.indexOf("</svg>")
            )} </symbol>`
          );
        });
      });
    }
  },
};
</script>
