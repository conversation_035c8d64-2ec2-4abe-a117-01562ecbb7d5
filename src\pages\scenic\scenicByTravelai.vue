<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>{{
		routerParams.scenicId ? state.scenicInfo.name : (state.composeGoodsInfo.name || '')
		}}</y-nav-bar>
	<!-- <scenicSwiper class="scenic-banner" :list="swiperList" /> -->
	<scenicMap class="scenic-banner" :style="{ height: 'calc(100vh - 700rpx)' }" :guideId="routerParams.scenicId" />
	<view style="position: relative">
		<view class="sceinc" :style="{ marginTop: 'calc(100vh - 700rpx)' }">
			<view class="sceinc-info">
				<view class="sceinc-info-bar"></view>
				<!-- 单票信息 -->
				<template v-if="routerParams.scenicId">
					<view class="scenic-detail">
						<image class="scenic-img" :src="bgImg" mode="aspectFill" />
						<view class="scenic-content">
							<view class="scenic-name">{{ state.scenicInfo.name }}</view>
							<view class="sceinc-info__rating" @click="goToComments">
								<template v-if="comments.allNumber > 0">
									<view class="rating-score">{{ comments.scoreAll }}<text class="rating-unit">分</text>
									</view>
									<view class="rating-count">{{ comments.allNumber }}条点评
										<view class="triangle-right"></view>
									</view>
								</template>
								<template v-else>
									<view class="rating-count">暂无点评</view>
								</template>
							</view>
							<view class="scenic-item" v-if="state.scenicInfo.distance">
								<text class="label">距离：</text>
								<text class="value">{{ state.scenicInfo.distance }}km</text>
							</view>
							<view class="scenic-item" v-if="state.scenicInfo.businessStartTime">
								<text class="label">营业时间：</text>
								<text class="value">{{ state.scenicInfo.businessStartTime }} - {{
									state.scenicInfo.businessEndTime }}</text>
							</view>
							<!-- <view class="scenic-item" v-if="state.scenicInfo.contractsPhone">
								<text class="label">电话：</text>
								<text class="value">{{ state.scenicInfo.contractsPhone }}</text>
							</view> -->
							<view class="scenic-item">
								<text class="label">地址：</text>
								<text class="value">{{ state.scenicInfo.provinceName || '' }}{{
									state.scenicInfo.cityName || '' }}{{ state.scenicInfo.areaName || '' }}{{
									state.scenicInfo.address || '' }}</text>
							</view>
						</view>
					</view>
				</template>
				<!-- 组合票信息 -->
				<template v-else>
					<view class="scenic-detail">
						<image class="scenic-img" :src="bgImg" mode="aspectFill" />
						<view class="scenic-content">
							<view class="scenic-name">{{ state.composeGoodsInfo.name }}</view>
							<view class="tags-row"
								v-if="state.composeGoodsInfo.labelList && state.composeGoodsInfo.labelList.length">
								<view class="tag" v-for="(tag, tagIndex) in state.composeGoodsInfo.labelList"
									:key="tagIndex">
									{{ tag.name }}
								</view>
							</view>
							<view class="price-row">
								<view class="price">
									<text class="unit">¥</text>{{ state.composeGoodsInfo.totalPrice }}<text
										class="up">起</text>
								</view>
								<view class="buy-btn" @tap="handleSubmitClick(state.composeGoodsInfo)">立即预订</view>
							</view>
							<view class="action-row">
								<view class="collect-btn" @tap="onCheckCollect">
									<uni-icons :type="isCollect ? 'star-filled' : 'star'" size="18"
										:color="isCollect ? '#FF9201' : '#999'"></uni-icons>
									<text :class="['collect-text', isCollect ? 'active' : '']">{{ isCollect ? '已收藏' :
										'收藏' }}</text>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
			<view style="background: #f1f1f1; min-height: 100vh; padding-top: 20rpx;">
				<template v-if="routerParams.scenicId">
					<view class="tab-container" id="tab-container" :class="{ 'fixed': isFixed }"
						:style="{top: isFixed ? '0px' : 'auto'}">
						<view class="tab-list">
							<view class="tab-item">
								<text :class="['tab-text', activeTab === 'ticket' ? 'tab-active' : 'tab-inactive']"
									@click="switchTab('ticket')">门票预订</text>
								<view v-if="activeTab === 'ticket'" class="tab-indicator"></view>
							</view>
							<view class="tab-item" style="margin-left: 40rpx;">
								<text :class="['tab-text', activeTab === 'comment' ? 'tab-active' : 'tab-inactive']"
									@click="switchTab('comment')">用户点评</text>
								<view v-if="activeTab === 'comment'" class="tab-indicator"></view>
							</view>
						</view>
					</view>
					<view v-if="isFixed" :style="{ height: tabHeight + 'px' }"></view>
					<!-- 使用票列表组件 -->
					<l-ticket-list id="ticket-content" :ticketList="state.ticketList" :scenicId="routerParams.scenicId"
						:composeGoodsInfo="state.composeGoodsInfo" :showPopUp="showPopUp"
						:ticketRemark="state.ticketRemark" @booking="booking" @submit="handleSubmitClick"
						@popup-close="showPopUp = false"></l-ticket-list>
					<view id="comment-content">
						<l-comment-list :scenicName="state.scenicInfo.name"
						:scenicId="routerParams.scenicId"></l-comment-list>
					</view>
				</template>
				<template v-else>
					<!-- 使用票列表组件 -->
					<l-ticket-list :ticketList="state.ticketList" :scenicId="routerParams.scenicId"
						:composeGoodsInfo="state.composeGoodsInfo" :showPopUp="showPopUp"
						:ticketRemark="state.ticketRemark" @booking="booking" @submit="handleSubmitClick"
						@popup-close="showPopUp = false"></l-ticket-list>
				</template>
			</view>
		</view>
	</view>
	<view class="cloud-view" @click="goCloudView">
		<image class="cloud-img" src="@/static/image/ai/yellow-cloud.webp" mode="aspectFill" />
		云游景区
	</view>
	<y-chat showTour :sceincName="state.scenicInfo.name" :scenicId="routerParams.scenicId" />
</template>
<script setup>
import { toRefs, reactive, ref, watch, onBeforeMount, nextTick } from "vue"
import scenicSwiper from "./component/scenicSwiper.vue"
import scenicMap from "./component/scenicMap.vue"
import lTicketList from "./component/l-ticket-list.vue"
import lCommentList from "./component/l-comment-list.vue"
import request from "@/utils/request.js"
import {

	formatTime,
	objToUrlPath,
	getRoute,
	setJWeixin,
	markdownToHtml
} from "@/utils/tool.js"
import { goodsType, ticketType } from "@/utils/constant.js"
import { onLoad, onPageScroll, onReady } from "@dcloudio/uni-app"
import { getEnv } from "@/utils/getEnv";

import { scenicGrade } from "@/utils/constant.js"
const props = defineProps({
	tabList: {
		type: Array,
		default: () => []
	}
})
const imgHost = ref(getEnv().VITE_IMG_HOST)
const swiperList = ref("")
const bgImg = ref("")
const sourceType = ref(0)
const comments = ref({})
setJWeixin()
//轮播图
const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
	sourceType.value = Tool.getSystemSource()
})
//跳转第三方导航
const nav = () => {
	const {
		provinceName,
		cityName,
		areaName,
		address,
		latitude,
		longitude,
		pointName
	} = state.scenicInfo
	const addressName = provinceName + cityName + areaName + address
	jWeixin.openLocation({
		latitude: Number(latitude),
		longitude: Number(longitude),
		name: pointName,
		address: addressName
	})
}
const showPopUp = ref(false)
//预订须知
const booking = async e => {
	state.ticketRemark = markdownToHtml(e.ticketRemark)
	// if (routerParams.scenicId) {
	// 	state.ticketRemark = markdownToHtml(e.ticketRemark)
	// } else {
	// 	const { code, data } = await request.get(`/simpleGoods/info/${e.goodsId}`)
	// 	state.ticketRemark = markdownToHtml(data.notice)
	// }
	showPopUp.value = !showPopUp.value
}
const state = reactive({
	composeGoodsInfo: {},
	scenicInfo: {},
	ticketList: [],
	ticketRemark: "" //须知
})
let userData = {}
onBeforeMount(async () => {
	userData = await Tool.getUserInfo()
	if (routerParams.scenicId) {
		//单票
		await getTicket()
		getScenicRating()
	} else if (routerParams.storeGoodsId) {
		//组合票
		await getGroup()
	}
	//获取收藏状态
	getCollectStatus()
})

// 在数据加载完后更新元素位置
watch(
	() => state.ticketList,
	() => {
		// 当票列表数据变化时，更新内容位置
		nextTick(() => {
			setTabPosition()
		})
	},
	{
		deep: true,
		immediate: true
	}
)

// 在页面准备完成后获取各元素位置
onReady(() => {
	setTabPosition()
	
	// 300ms 后再次获取位置，确保所有内容都已渲染
	setTimeout(() => {
		setTabPosition()
	}, 300)
})
//立即预定
const handleSubmitClick = async item => {
	if (routerParams.scenicId) {
		// 单票
		const urlQuery = {
			storeGoodsId: item.storeGoodsId,
			orderType: "single"
		}
		if (item.timeShareId) urlQuery.timeShareId = item.timeShareId
		const bookPath = `/pages/book/book?${objToUrlPath(urlQuery)}`
		Tool.goPage.push(bookPath)
		// if (await needCheck(item)) {
		// 	// 需要审核
		// 	if (await isApproval(item)) {
		// 		//审批通过

		// 	}
		// } else {
		// 	Tool.goPage.push(bookPath)
		// }
	} else {
		//组合票
		const urlQuery = {
			storeGoodsId: routerParams.storeGoodsId,
			orderType: "compose"
		}
		Tool.goPage.push(`/pages/book/book?${objToUrlPath(urlQuery)}`)
	}
}

//获取标签列表
const getLabels = async arr => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.post(`/ticketStore/goodsLabel`, arr)
		return data
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//获取组合票
const getGroup = async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.get(
			`/appTicket/composeGoodsDetail/${routerParams.storeGoodsId}`
		)
		//添加标签
		const labelArr = await getLabels([data.composeGoodsInfo.id])
		let labels
		if (labelArr.length > 0) {
			labels = [
				{
					name: "组合票"
				},
				...(labelArr[0] && labelArr[0].label ? labelArr[0].label : [])
			]
		} else {
			labels = [
				{
					name: "组合票"
				}
			]
		}
		if (data.composeGoodsInfo.isRealName == 1) {
			labels.unshift({
				name: "实名制"
			})
		}
		data.composeGoodsInfo.labelList = labels
		// 图片添加前缀
		bgImg.value = data.composeGoodsInfo.picUrl
			? imgHost.value + data.composeGoodsInfo.picUrl
			: "https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png"
		console.log(data.composeGoodsInfo.picUrl)
		//组合票信息
		data.composeGoodsInfo.note = markdownToHtml(data.composeGoodsInfo.note)
		state.composeGoodsInfo = data.composeGoodsInfo
		//组合票里的单票信息
		const ticketList = []
		data.goodsDetail.forEach(item => {
			//重组数据，相同景区的归到同个 list
			let hasScenic = false
			hasScenic =
				ticketList.length > 0 &&
				ticketList.every(e => {
					if (item.scenicId === e.scenicId) {
						e.list.push(item)
						return true
					} else {
						return false
					}
				})
			if (!hasScenic) {
				ticketList.push({
					scenicName: item.scenicName,
					scenicId: item.scenicId,
					list: [item]
				})
			}
		})
		state.ticketList = ticketList
		// 轮播图
		swiperList.value = data.picUrl && data.picUrl.split(",")[0]
		// 添加预订须知
		state.ticketList.forEach(item => {
			item?.list?.forEach(e => {
				request.get(`/simpleGoods/info/${e.goodsId}`).then(({ code, data }) => {
					e.ticketRemark = data.notice
				})
			})
		})
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//获取单票
const getTicket = async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		let params = {
			day: formatTime(Date.now(), "Y-M-D h:m:s"),
			scenicId: routerParams.scenicId,
			storeId: getRoute.params().storeId,
			isRecommend: !!routerParams.isRecommend //是否推荐
		}
		const { data } = await request.get(`/appScenic/scenicInfo`, params)
		state.scenicInfo = data
		swiperList.value =
			state.scenicInfo.picture && state.scenicInfo.picture.split(",")[0]
		bgImg.value = state.scenicInfo.picture
			? imgHost.value + state.scenicInfo.picture.split(",")[0]
			: "https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png"
		const toPush = n => {
			n.labels.unshift({
				name: goodsType[n.goodsType]
			})
			if (n.isRealName == 1) {
				n.labels.unshift({
					name: "实名制"
				})
			}
			return n
		}
		data.tickList?.map(n => {
			let index = state.ticketList.map(e => e.ticketId).indexOf(n.ticketId)
			if (index >= 0) {
				state.ticketList[index].list.push(toPush(n))
			} else {
				state.ticketList.push({
					pcName: n.pcName,
					ticketId: n.ticketId,
					list: [toPush(n)]
				})
			}
		})
		uni.hideLoading()
	} catch (err) {
		console.log(err)
	}
}
//拨打电话
const dial = () => {
	uni.makePhoneCall({
		phoneNumber: state.scenicInfo.contractsPhone
	})
}

//收藏
const isCollect = ref(false)
const onCheckCollect = async () => {
	const params = {
		actionType: !isCollect.value ? 1 : 2, //类型：1 - 收藏，2 - 取消收藏
		info: {
			relationId: "", //relationType: 对应的 id- 景区 id、组合商品 id、权益卡 id
			relationType: "", //类型 1 - 景点，2 - 组合商品 3 - 权益卡
			storeId: getRoute.params().storeId,
			userId: userData.userInfo.userId
		}
	}
	if (routerParams.scenicId) {
		//景区
		params.info.relationId = routerParams.scenicId
		params.info.relationType = 1
	} else if (routerParams.storeGoodsId) {
		//组合票
		params.info.relationId = routerParams.storeGoodsId
		params.info.relationType = 2
	}
	const { code, data } = await request.post(`/my/favorites`, params)
	uni.showToast({
		icon: "none",
		title: `${!isCollect.value ? "收藏成功" : "取消收藏"}`
	})
	isCollect.value = !isCollect.value
}
const getCollectStatus = async () => {
	const params = {
		relationId: "",
		relationType: "",
		storeId: getRoute.params().storeId,
		userId: userData.userInfo.userId
	}
	if (routerParams.scenicId) {
		//景区
		params.relationId = routerParams.scenicId
		params.relationType = 1
	} else if (routerParams.storeGoodsId) {
		//组合票
		params.relationId = routerParams.storeGoodsId
		params.relationType = 2
	}
	const { data } = await request.get(`/my/confirmFavorites`, params)
	isCollect.value = data
}

// 获取景区评分概览
const getScenicRating = async () => {
	const { data } = await request.post('/comment/statisticsScore', {
		scenicId: routerParams.scenicId,
		storeId: routerParams.storeId
	})
	comments.value = {
		scoreAll: data.scoreAll.toFixed(1),
		allNumber: data.allNumber
	}
}

// 跳转到评论页面
const goToComments = () => {
	Tool.goPage.push(`/pages/scenic/commentList?scenicId=${routerParams.scenicId}&scenicName=${state.scenicInfo.name}`)
}

const activeTab = ref('ticket')
const isFixed = ref(false)
const tabHeight = ref(0)
const tabOffsetTop = ref(0)
const ticketContentTop = ref(0)
const commentContentTop = ref(0)
let scrollTop = 0

const setTabPosition = () => {
	setTimeout(() => {
		const query = uni.createSelectorQuery()
		query.select('#tab-container').boundingClientRect(data => {
			if (data) {
				tabHeight.value = data.height
				tabOffsetTop.value = data.top + scrollTop
			}
		}).exec()
		
		// 获取内容区域位置
		query.select('#ticket-content').boundingClientRect(data => {
			if (data) {
				ticketContentTop.value = data.top + scrollTop
			}
		}).exec()

		query.select('#comment-content').boundingClientRect(data => {
			if (data) {
				commentContentTop.value = data.top + scrollTop
			}
		}).exec()
	}, 500)
}

onPageScroll(e => {
	scrollTop = e.scrollTop
	isFixed.value = e.scrollTop >= tabOffsetTop.value
	
	// 根据滚动位置动态更新当前 tab
	if (e.scrollTop >= commentContentTop.value - tabHeight.value - 10) {
		activeTab.value = 'comment'
	} else if (e.scrollTop >= ticketContentTop.value - tabHeight.value - 10) {
		activeTab.value = 'ticket'
	}
})

const switchTab = (tab) => {
	if(activeTab.value === tab) return
	activeTab.value = tab;
	
	// 先确保 tab 吸顶
	if (!isFixed.value) {
		uni.pageScrollTo({
			scrollTop: tabOffsetTop.value,
			duration: 100,
		});
	}
	
	// 滚动到对应内容区域
	setTimeout(() => {
		let targetPosition = 0;
		if (tab === 'ticket') {
			targetPosition = ticketContentTop.value - tabHeight.value;
		} else if (tab === 'comment') {
			targetPosition = commentContentTop.value - tabHeight.value;
		}
		
		uni.pageScrollTo({
			scrollTop: targetPosition,
			duration: 300,
		});
	}, isFixed.value ? 0 : 150);
}

const goCloudView = () => {
	Tool.goPage.push(`/pages/tour/tour?guideId=${routerParams.scenicId}`)
}
</script>

<style lang="scss" scoped>
.scenic-banner {
	position: fixed;
	top: 0px;
	width: 100%;
	height: 500rpx;
	z-index: 1;
}
.sceinc {
	min-height: 100%;
	background-color: transparent;
	z-index: 10; /* 确保内容显示在地图上方 */
	position: relative; /* 增加定位，使 z-index 生效 */
	.sceinc-info {
		background-color: #fff;
		border-top-left-radius: 32rpx;
		border-top-right-radius: 32rpx;
		padding: 32rpx 30rpx;
		overflow: hidden;
		position: relative;
		box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);
		.sceinc-info-bar {
			width: 46rpx;
			height: 6rpx;
			background-color: #d8d8d8;
			border-radius: 3rpx;
			position: absolute;
			left: 50%;
			top: 10rpx;
			transform: translateX(-50%);
		}
		
		.scenic-detail {
			display: flex;
			margin-top: 20rpx;
			
			.scenic-img {
				width: 180rpx;
				height: 180rpx;
				border-radius: 12rpx;
				flex-shrink: 0;
				box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
				object-fit: cover;
			}
			
			.scenic-content {
				flex: 1;
				margin-left: 24rpx;
				// display: flex;
				// flex-direction: column;
				
				.scenic-name {
					font-size: 36rpx;
					font-weight: 600;
					color: #14131f;
					margin-bottom: 12rpx;
					line-height: 1.2;
				}
				
				.sceinc-info__rating {
					display: inline-flex;
					border-radius: 4rpx;
					align-items: center;
					margin-top: 2rpx;
					margin-bottom: 10rpx;
					background: #E1F1FF;
					height: 42rpx;

					.rating-score {
						width: 84rpx;
						height: 42rpx;
						font-size: 28rpx;
						font-weight: 600;
						background: #1C78E9;
						color: #fff;
						border-radius: 4rpx 0 25rpx 4rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 0;
						line-height: 1;

						.rating-unit {
							font-size: 24rpx;
							font-weight: 400;
							margin-left: 2rpx;
						}
					}

					.rating-count {
						display: flex;
						align-items: center;
						font-size: 24rpx;
						color: #1C78E9;
						line-height: 1;
						padding: 0 10rpx;
						height: 42rpx;
						position: relative;
						
						.triangle-right {
							width: 0;
							height: 0;
							border-top: 10rpx solid transparent;
							border-bottom: 10rpx solid transparent;
							border-left: 12rpx solid #1C78E9;
							margin-left: 8rpx;
							position: relative;
							top: 0rpx;
						}

						&::after {
							content: '';
							display: inline-block;
							margin-left: 6rpx;
							margin-top: 2rpx;
						}
					}
				}
				
				.scenic-item {
					display: flex;
					align-items: flex-start;
					margin-bottom: 10rpx;
					font-size: 26rpx;
					line-height: 1.4;
					
					.label {
						color: #666;
						flex-shrink: 0;
					}
					
					.value {
						color: #333;
						flex: 1;
						word-break: break-all;
					}
					
					.nav-btn {
						padding: 4rpx 8rpx;
						margin-left: 8rpx;
						flex-shrink: 0;
					}
				}
				
				.tags-row {
					display: flex;
					flex-wrap: wrap;
					margin-bottom: 16rpx;
					
					.tag {
						display: inline-block;
						padding: 4rpx 12rpx;
						font-size: 22rpx;
						font-weight: 400;
						color: #ff9201;
						border: 1rpx solid #ff9201;
						border-radius: 6rpx;
						margin-right: 10rpx;
						margin-bottom: 8rpx;
						background-color: rgba(255, 146, 1, 0.05);
						
						&.nft {
							color: #1C78E9;
							border-color: #1C78E9;
							background-color: rgba(28, 120, 233, 0.05);
						}
					}
				}
				
				.price-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: auto;
					padding-top: 10rpx;
					margin-bottom: 16rpx;
					
					.price {
						display: flex;
						align-items: baseline;
						font-size: 42rpx;
						font-weight: 600;
						color: #f43636;

						.unit {
							font-size: 28rpx;
							margin-right: 2rpx;
						}

						.up {
							margin-left: 4rpx;
							font-size: 28rpx;
							color: #6b6b6b;
							font-weight: 400;
						}
					}
					
					.buy-btn {
						display: inline-block;
						padding: 12rpx 30rpx;
						font-size: 26rpx;
						font-weight: 500;
						color: #ffffff;
						line-height: 37rpx;
						letter-spacing: 1px;
						background: #FF9201;
						border-radius: 34rpx;
						box-shadow: 0 4rpx 8rpx rgba(255, 146, 1, 0.3);
					}
				}
				
				.action-row {
					display: flex;
					margin-top: auto;
					padding-top: 16rpx;
					
					.collect-btn, .phone-btn {
						display: flex;
						align-items: center;
						padding: 8rpx 16rpx;
						border-radius: 30rpx;
						background: #f5f5f5;
						margin-right: 20rpx;
						
						.collect-text, .phone-text {
							font-size: 24rpx;
							color: #666;
							margin-left: 6rpx;
							
							&.active {
								color: #FF9201;
							}
						}
					}
				}
			}
		}
		
		.sceinc-info__label {
			display: inline-block;
			padding: 2rpx 8rpx;
			margin: 20rpx 0 30rpx;
			border-radius: 6rpx;
			border: 1rpx solid #ff9201;
			font-size: 22rpx;
			font-weight: 400;
			color: #ff9201;
		}
		.sceinc-info__bottom {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.price {
				display: flex;
				align-items: baseline;
				font-size: 42rpx;
				font-weight: 600;
				color: #f43636;

				.unit {
					margin-right: 1rpx;
					font-size: 28rpx;
				}

				.up {
					margin-left: 4rpx;
					font-size: 28rpx;
					color: #6b6b6b;
					font-weight: 400;
				}
			}

			.buy {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10rpx 20rpx;
				background: #ff9201;
				border-radius: 34rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #ffffff;
				letter-spacing: 1rpx;
			}
		}
		.info-box,
		.info-box-group {
			padding: 32rpx 30rpx 34rpx;
		}

		.info-box-group {
			.group-title {
				display: flex;
				justify-content: space-between;
				font-size: 30rpx;
				font-weight: 400;
				color: #000000;
				line-height: 30rpx;
			}
		}
	}

	.title {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;

		.name {
			font-size: 40rpx;
			font-weight: 500;
			color: #000000;
		}

		.level {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 14rpx;
			padding: 0 12rpx;
			height: 34rpx;
			background: #ffe7ca;
			border-radius: 6rpx;
			font-size: 22rpx;
			font-weight: 400;
			color: #ff772f;
		}

		.phone-icon {
			$w: 54rpx;
			width: $w;
			height: $w;
			margin-left: auto;
		}

		.collect {
			margin-left: auto;
		}
	}

	.ticket {
		background-color: #fff;
		margin-bottom: 20rpx;
		.ticket-category {
			display: flex;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #E9E9E9;
			
			.category-icon {
				width: 6rpx;
				height: 28rpx;
				background: #4787FB;
				border-radius: 3rpx;
				margin-right: 12rpx;
			}
			
			.category-text {
				font-size: 32rpx;
				font-weight: 600;
				color: #050505;
			}
		}
		
		.ticket-list {
			.item {
				position: relative;
				margin: 0;
				padding: 34rpx 30rpx;
				background-color: #fff;
				border-radius: 12rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

				&:not(:last-child) {
					position: relative;
					
					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 30rpx; /* 左侧留白距离 */
						right: 30rpx; /* 右侧留白距离 */
						height: 1px;
						background-color: #E9E9E9;
					}
				}

				> .title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 16rpx;
					.left {
						margin-right: 8rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #000000;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.right {
						flex: none;
						font-size: 42rpx;
						font-weight: 600;
						color: #f43636;
						line-height: 42rpx;

						.unit {
							font-size: 28rpx;
						}

						.up {
							font-size: 28rpx;
							color: #6b6b6b;
							font-weight: 400;
						}
					}
				}

				.time {
					display: flex;
					align-items: center;
					font-size: 26rpx;
					font-weight: 400;
					color: #14131F;
					margin-bottom: 12rpx;
					
					text {
						margin-left: 6rpx;
					}
				}

				.tags-row {
					display: flex;
					flex-wrap: wrap;
					margin-bottom: 12rpx;
					
					.tag {
						display: inline-block;
						padding: 4rpx 12rpx;
						font-size: 22rpx;
						font-weight: 400;
						color: #ff9201;
						border: 1rpx solid #ff9201;
						border-radius: 6rpx;
						margin-right: 10rpx;
						// margin-bottom: 8rpx;
						background-color: rgba(255, 146, 1, 0.05);
						
						&.nft {
							color: #1C78E9;
							border-color: #1C78E9;
							background-color: rgba(28, 120, 233, 0.05);
						}
					}
				}

				.reserve {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						display: flex;
						align-items: center;
						font-size: 24rpx;
						font-weight: 400;
						color: #1c78e9;
						line-height: 33rpx;

						.icon {
							width: 14rpx;
							margin-left: 8rpx;
						}
					}

					.right {
						display: inline-block;
						padding: 12rpx 30rpx;
						font-size: 26rpx;
						font-weight: 500;
						color: #ffffff;
						line-height: 37rpx;
						letter-spacing: 1px;
						background: #FF9201;
						border-radius: 34rpx;
						box-shadow: 0 4rpx 8rpx rgba(255, 146, 1, 0.3);
					}
				}
			}
		}
	}

	.group-ticket {
		margin-bottom: 30rpx;
		border-radius: 24rpx;

		> .title {
			padding-top: 30rpx;
			font-weight: 600;
			margin-bottom: 22rpx;
			color: #050505;
			font-size: 34rpx;
		}

		.item {
			margin-bottom: 30rpx;
			padding: 34rpx 30rpx;
			background-color: #fff;
			border-radius: 24rpx;
			box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

			> .title {
				margin-right: 8rpx;
				font-weight: 500;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 34rpx;
				color: #14131f;
				margin-bottom: 16rpx;
			}

			.ticket-item {
				padding-top: 30rpx;
				overflow: hidden;
				.ticket-name {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					font-weight: 500;
					color: #000000;
				}
				.ticket-info {
					display: flex;
					align-items: center;
					margin-top: 15rpx;

					.realName {
						margin-right: 28rpx;
						padding: 2rpx 12rpx;
						background: #ffe7ca;
						border-radius: 6rpx;
						font-size: 22rpx;
						font-weight: 400;
						color: #ff772f;
					}

					.ticket-number {
						font-size: 22rpx;
						font-weight: 400;
						color: #14131f;
					}
				}
			}
			.ticket-item:not(:last-child) {
				border-bottom: 1px solid rgba(157, 157, 157, 0.1);
				padding-bottom: 30rpx;
			}

			.ticket-reserve {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;
				margin-top: 15rpx;

				.left {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #1c78e9;
					line-height: 33rpx;

					.icon {
						width: 14rpx;
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	.product-explain {
		margin-bottom: 30rpx;
		margin-top: 42rpx;
		border-radius: 24rpx;

		> .title {
			margin-bottom: 20rpx;
			color: #050505;
			font-size: 32rpx;
			font-weight: 500;
		}

		.note {
			margin-bottom: 30rpx;
			padding: 34rpx 30rpx;
			background-color: #fff;
			border-radius: 24rpx;
			box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
		}
	}
}
.tab-container {
	background-color: #ffffff;
	padding: 16rpx 30rpx 0 44rpx;
	// 
}

.tab-list {
	width: 100%;
	display: flex;
	justify-content: flex-start;
}

.tab-item {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.tab-text {
	font-size: 32rpx;
	letter-spacing: 0.28rpx;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 45rpx;
}

.tab-active {
	color: rgba(23, 24, 26, 1);
}

.tab-inactive {
	color: rgba(153, 153, 153, 1);
}

.tab-indicator {
	background-color: rgba(23, 24, 26, 1);
	border-radius: 2rpx;
	width: 56rpx;
	height: 4rpx;
	margin-top: 15rpx;
}

.tab-container.fixed {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.cloud-view{
	position: fixed;
	display: flex;
	align-items: center;
	justify-content: center;
	top: 100rpx;
	left: 30rpx;
	font-size: 26rpx;
	padding: 15rpx 12rpx;
	background: #FFFFFF;
	box-shadow: 0px 0px 8rpx 3rpx rgba(166, 166, 166, 0.5);
	border-radius: 12rpx;
	z-index: 999;
	.cloud-img{
		width: 35rpx;
		height: 35rpx;
		margin-right: 10rpx;
	}
}
</style>
