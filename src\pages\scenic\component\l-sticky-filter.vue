<template>
	<view>
		<!-- 简化后的标签滚动容器 -->
		<view class="sticky-filter" :class="{'show-sticky-filter': showStickyFilter}">
			<view class="filter-container">
				<view class="custom-scroll-view sticky-tags">
					<view class="sticky-tags-inner flex-row">
						<view v-for="(tag, index) in filterTags" :key="index" class="sticky-tag-item"
							:class="[tag.active ? 'sticky-tag-active' : '']" @click="handleTagSelect(index)">
							<text class="sticky-tag-text" :class="[tag.active ? 'sticky-tag-text-active' : '']">{{ tag.text }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 统一的展开/收起按钮 -->
		<view class="toggle-filter-btn" @click="toggleFilterPanel" v-if="showStickyFilter">
			<y-svg name="arrow-down" class="toggle-icon" :class="{ 'is-expanded': showFilterPanel }"></y-svg>
		</view>

		<!-- 筛选下拉面板 -->
		<view class="filter-panel" v-if="showFilterPanel" @click="toggleFilterPanel">
			<view class="filter-panel-content flex-col" @click.stop>
				<view class="panel-header flex-row justify-between">
					<text class="panel-title">筛选</text>
				</view>
				<view class="panel-tags-container">
					<view class="panel-tags flex-row">
						<view v-for="(tag, index) in filterTags" :key="index" class="panel-tag-item"
							:class="[tag.active ? 'panel-tag-active' : '']" @click="handleTagSelectAndClose(index)">
							<text class="panel-tag-text" :class="[tag.active ? 'panel-tag-text-active' : '']">{{ tag.text }}</text>
						</view>
					</view>
				</view>
				<view class="panel-sort-options flex-row justify-between">
					<text class="panel-sort-option" :class="{'panel-sort-active': sortType === 'recommend'}" 
						@click="handleSortChangeAndClose('recommend')">推荐</text>
					<text class="panel-sort-option" :class="{'panel-sort-active': sortType === 'latest'}" 
						@click="handleSortChangeAndClose('latest')">最新</text>
				</view>
			</view>
		</view>

		<!-- 吸顶筛选栏占位元素，防止遮挡内容 -->
		<view class="sticky-placeholder" v-if="showStickyFilter"></view>
	</view>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import ySvg from '@/components/y-svg/y-svg.vue';

const props = defineProps({
	// 筛选标签数据
	filterTags: {
		type: Array,
		default: () => []
	},
	// 排序类型：'recommend' 或 'latest'
	sortType: {
		type: String,
		default: 'recommend'
	},
	// 是否显示吸顶筛选栏
	isVisible: {
		type: Boolean,
		default: false
	}
});

const emits = defineEmits(['update:isVisible', 'tag-select', 'sort-change']);

// 控制吸顶筛选栏的显示与隐藏
const showStickyFilter = ref(false);
// 控制筛选下拉面板的显示与隐藏
const showFilterPanel = ref(false);

// 监听 isVisible 属性变化
watch(() => props.isVisible, (newValue) => {
	showStickyFilter.value = newValue;
});

// 切换筛选下拉面板
const toggleFilterPanel = () => {
	showFilterPanel.value = !showFilterPanel.value;
};

// 选择标签
const handleTagSelect = (index) => {
	emits('tag-select', index);
};

// 选择标签并关闭面板
const handleTagSelectAndClose = (index) => {
	handleTagSelect(index);
	showFilterPanel.value = false;
};

// 切换排序类型
const handleSortChange = (type) => {
	emits('sort-change', type);
};

// 切换排序类型并关闭面板
const handleSortChangeAndClose = (type) => {
	handleSortChange(type);
	showFilterPanel.value = false;
};
</script>

<style lang="scss" scoped>
// 吸顶筛选栏
.sticky-filter {
	position: fixed;
	top: 0;
	left: 0;
	width: 750rpx;
	background-color: #fff;
	z-index: 100;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	padding: 12rpx 0 12rpx 30rpx;
	transform: translateY(-100%);
	transition: transform 0.3s ease;
	
	&.show-sticky-filter {
		transform: translateY(0);
	}
	
	.filter-container {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.custom-scroll-view {
		width: 670rpx;
		overflow-x: auto;
		overflow-y: hidden;
		/* 隐藏滚动条 - 适用于 WebKit 浏览器（Chrome、Safari 等） */
		&::-webkit-scrollbar {
			display: none;
		}
		/* 隐藏滚动条 - 适用于 Firefox */
		scrollbar-width: none;
		/* 隐藏滚动条 - IE 和 Edge */
		-ms-overflow-style: none;
	}
	
	.sticky-tags {
		white-space: nowrap;
		
		.sticky-tags-inner {
			display: inline-flex;
			align-items: center;
			
			.sticky-tag-item {
				background-color: #F5F6F7;
				border-radius: 8rpx;
				height: 48rpx;
				padding: 6rpx 16rpx;
				margin-right: 22rpx;
				display: inline-flex;
				align-items: center;
				
				&.sticky-tag-active {
					background-color: #349FFF;
				}
				
				.sticky-tag-text {
					color: #17181A;
					font-size: 24rpx;
					line-height: 36rpx;
					white-space: nowrap;
					overflow-wrap: break-word;
					
					&.sticky-tag-text-active {
						color: #FFFFFF;
					}
				}
			}
		}
	}
}

// 统一的展开/收起按钮
.toggle-filter-btn {
	position: fixed;
	top: 15rpx;
	right: 0rpx;
	width: 48rpx;
	height: 48rpx;
	background-color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 150; // 确保按钮在筛选栏之上
	
	.toggle-icon {
		width: 32rpx;
		height: 32rpx;
		transition: transform 0.3s ease;
		
		&.is-expanded {
			transform: rotate(180deg);
		}
	}
}

// 筛选下拉面板
.filter-panel {
	position: fixed;
	top: 0;
	left: 0;
	width: 750rpx;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 140; // 确保在按钮之下
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.filter-panel-content {
		width: 750rpx;
		background-color: #fff;
		padding: 28rpx 30rpx 30rpx 30rpx;
		border-radius: 0 0 16rpx 16rpx;
		max-height: 70vh;
		overflow-y: auto;
		
		.panel-header {
			margin-bottom: 20rpx;
			
			.panel-title {
				color: #14131F;
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				font-weight: 500;
				line-height: 33rpx;
			}
		}
		
		.panel-tags-container {
			margin-bottom: 20rpx;
			
			.panel-tags {
				display: flex;
				flex-wrap: wrap;
				gap: 20rpx;
				
				.panel-tag-item {
					background-color: #F5F6F7;
					border-radius: 8rpx;
					height: 48rpx;
					padding: 6rpx 16rpx;
					display: flex;
					align-items: center;
					
					&.panel-tag-active {
						background-color: #349FFF;
					}
					
					.panel-tag-text {
						color: #17181A;
						font-size: 24rpx;
						line-height: 36rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						max-width: 300rpx; // 限制最大宽度

						&.panel-tag-text-active {
							color: #FFFFFF;
						}
					}
				}
			}
		}
		
		.panel-sort-options {
			width: 139rpx;
			margin-top: 30rpx;
			
			.panel-sort-option {
				font-size: 24rpx;
				letter-spacing: 0.21rpx;
				line-height: 33rpx;
				color: #999;
				
				&.panel-sort-active {
					color: #14131F;
					font-weight: 500;
				}
			}
		}
	}
}

// 吸顶筛选栏占位元素，防止遮挡内容
.sticky-placeholder {
	height: 72rpx; // 调整高度以匹配吸顶筛选栏的高度
	width: 100%;
}

// 工具类
.flex-row {
	display: flex;
	flex-direction: row;
}

.flex-col {
	display: flex;
	flex-direction: column;
}

.justify-between {
	justify-content: space-between;
}
</style> 