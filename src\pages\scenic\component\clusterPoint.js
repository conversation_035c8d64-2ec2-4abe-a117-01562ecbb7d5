import icon_1_ from "@/static/image/tour/tour_icon_1_.svg";
import icon_2_ from "@/static/image/tour/tour_icon_2_.svg";
import icon_3_ from "@/static/image/tour/tour_icon_3_.svg";
import icon_4_ from "@/static/image/tour/tour_icon_4_.svg";
import icon_5_ from "@/static/image/tour/tour_icon_5_.svg";
import icon_6_ from "@/static/image/tour/tour_icon_6_.svg";
import icon_7_ from "@/static/image/tour/tour_icon_7_.svg";
import icon_8_ from "@/static/image/tour/tour_icon_8_.svg";
import icon_9_ from "@/static/image/tour/tour_icon_9_.svg";
import icon_10_ from "@/static/image/tour/tour_icon_10_.svg";
import icon_11_ from "@/static/image/tour/tour_icon_11_.svg";
import icon_12_ from "@/static/image/tour/tour_icon_12_.svg";
import icon_13_ from "@/static/image/tour/tour_icon_13_.svg";
import icon_14_ from "@/static/image/tour/tour_icon_14_.svg";
import { getEnv } from "@/utils/getEnv";

const tabIcon = {
  1: icon_1_,
  2: icon_2_,
  3: icon_3_,
  4: icon_4_,
  5: icon_5_,
  6: icon_6_,
  7: icon_7_,
  8: icon_8_,
  9: icon_9_,
  10: icon_10_,
  11: icon_11_,
  12: icon_12_,
  13: icon_13_,
  14: icon_14_,
};

// 基于DOMOverlay实现dom点位
function Point(options) {
  TMap.DOMOverlay.call(this, options);
}

Point.prototype = new TMap.DOMOverlay();

// 初始化
Point.prototype.onInit = function (options) {
  this.details = options.details;
  this.position = options.position;
  this.content = options.content;
  this.id = options.id;
  this.moveValue = 0;
  this.index = options.index;
};

// 销毁时需解绑事件监听
Point.prototype.onDestroy = function () {
  this.dom.removeEventListener("touchstart", this.onTouchstart);
  this.dom.removeEventListener("touchmove", this.onTouchmove);
  this.dom.removeEventListener("touchend", this.onClick);
  this.removeAllListeners();
};

// 创建DOM元素，返回一个DOMElement，使用this.dom可以获取到这个元素
Point.prototype.createDOM = function () {
  let dom = document.createElement("div");
  let div = document.createElement("div");
  let imgDiv = document.createElement("div"); // 类型图标
  let imgDivSpan = document.createElement("span"); // 详情值
  let imgDivImg = document.createElement("img"); // 详情图
  let span = document.createElement("span");

  // 设置图标
  const pointType = this.details.pointType || 1;
  imgDivImg.src =
    pointType < 6 && this.details.publicizeUrl
      ? getEnv().VITE_IMG_HOST + this.details.publicizeUrl
      : tabIcon[pointType];
  imgDiv.style = `background: url(${tabIcon[pointType]}) no-repeat center/contain;`;

  // 添加索引（如果有）
  if (this.index) {
    imgDivSpan.innerHTML = this.index;
  }

  imgDiv.append(imgDivSpan, imgDivImg);
  div.classList.add("logo_box");
  div.append(imgDiv);
  span.innerText = this.content;
  dom.classList.add("clusterPoint");
  dom.append(div, span);

  // click事件监听
  this.onClick = () => {
    // DOMOverlay继承自EventEmitter，可以使用emit触发事件
    if (this.moveValue < 10) this.emit("click");
  };

  this.onTouchstart = () => (this.moveValue = 0);
  this.onTouchmove = () => this.moveValue++;

  // pc端注册click事件，移动端注册touchend事件
  dom.addEventListener("touchstart", this.onTouchstart);
  dom.addEventListener("touchmove", this.onTouchmove);
  dom.addEventListener("touchend", this.onClick);

  return dom;
};

// 更新DOM元素，在地图移动/缩放后执行
Point.prototype.updateDOM = function () {
  if (!this.map) {
    return;
  }
  // 经纬度坐标转容器像素坐标
  let pixel = this.map.projectToContainer(this.position);
  // 气泡箭头对齐经纬度坐标点
  let left = pixel.getX() - this.dom.offsetWidth * 0.5 + "px";
  let top = pixel.getY() - this.dom.offsetWidth * 1.1875 + "px";
  this.dom.style.transform = `translate(${left}, ${top})`;
};

window.Point = Point;
