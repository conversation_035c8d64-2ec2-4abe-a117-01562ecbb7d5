<template>
  <view v-if="tourList.length" class="tour_list">
    <view class="tour_list--box">
      <view class="image--box">
        <image
          v-for="(item, index) in tourList"
          :key="index"
          :src="imgSrc(item.publicizeImgUrl)"
          :class="{active: index == tourIndex}"
          mode="aspectFill"
        />
      </view>
      <view class="swiper--box">
        <swiper class="swiper" @change="change" :current="tourIndex">
          <swiper-item class="swiper--item" v-for="(item, index) in tourList" :key="index">
            <text>{{ item.scenicName }}</text>
          </swiper-item>
        </swiper>
        <image
          class="swiper--dots__left"
          :class="{'swiper--dots__active': tourIndex}"
          src="@/static/image/tour/tour_arrows.svg"
          mode="aspectFit"
          @click="tourIndex && tourIndex--"
        />
        <image
          class="swiper--dots__right"
          :class="{'swiper--dots__active': tourIndex < tourList.length - 1}"
          src="@/static/image/tour/tour_arrows.svg"
          mode="aspectFit"
          @click="tourIndex < tourList.length - 1 && tourIndex++"
        />
      </view>
      <view class="content--box">
        <view class="left">
          <text class="title">{{ tourList[tourIndex].scenicName }}</text>
          <text class="distance" v-if="tourList[tourIndex].latitude && tourList[tourIndex].longitude">距您{{ tourList[tourIndex].distance || 0 }}公里</text>
        </view>
        <view class="right" @click="tourTap">
          <text>进入导览</text>
          <image src="@/static/image/tour/tour_arrows_.svg" mode="aspectFit" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue';
import { getRoute, imgSrc, pointDistance } from "@/utils/tool.js";
import request from "@/utils/request.js";

const tourList = ref([])
const tourIndex = ref(0)
const tourTap = () => {
  const item = tourList.value[tourIndex.value]
  if (item.type == 2) { // 外部链接
    location.href = item.navigationUrl
    // open(item.navigationUrl, '_blank')
  } else {
    Tool.goPage.push('/pages/tour/tour?guideId=' + item.scenicId)
    }
  }
  const change = ({ detail: { current } }) => {
    tourIndex.value = current
  }
  const initTourList = () => {
    tourList.value.map(item => {
      if (item.latitude && item.longitude) {
        item.distance = pointDistance([item.latitude, item.longitude])
      }
    })
  }
  // 加载地图 API 完成
  const loadMap = () => {
    if (window.navFrom) return initTourList()
    uni.getLocation({
      type: 'gcj02',
      isHighAccuracy: true, // 高精度
      highAccuracyExpireTime: 5000, // 超时
      success: res => {
        window.navFrom = [res.latitude, res.longitude]
        initTourList()
      },
      fail: err => {},
    })
  }
  // 加载地图 API
  const loadScript = () => {
    window.loadMap = loadMap
    var script = document.createElement("script")
    script.type = "text/javascript"
    script.src =
      "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=geometry&callback=loadMap"
    document.body.appendChild(script)
  }
  request.get('/navigation/store/list', {storeId: getRoute.params().storeId}).then(({data}) => {
    tourList.value = data
    window.TMap?loadMap():loadScript()
  })
</script>

<style lang="scss" scoped>
  .tour_list {
    width: 100vw;
    height: calc(100vh - 98rpx);
    padding: 60rpx 30rpx;
    background: #fff;
    overflow: auto;
    .tour_list--box {
      width: 100%;
      height: 100%;
      min-height: 656rpx;
      max-height: 1100rpx;
      border-radius: 13rpx;
      display: flex;
      flex-direction: column;
      color: #fff;
      position: relative;
      overflow: hidden;
      .image--box {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        >image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transition: 1s;
          opacity: 0;
        }
        .active {
          opacity: 1;
        }
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          background: #000;
          opacity: 0.13;
        }
      }
      .swiper--box {
        width: 100%;
        height: 0;
        flex: 1;
        position: relative;
        .swiper {
          width: 100%;
          height: 100%;
          .swiper--item {
            display: flex;
            justify-content: center;
            align-items: center;
            >text {
              max-height: 420rpx;
              font-size: 60rpx;
              font-family: cursive;
              font-weight: bold;
              line-height: 66rpx;
              letter-spacing: 10rpx;
              writing-mode: vertical-lr;
            }
          }
        }
        .swiper--dots__left,.swiper--dots__right {
          position: absolute;
          top: 50%;
          width: 50rpx;
          height: 50rpx;
          opacity: .5;
          transition: .2s;
        }
        .swiper--dots__left {
          left: 20rpx;
          transform: translateY(-50%) scaleX(-1);
        }
        .swiper--dots__right {
          right: 20rpx;
          transform: translateY(-50%);
        }
        .swiper--dots__active {
          opacity: 1;
        }
      }
      .content--box {
        position: relative;
        margin: 0 auto 30rpx;
        width: 630rpx;
        height: 145rpx;
        border-radius: 12rpx;
        backdrop-filter: blur(7px);
        background-color: rgba(255, 255, 255, .1);
        padding: 0 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          width: 0;
          flex: 1;
          display: flex;
          flex-direction: column;
          .title {
            font-size: 36rpx;
            font-weight: bold;
            line-height: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .distance {
            margin-top: 16rpx;
            font-size: 24rpx;
            line-height: 1;
          }
        }
        .right {
          margin-left: 30rpx;
          width: 168rpx;
          height: 58rpx;
          background: #fff;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          >text {
            font-size: 28rpx;
            font-weight: bold;
            color: #349FFF;
          }
          >image {
            margin-left: 13rpx;
            width: 15rpx;
            height: 24rpx;
          }
        }
      }
    }
  }
</style>