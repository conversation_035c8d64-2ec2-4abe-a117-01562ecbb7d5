<!--
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-06-20 16:06:42
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-07-01 17:51:23
 * @FilePath: \shop\src\components\drawer\drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <transition name="drawer-fade">
    <view class="drawer-mask" v-show="visible" @click="close">
      <transition name="drawer-slide">
        <view class="drawer-content" v-show="visible" @click.stop>
          <slot></slot>
        </view>
      </transition>
    </view>
  </transition>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible"]);

const close = () => {
  emit("update:visible", false);
};
</script>

<style lang="scss" scoped>
/* 遮罩层动画 */
.drawer-fade-enter-active,
.drawer-fade-leave-active {
  transition: opacity 0.3s ease;
}

.drawer-fade-enter-from,
.drawer-fade-leave-to {
  opacity: 0;
}

/* 抽屉内容动画 */
.drawer-slide-enter-active {
  animation: slide-in 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}

.drawer-slide-leave-active {
  animation: slide-out 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

@keyframes slide-in {
  0% {
    transform: translateY(100%);
  }
  // 60% { transform: translateY(-5%); }
  100% {
    transform: translateY(0);
  }
}

@keyframes slide-out {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: center;

  .drawer-content {
    width: 100%;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
    box-shadow: 0 -4rpx 30rpx rgba(0, 0, 0, 0.1);
  }
}
</style>
