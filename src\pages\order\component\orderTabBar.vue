<template>
	<view class="order-tab-bar">
		<view
			class="item"
			:class="{ active: activeIndex === 'all' }"
			@click="changeTab('all')">
			<y-svg class="icon" name="order-all-icon" />
			<text>全部</text>
		</view>
		<view
			class="item"
			:class="{ active: activeIndex === 'unpaid' }"
			@click="changeTab('unpaid')">
			<y-svg class="icon" name="order-unpaid-icon" />
			<text>待支付</text>
		</view>
		<view
			class="item"
			:class="{ active: activeIndex === 'completed' }"
			@click="changeTab('completed')">
			<y-svg class="icon" name="order-completed-icon" />
			<text>待出行</text>
		</view>
		<view
			class="item"
			:class="{ active: activeIndex === 'comment' }"
			@click="changeTab('comment')">
			<y-svg class="icon" name="order-comment-icon" />
			<text>待点评</text>
		</view>
		<view
			class="item"
			:class="{ active: activeIndex === 'sale' }"
			@click="changeTab('sale')">
			<y-svg class="icon" name="order-sale-icon" />
			<text>售后</text>
		</view>
	</view>
</template>

<script>
import { toRefs, reactive, ref } from "vue"
export default {
	name: "orderTabBar",
	setup(props, context) {
		// 点击 tab
		const activeIndex = ref("all")
		const changeTab = type => {
			activeIndex.value = type
			context.emit("changeTab", type)
		}

		return {
			activeIndex,
			changeTab
		}
	}
}
</script>

<style lang="scss" scoped>
.order-tab-bar {
	display: flex;
	justify-content: space-around;
	align-items: center;
	background-color: #fff;
	height: 140rpx;
	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 28rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #14131f;
		&.active {
			opacity: 1;
			color: var(--theme-color);
			font-weight: 700;
		}
		.icon {
			$w: 60rpx;
			width: $w;
			height: $w;
			// margin-bottom: 8rpx;
		}
	}
}
</style>
