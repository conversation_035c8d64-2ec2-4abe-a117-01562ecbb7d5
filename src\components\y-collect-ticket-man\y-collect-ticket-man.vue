<template>
	<view v-if="
		edit ||
		modelValue.name !== undefined ||
		modelValue.identity !== undefined ||
		modelValue.mobile !== undefined
	" class="collect-ticket-man">
		<view class="title">
			<y-font-weight>联系人</y-font-weight>
			<view v-if="!edit && modelValue.mobile !== undefined" class="eye" @tap="switchShowMobile">

				<image :class="showMobile ? 'icon-open-eye' : 'icon-eye'"
					:src="showMobile ? '@/static/image/icon_inputopeneye.png' : '@/static/image/icon_inputeye.png'"
					mode="widthFix" />

			</view>
		</view>
		<view class="info-box">
			<view v-if="edit || modelValue.name !== undefined" class="item bottom-line">
				<view class="key"> 姓&emsp;名 </view>
				<template v-if="edit">
					<input v-model="modelValue.name" type="text" placeholder="与身份证名字保持一致" placeholder-class="placeholder-style" />
				</template>
				<template v-else>
					<view>{{ modelValue.name }}</view>
				</template>
			</view>
			<!-- <view
				class="item bottom-line"
				v-if="edit || modelValue.identity !== undefined">
				<view class="key"> 身份证 </view>
				<template v-if="edit">
					<input
						v-model="modelValue.identity"
						maxlength="18"
						type="text"
						placeholder="用于景区入园凭证"
						placeholder-class="placeholder-style" />
				</template>
				<template v-else>
					<view v-if="modelValue.identity">{{
						`${modelValue.identity.slice(
							0,
							3
						)}***********${modelValue.identity.slice(-4)}`
					}}</view>
				</template>
			</view> -->
			<view v-if="edit || modelValue.mobile !== undefined" class="item">
				<view class="key"> 联系手机 </view>

				<template v-if="edit">
					<input v-model="modelValue.mobile" maxlength="11" type="text" placeholder="请输入手机号"
						placeholder-class="placeholder-style" />
				</template>

				<template v-else-if="modelValue.mobile">
					<view>
						{{ showMobile ? modelValue.mobile : `${modelValue.mobile.slice(0, 3)}****${modelValue.mobile.slice(-4)}` }}
					</view>
				</template>
			</view>

		</view>
	</view>
</template>
<script>
import { toRefs, reactive, ref } from "vue"
export default {
	name: "collect-ticket-man",
	props: {
		modelValue: {
			type: Object,
			default: () => {
				return {
					name: '',
					identity: '',
					mobile: ''
				}
			}
		},
		//是否可编辑
		edit: {
			type: Boolean,
			default: false
		}
	},
	setup(props, context) {
		const showMobile = ref(false);
		const switchShowMobile = () => {
			showMobile.value = !showMobile.value;
		};
		return {
			...toRefs(props),
			showMobile,
			switchShowMobile
		}
	}
}
</script>
<style lang="scss" scoped>
.collect-ticket-man {
	margin-bottom: 20rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;

	>.title {
		padding: 30rpx 0;
		margin: 0 30rpx;
		font-weight: 500;
		color: #050505;
		font-size: 34rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
		display: flex;
		justify-content: space-between;
		align-items: center;

		.eye {
			width: 60rpx;
			text-align: right;

			.icon-eye {
				width: 30rpx;
				height: 15rpx;
			}

			.icon-open-eye {
				width: 30rpx;
				height: 23rpx;
			}
		}
	}

	>.info-box {
		padding: 0 30rpx;

		>.item {
			display: flex;
			align-items: center;
			margin: 30rpx 0;
			font-size: 28rpx;

			.key {
				flex: none;
				margin-right: 40rpx;
			}

			input {
				flex: 1;
			}
		}
	}
}
</style>
