<script>
import {
  loadVConsole,
  createBackButton,
  handleWechatLogin
} from "@/utils/appInit.js"
import { getArrFun } from "@/utils/getEnum";

export default {
  onLaunch: async function () {

    // 处理微信登录
    const loginHandled = handleWechatLogin(getRoute)
    if (loginHandled) return

    // 获取系统来源参数
    const { source, source_url } = getRoute.params()
    // 先从路由参数获取，如果没有则从本地缓存读取
    let systemSource = source || sessionStorage.getItem("systemSource")
    let sourceUrl = source_url || sessionStorage.getItem("sourceUrl")
    
    if (systemSource) {
      sessionStorage.setItem("systemSource", systemSource)
      // 存储解码后的 source_url
      if (sourceUrl) {
        // 如果是从路由参数获取的 source_url 需要解码
        if (source_url) {
          sourceUrl = decodeURIComponent(source_url)
        }
        sessionStorage.setItem("sourceUrl", sourceUrl)
        // 只有在有 sourceUrl 时才创建返回按钮
        createBackButton()
      }
    }
    
    // 加载 debug 模块
    loadVConsole()
    
    const res =await getArrFun()
    localStorage.setItem('enumList',JSON.stringify(res))
  },
  
  onShow: function () {
    // console.log("App Show...")
  },
  onHide: function () {
    // console.log("App Hide")
  },
  onPageNotFound: [
    function () {
      // 跳转到 404 页面：
      uni.redirectTo({
        url: "pages/error/404", // 404 页面的路径
      });
    },
  ],
  globalData: {
    storeId: "",
    locationInfo: {},
    pointObj: {},
    userInfo: {}, // 用户信息
    realNameInfo: {}, // 实名信息
    themeConfig: {},
    storeConfig: {},
    storeInfo: {}, // 店铺信息
    wxLogin: {}, // 微信登录
    customPointList: [], // 自定义点位列表
  },
}
</script>
<style lang="less">
:root {
  --theme-color: #349fff;
  --theme-bg: #349fff;
  --linear-gradient0: #bbd9ff;
  --linear-gradient1: #eff6ff;
}


// 返回系统按钮样式
.back-to-system-button {
  transition: all 0.3s ease;
  
  &:hover {
    filter: brightness(1.05);
  }
  
  &:active {
    transform: translateX(2px);
  }
}

uni-page-body {
  background-color: #fcfcfc !important;
  font-family: PingFangSC,
    PingFang SC, PingFangSC-Regular;
}

/* 腾讯地图 logo */
#container>div {
  z-index: 0 !important;

  >div:last-child {
    display: none;
  }
}

/* 地图点聚合 */
.clusterBubble,
.clusterPoint {
  position: absolute;
  top: 0;
  left: 0;

  .logo_box {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background: #fff;
    position: relative;
    display: flex;
    transition: 0.2s;
    transform-origin: center calc(100% + 12rpx);
    // filter: drop-shadow(0 2rpx 4rpx #DBDBDB);
    filter: drop-shadow(0 2rpx 4rpx rgba(158, 158, 158, 0.5));

    &::before {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      z-index: -1;
      transform: translate(-50%, -8rpx);
      border-top: 20rpx solid #fff;
      border-left: 17.23rpx solid transparent;
      border-right: 17.23rpx solid transparent;
    }

    >span {
      margin: auto;
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: var(--theme-color);
      font-weight: bold;
      font-size: 30rpx;
      color: #fff;
      text-align: center;
      line-height: 48rpx;
    }

    >div {
      margin: auto;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      object-fit: cover;
      position: relative;
      transition: 0.3s;
      text-align: center;
      line-height: 40rpx;
      font-size: 14rpx;
      font-weight: bold;
      color: #f43636;

      >span {
        display: block;
      }

      >img {
        display: none;
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        object-fit: cover;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .content_box {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, 23rpx);
    background: #fff;
    border-radius: 12rpx;
    align-items: center;
    padding: 30rpx;
    box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);

    >div:first-child {
      >div:first-child {
        width: 200rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #090909;
        line-height: 36rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      >div:last-child {
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #535353;
        line-height: 33rpx;
      }
    }

    >div:last-child {
      margin-left: 14rpx;
      display: flex;
      align-items: flex-end;

      >img {
        width: 40rpx;
        height: 40rpx;
        object-fit: contain;
      }

      >div {
        font-size: 26rpx;
        color: var(--theme-color);
        white-space: nowrap;
      }
    }
  }

  >span {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, 23rpx);
    min-width: 97rpx;
    height: 42rpx;
    background: #fff;
    // box-shadow: 0rpx 2rpx 4rpx 0rpx #DBDBDB;
    box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);
    border-radius: 6rpx;
    text-align: center;
    line-height: 42rpx;
    font-size: 24rpx;
    color: #000;
    box-sizing: border-box;
    padding: 0 12rpx;
    white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }

  
}

.clusterBubble .logo_box {
  animation: popup-down 0.5s;

  @keyframes popup-down {
    0% {
      opacity: 0;
      transform: scale(1.5);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.check-in-wrapper {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-50%, 23rpx);
  box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);
    border-radius: 6rpx;
    padding: 0 6rpx;
      width: 150rpx;
      background: #fff;
      font-size: 24rpx;
      .check-in-point-name {
        text-align: center;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        width: 100%;
        max-width: 300rpx;
      }
    }

.clusterPoint .logo_box {
  animation: popup-up 0.5s;

  @keyframes popup-up {
    0% {
      opacity: 0;
      transform: scale(0);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.pointBig {
  z-index: 1;

  .logo_box {
    transform: scale(1.8);

    >div {
      background: #fff !important;
      transform: scale(1) !important;

      >span {
        display: none;
      }

      >img {
        display: block;
      }
    }
  }

  .content_box {
    display: flex;
  }
}

/* 位置点位 */
.locaPoint {
  position: absolute;
  top: 0;
  left: 0;
  width: 58rpx;
  height: 58rpx;
  background: rgba(153, 202, 255, 0.67);
  border-radius: 50%;
  display: flex;

  >img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
  }
}

.tutupoint,
.tutujourney {
  display: inline-block;
  margin: 0 10rpx;
  background: var(--theme-color);
  color: #fff;
  border-radius: 10rpx;
  padding: 2rpx 8rpx;
  font-size: 24rpx;
}


.markdown_html {

  ::v-deep h1,
  ::v-deep h2,
  ::v-deep h3,
  ::v-deep h4,
  ::v-deep h5,
  ::v-deep h6,
  ::v-deep p {
    font-size: 32rpx;
    margin: 10rpx 0;
  }

  ::v-deep ul {
    padding-left: 38rpx;
    margin: 10rpx 0;
  }

  ::v-deep li {
    margin: 10rpx 0;
  }


}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style lang="less">
.cPoint {
  position: absolute;
  top: 0;
  left: 0;

  .logo_box {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    // background: #fff;
    position: relative;
    display: flex;
    transition: 0.2s;
    transform-origin: center calc(100% + 12rpx);
    filter: drop-shadow(0 2rpx 4rpx rgba(158, 158, 158, 0.5));

    >span {
      margin: auto;
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: var(--theme-color);
      font-weight: bold;
      font-size: 30rpx;
      color: #fff;
      text-align: center;
      line-height: 48rpx;
    }

    >div {
      margin: auto;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      object-fit: cover;
      position: relative;
      transition: 0.3s;
      text-align: center;
      line-height: 40rpx;
      font-size: 14rpx;
      font-weight: bold;
      color: #f43636;

      >span {
        display: block;
      }

      >img {
        display: none;
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        object-fit: cover;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .content_box {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, 23rpx);
    background: #fff;
    border-radius: 12rpx;
    align-items: center;
    padding: 30rpx;
    box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);

    >div:first-child {
      >div:first-child {
        width: 200rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #090909;
        line-height: 36rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      >div:last-child {
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #535353;
        line-height: 33rpx;
      }
    }

    >div:last-child {
      margin-left: 14rpx;
      display: flex;
      align-items: flex-end;

      >img {
        width: 40rpx;
        height: 40rpx;
        object-fit: contain;
      }

      >div {
        font-size: 26rpx;
        color: var(--theme-color);
        white-space: nowrap;
      }
    }
  }

  >span {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, 23rpx);
    min-width: 97rpx;
    height: 42rpx;
    background: #fff;
    // box-shadow: 0rpx 2rpx 4rpx 0rpx #DBDBDB;
    box-shadow: 0rpx 1rpx 4rpx 1rpx rgba(158, 158, 158, 0.5);
    border-radius: 6rpx;
    text-align: center;
    line-height: 42rpx;
    font-size: 24rpx;
    color: #000;
    box-sizing: border-box;
    padding: 0 12rpx;
    white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }


}

.clusterBubble .logo_box {
  animation: popup-down 0.5s;

  @keyframes popup-down {
    0% {
      opacity: 0;
      transform: scale(1.5);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.cPoint .logo_box {
  animation: popup-up 0.5s;

  @keyframes popup-up {
    0% {
      opacity: 0;
      transform: scale(0);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}
</style>

