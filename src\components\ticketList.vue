<template>
	<view class='ticket-list'>
		<view class='item' v-for="(n,i) in 5" :key="i">
			<view class='img'>
				<img src='@/static/image/home/<USER>' />
			</view>
			<view class='center'>
				<view class='semicircle' :style="{borderRadius: '0% 0 22rpx 22rpx'}"></view>
				<view class='middle'></view>
				<view class='semicircle' :style="{borderRadius: '22rpx 22rpx 0rpx 0rpx'}"></view>
			</view>
			<view class='content'>
				<view class='title'><h3>世界之窗<span>5A</span></h3></view>
				<view class='info'>广东省深圳市南山区侨城东路华华华街道</view>
				<view class='pic'>￥<span>120</span><label>起</label></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"ticketList",
	}
</script>

<style lang="less">
	.ticket-list{
		.item{
			width: 100%;
			height: 216rpx;
			border-radius: 16rpx;
			background-color: #fff;
			display: flex;
			margin-bottom: 31rpx;
			box-shadow: 0rpx 2rpx 9rpx rgba(195,206,218,0.31), 0rpx -2rpx 3rpx rgba(195,206,218,0.31);
			.center{
				display: flex;
				width: 40rpx;
				height: 100%;
				flex-direction: column;
				justify-content: space-between;
				.semicircle{
					width: 100%;
					height: 20rpx;
					background-color: #f1f1f1; //FCFCFC
				}
				.middle{
				width: 50%;
				 height: 100%;
				 background-image: linear-gradient(to bottom, #CBCBCB 0%, #CBCBCB 50%, transparent 50%);
				 background-size: 4rpx 36rpx;
				 background-repeat: repeat-y;
				 margin: 10rpx 0;
				 margin-left: 50%;
				}
			}
			.img{
				width:216rpx;
				padding: 12rpx 6rpx;
				img{width: 100%;height: 100%;}
			}
			.content{
				max-width: calc(100% - 260rpx);
				flex: 1;
				display: flex;
				flex-direction: column;
				margin: 27rpx 0 13rpx 0;
				h3{
					font-size: 34rpx;
					span{
						color: #FF772F ;
						background-color: #FFE7CA;
						display: inline-block;
						width: 50rpx;
						border-radius: 6rpx;
					   margin-left: 14rpx;
					  font-size: 22rpx;
					  text-align: center;
					  font-weight: 200;
					}
				}
				.info{
					padding-right:14rpx;
					color: #868686;
					font-size: 26rpx;
					overflow:hidden;
					white-space:nowrap;
					text-overflow:ellipsis;
					flex: 1;
				}
				.pic{
					color: #F43636;
					span{
						font-size: 48rpx;
						padding: 0 9rpx 0 0;
						font-weight: 600;
					}
					label{
						font-size: 26rpx;
						color: #6B6B6B;
					}
				}
			}
		}
	}
</style>