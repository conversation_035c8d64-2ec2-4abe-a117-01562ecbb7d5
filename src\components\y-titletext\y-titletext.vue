<template>
  <view class="titletext" :style="{ padding: `0 ${conf.pageMargin}`, backgroundColor: conf.bgColor }">
    <view class="title" :style="{ justifyContent: conf.position === 'left' ? 'space-between' : 'center' }">
      <text :style="{
        fontSize: conf.titleSize,
        color: conf.titleColor,
        fontWeight: conf.titleWeight === 'bold' ? 'bold' : 'normal',
      }">{{ conf.title }}</text>
      <text v-show="conf.position === 'left' && conf.lookMore === 1" :style="{
        // color: conf.lookMoreColor,
        'font-size': '14px',
      }">{{ conf.lookMoreText }}</text>
    </view>
    <view class="des" :style="{
      color: conf.desColor,
      fontSize: conf.desSize,
      textAlign: conf.position === 'center' ? 'center' : 'left',
      fontWeight: conf.desWeight === 'bold' ? 'bold' : 'normal',
    }">
      {{ conf.description }}
    </view>
    <view v-show="conf.position === 'center' && conf.lookMore === 1" class="more" :style="{ textAlign: 'center' }">
      {{ conf.lookMoreText }}
    </view>
  </view>
</template>

<script setup>
import { ref, toRefs, computed } from "vue";
import { pxToRpx } from "@/utils/tool.js";
const props = defineProps({
  config: {
    type: Object,
    default: () => { },
  },
});

const conf = computed(() => {
  const style = {};
  const { titleSize, desSize, pageMargin } = props.config;
  if (titleSize) style.titleSize = `${pxToRpx(titleSize)}`;
  if (desSize) style.desSize = `${pxToRpx(desSize)}`;
  // 页面间距
  if (pageMargin) {
    style.pageMargin = `${pxToRpx(pageMargin)}`;
  }
  return {
    ...props.config,
    ...style,
  };
});
</script>

<style lang="scss" scoped>
.titletext {
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;

    .more {}
  }

  .des {
    font-size: 14px;
    color: #666;
  }
}
</style>
