import App from "./App"
// 网络请求
import request from "./utils/request.js"

// // #ifndef VUE3
// import Vue from "vue"
// Vue.config.productionTip = false
// App.mpType = "app"
// const app = new Vue({
// 	...App
// })
// app.$mount()
// // #endif
console.log(uni.getStorageSync("ylb-cookie"))
// #ifdef VUE3
import { createSSRApp } from "vue"
import * as Pinia from 'pinia';
export function createApp() {
	const app = createSSRApp(App)
  app.use(Pinia.createPinia());
	app.config.globalProperties.$api = request
	return {
		app,
    Pinia
	}
}
// #endif
