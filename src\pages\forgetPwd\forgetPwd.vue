<template>
	<!-- 头部样式 TODO -->
	<y-nav-bar backgroundColor="#fff" fontColor="#050505" backBtn solid>忘记密码</y-nav-bar>
	<view class="forgetPwd">
		<view class="step">
			<y-step :stepContent="stepContent" :curStep="index"></y-step>
		</view>
		<template v-if="index === 1">
			<forgetInput type="mobile" v-model="forgetParams.mobile" @verify="verify => (disMobile = verify)" />
			<forgetInput type="verificationCode" v-model="forgetParams.verCode" :mobile="forgetParams.mobile"
				:disable="!disMobile" />
		</template>
		<template v-else-if="index === 2">
			<forgetInput type="password" placeholder="输入新密码" v-model="forgetParams.password" />
			<forgetInput type="password" placeholder="再次输入" v-model="forgetParams.rePassword" />
		</template>
		<template v-else-if="index === 3">
			<y-svg class="tent" name="forget-success" />
			<!-- <image :src="`@/static/image/icon_tent${linkSuffix}.png`" mode="widthFix"></image> -->
		</template>
		<y-button @tap="nextStep" :disable="!canStep">{{ stepText }}
			<template v-if="index === 3"> ({{ timeoutNum }}) </template>
		</y-button>
	</view>
</template>

<script>
import forgetInput from "./component/forgetInput.vue"
import w_md5 from "@/js_sdk/zww-md5/w_md5.js"
import request from "@/utils/request.js"
export default {
	components: {
		forgetInput,
		w_md5
	},
	computed: {
		canStep() {
			let isCan = false
			switch (this.index) {
				case 1:
					if (this.disMobile && this.forgetParams.verCode) {
						isCan = true
					}
					break
				case 2:
					if (this.forgetParams.password && this.forgetParams.rePassword) {
						isCan = true
					}
					break
				case 3:
					isCan = true
					this.stepText = "去登录"
					break
			}
			return isCan
		},
		linkSuffix() {
			return ""
		}
	},
	data() {
		return {
			index: 1,
			stepContent: ["验证手机号", "重置密码", "操作成功"],
			disMobile: false,
			forgetParams: {
				mobile: "",
				verCode: "",
				password: "",
				rePassword: ""
			},
			stepText: "下一步",
			timeoutNum: 3
		}
	},

	methods: {
		//下一步
		nextStep() {
			if (!this.canStep) return
			switch (this.index) {
				case 1:
					if (!this.forgetParams.verCode || !this.forgetParams.mobile) return
					request
						.casPost(
							"/user/phone/verifyCode",
							{
								phone: this.forgetParams.mobile,
								phoneCode: this.forgetParams.verCode
							},
							{ intercept: false }
						)
						.then(({ code, message }) => {
							if (code === 20000) {
								this.index++
							} else {
								uni.showToast({
									icon: "error",
									title: message
								})
							}
						})

					break
				case 2: {
					// 重置密码
					if (this.forgetParams.password !== this.forgetParams.rePassword) {
						uni.showToast({
							icon: "error",
							title: "两次密码不一致"
						})
						return
					}
					if (this.forgetParams.password.toString().length < 6) {
						uni.showToast({
							icon: "error",
							title: "请输入不少于 6 位字母、数字、符号或组合"
						})
						return
					}
					// 验证手机号
					const params = {
						code: this.forgetParams.verCode,
						credential: this.forgetParams.mobile,
						newPassword: w_md5.hex_md5_32(this.forgetParams.password),
						type: 2
					}
					request
						.casPut("/password/reset", params, { intercept: false })
						.then(({ code, message }) => {
							if (code === 20000) {
								this.index++
								this.countdown()
							} else {
								uni.showToast({
									icon: "error",
									title: message
								})
							}
						})

					break
				}
				case 3:
					// 操作成功
					Tool.goPage.back()
					break
			}
		},
		// 倒计时
		countdown() {
			if (this.timeoutNum === 1) {
				Tool.goPage.back()
			} else {
				setTimeout(() => {
					this.timeoutNum--
					this.countdown()
				}, 1000)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.forgetPwd {
	overflow: hidden;

	.step {
		margin: 82rpx 60rpx 110rpx;
	}

	.tent {
		display: block;
		width: 313rpx;
		margin: 140rpx auto 0;
	}
}
</style>
