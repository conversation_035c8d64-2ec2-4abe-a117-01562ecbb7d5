<template>
  <scroll-view class="scrollView" v-if="conf.navigationType === '0'" :scroll-x="true" @scroll="
    (event: any) => {
      scroll = event.detail
    }
  ">
    <view class="menu-row">
      <view v-for="item in conf.navigationList" :key="item.src" class="title" @click="goLink(item)"
        :style="{ flexBasis: conf.flexBasis }">
        <image :style="{ borderRadius: conf.borderRadius }" :src="item.src" mode="aspectFit" class="img" />
        <view :style="{
          maxWidth: '134rpx',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          lineHeight: conf.textHeight,
          fontSize: conf.textSize,
          color: conf.textColor,
        }">
          {{ item.text || "&nbsp;" }}
        </view>
      </view>
    </view>
  </scroll-view>
  <view v-if="conf.navigationType == 0 && conf.navigationList.length > 5" style="
      width: 72rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background: #ccc;
      margin: 8rpx auto 0;
    ">
    <span style="
        height: 100%;
        border-radius: 4rpx;
        background: var(--theme-color, #349fff);
        display: block;
      " :style="`width: calc(${[6, 7, 8].includes(conf.navigationList.length) ? 4 : 5
        } / ${conf.navigationList.length} * 72rpx);transform: translateX(calc(${scroll.scrollLeft / scroll.scrollWidth
        } * 72rpx));`"></span>
  </view>
  <view class="menu" v-if="conf.navigationType === '1'">
    <view v-for="item in conf.navigationList" class="title" :key="item.src" :style="{ flexBasis: conf.flexBasis }"
      @click="goLink(item)">
      <image :style="{ borderRadius: conf.borderRadius }" :src="item.src" mode="aspectFit" class="img" />
      <view :style="{
        maxWidth: '134rpx',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        lineHeight: conf.textHeight,
        fontSize: conf.textSize,
        color: conf.textColor,
      }">
        {{ item.text || "&nbsp;" }}
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { goLink, pxToRpx } from "@/utils/tool.js";
import { computed, ref } from "vue";

const scroll: any = ref({});
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      imageList: [],
    }),
  },
});

const conf = computed(() => {
  const style: any = {};
  const {
    navigationType,
    navigationList,
    borderRadius,
    textSize,
    textColor,
    textHeight,
  } = props.config;

  switch (navigationList.length) {
    case 6:
    case 7:
    case 8:
      style.flexBasis = "25%";
      break;
    default:
      style.flexBasis = "20%";
      break;
  }
  // 图片圆角
  if (borderRadius) {
    style.borderRadius = `${pxToRpx(borderRadius)}`;
  }
  // 文字大小
  if (textSize) {
    style.textSize = `${pxToRpx(textSize)}`;
  }
  // 文字颜色
  if (textColor) {
    style.textColor = textColor;
  }
  // 文字高度
  if (textHeight) {
    style.textHeight = `${pxToRpx(textHeight)}`;
  }

  return {
    ...props.config,
    ...style,
  };
});

// 菜单
const menuData = [
  {
    title: "门票预订",
    url: "@/static/image/message/reserve.svg",
    onTap: () => Tool.goPage.push("/pages/ticketList/ticketList"),
  },
  {
    title: "权益卡",
    url: "@/static/image/message/travel_card.svg",
    onTap: () => Tool.goPage.push(`/pages/travelCardList/travelCardList?index=2`),
  },
  {
    title: "活动资讯",
    url: "@/static/image/message/message.svg",
    onTap: () => Tool.goPage.push(`/pages/information/information`),
  },
  {
    title: "活动资讯",
    url: "@/static/image/message/message.svg",
    onTap: () => Tool.goPage.push(`/pages/information/information`),
  },
  {
    title: "活动资讯",
    url: "@/static/image/message/message.svg",
    onTap: () => Tool.goPage.push(`/pages/information/information`),
  },
  {
    title: "活动资讯",
    url: "@/static/image/message/message.svg",
    onTap: () => Tool.goPage.push(`/pages/information/information`),
  },
  {
    title: "活动资讯",
    url: "@/static/image/message/message.svg",
    onTap: () => Tool.goPage.push(`/pages/information/information`),
  },
  {
    title: "攻略游记",
    url: "@/static/image/message/travel_note.svg",
    onTap: () => Tool.goPage.push(`/pages/travelNotes/travelNotes`),
  },
];
</script>
<style lang="scss" scoped>
.scrollView {
  ::-webkit-scrollbar {
    height: 0;
  }
}

.menu {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 0;
  // margin: 20rpx 0 40rpx 0;

  .title {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: 25%;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: bold;
    line-height: 1;
    white-space: nowrap;

    .img {
      height: 110rpx;
      width: 110rpx;
    }

    &:nth-child(n + 5) {
      flex-grow: 0;
    }
  }
}

.menu-row {
  display: flex;

  .title {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: 25%;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: bold;
    line-height: 1;
    white-space: nowrap;

    .img {
      height: 110rpx;
      width: 110rpx;
    }
  }
}
</style>
