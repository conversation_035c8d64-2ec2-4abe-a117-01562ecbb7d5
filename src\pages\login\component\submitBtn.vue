<template>
	<view
		class="submit_btn theme-bg-color-primary"
		:class="{ disable: props.disable }">
		<view class="jt1">
			<view class="jt2"></view>
			<view class="jt3"></view>
		</view>
	</view>
</template>

<script setup>
import { computed } from "vue"
const props = defineProps({
	disable: {
		type: Boolean,
		default: false
	}
})
const isBlueColor = computed(() => {
	return true
})
</script>

<style lang="scss" scoped>
.submit_btn {
	position: absolute;
	bottom: 80rpx;
	left: 50%;
	width: 113rpx;
	height: 113rpx;
	transform: translate(-50%, 50%);
	border-radius: 50%;
	&.disable {
		opacity: 0.5;
	}
	.jt1 {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translateX(-50%) rotate(180deg);
		background-color: #fff;
		width: 53rpx;
		height: 8rpx;
		border-radius: 6rpx;
		.jt2 {
			position: absolute;
			// top: 50%;
			left: 2rpx;
			transform: rotate(-45deg);
			transform-origin: 0 4rpx;
			background-color: #fff;
			width: 30rpx;
			height: 8rpx;
			border-radius: 6rpx;
		}
		.jt3 {
			position: absolute;
			// top: 50%;
			left: 2rpx;
			transform: rotate(45deg);
			transform-origin: 0;
			background-color: #fff;
			width: 30rpx;
			height: 8rpx;
			border-radius: 6rpx;
		}
	}
}
</style>
