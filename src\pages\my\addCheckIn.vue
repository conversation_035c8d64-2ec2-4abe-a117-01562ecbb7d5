<template>
    <view class="travelAddress">
        <view class="search-wrapper">
            <view class="search-container">
                <view class="city-selector" @click="Tool.goPage.push('/pages/locationSearch/locationSearch')">
                    <uni-icons v-if="isLocationing" class="icon spin-animation" type="spinner-cycle" size="15" color="#000"></uni-icons>
                    <template v-if="isLocationing">
                        <text class="city-name">定位中</text>
                    </template>
                    <template v-else>
                        <text class="city-name">{{ locationData.cityName }}</text>
                    </template>
                    <image class="arrow-down" src="@/static/svg/arrow-down.svg" />
                </view>
                <view class="separator"></view>
                <view class="search-input-wrapper">
                    <image class="search-icon" src="@/static/image/travel/seach.svg"/>
                    <input class="search-input" type="text" placeholder="查找地点" @input="input" />
                </view>
            </view>
            <view class="cancel-btn" @click="goPage.back()">取消</view>
        </view>
        <view v-if="addressList.length" class="list">
            <view class="list--item" v-for="(item, index) in addressList" :key="index">
                <view class="list--info">
                    <view class="list--title">{{ item.title }}</view>
                    <view class="list--content">
                        {{ item.category.split(":")[0] }}｜{{ item.address }}
                    </view>
                </view>
                <view class="check-in-btn" @click="checkIn(item)">
                    打卡
                </view>
            </view>
        </view>
        <view v-else class="nullData">
            <image class="icon" src="@/static/image/check-in/address-icon.webp" mode="aspectFit" />
            <view class="text">
                {{ seachValue ? '很抱歉，未找到匹配地点' : '请输入关键词搜索' }}
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { Tool } from '@/utils/tools';
import request from '@/utils/request';
import { onShow } from "@dcloudio/uni-app";
import useLocation from "@/hooks/useLocation"
const { locationInfo, locationStatus, locationInit } = useLocation({
    geocoder: true
})
const addressList = ref([]);
const seachValue = ref();
const locationData = ref({
    cityName: "",
    cityCode: ""
});
const isLocationing = computed(() => {
    return locationStatus.value === 'waiting' && locationData.value.cityName === ""
})

// 页面显示时获取定位信息
onShow(async() => {
    console.log('onShow')
    let info = uni.getStorageSync("selectedCity")
    if (info) {
        info = JSON.parse(info)
    } else {
        await locationInit()
        info = locationInfo.value
    }
    console.log(info)
    locationData.value = info
    uni.setStorageSync("selectedCity", JSON.stringify(info))
});
const userData = ref({})
onMounted(async () => {
    userData.value = await Tool.getUserInfo()
});

const input = Tool.debounce((e) => {
    let parameter = "keyword=" + e.detail.value;
    const cityName = locationData.value.cityName
    if (cityName !== "全国") {
        parameter += "&region=" + cityName;
    }
    
    request
        .post("/navigation/line/common", {
            domain: "https://apis.map.qq.com",
            interfacePath: "/ws/place/v1/suggestion",
            parameter: parameter,
        })
        .then((res) => {
            seachValue.value = e.detail.value
            addressList.value = JSON.parse(res.data).data;
        });
});

const checkIn = async (item) => {
    console.log('打卡地点：', item);
    uni.showLoading({
        title: '打卡中...'
    })
    // 这里添加打卡逻辑
    await request.post('/comment/addTClockIn', {
        name: item.title,
        latitude: item.location.lat,
        longitude: item.location.lng,
        address: item.address,
        city: item.city,
        userId: userData.value.userInfo.userId
    })
    uni.hideLoading()
    Tool.goPage.replace(`/pages/scenic/checkInSuccess?latitude=${item.location.lat}&longitude=${item.location.lng}`)
    // Tool.goPage.back()
};
</script>
<style lang="scss" scoped>
@keyframes spin {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}

.icon.spin-animation {
    animation: spin 1s linear infinite;
}

.travelAddress {
    width: 100vw;
    height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;

    .search-wrapper {
        display: flex;
        align-items: center;
        padding: 14rpx 30rpx;
        border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
        gap: 20rpx;
        .search-container {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: #f5f4f9;
            border-radius: 44rpx;
            padding: 0 24rpx;
            height: 72rpx;

            .city-selector {
                display: flex;
                align-items: center;
                gap: 8rpx;
                padding-right: 10rpx;
                .city-name {
                    font-size: 28rpx;
                    color: #14131f;
                }
                .arrow-down {
                    width: 30rpx;
                    height: 30rpx;
                }
            }
            .separator {
                width: 2rpx;
                height: 36rpx;
                background-color: #e0e0e0;
            }
            .search-input-wrapper {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 12rpx;
                padding-left: 10rpx;
                .search-icon {
                    width: 32rpx;
                    height: 32rpx;
                    color: #999;
                }
                .search-input {
                    flex: 1;
                    font-size: 28rpx;
                    height: 100%;
                    padding: 0; // reset
                }
            }
        }

        .cancel-btn {
            font-size: 28rpx;
            color: #000;
        }
    }

    .list {
        flex: 1;
        padding: 0 30rpx;
        overflow: auto;

        .list--item {
            padding: 30rpx 0;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .list--info {
                flex: 1;
            }

            .list--title,
            .list--content {
                width: 100%;
                // overflow: hidden;
                // text-overflow: ellipsis;
                // white-space: nowrap;
            }

            .list--title {
                font-weight: bold;
                font-size: 32rpx;
                color: #14131f;
                line-height: 34rpx;
            }

            .list--content {
                margin-top: 20rpx;
                font-size: 26rpx;
                color: rgba(20, 19, 31, 0.8);
                letter-spacing: 1px;
            }

            .check-in-btn {
                background-color: rgba(52, 159, 255, 1);
                border-radius: 30rpx;
                padding: 10rpx 34rpx 10rpx 33rpx;
                color: rgba(255, 255, 255, 1);
                font-size: 26rpx;
                text-align: center;
                white-space: nowrap;
                line-height: 36rpx;
            }
        }

        .list--item:not(:last-child) {
            border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
        }
    }

    .nullData {
        flex: 1;
        overflow: auto;

        .icon {
            display: block;
            margin: 256rpx auto 0;
            width: 460rpx;
        }

        .text {
            text-align: center;
        }
    }
}
</style>
