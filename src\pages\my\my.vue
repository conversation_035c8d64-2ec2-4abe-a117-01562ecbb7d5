<style lang="scss" scoped>
.my-page {
  background-color: #f1f1f1;
  text-align: center;
  padding-bottom: 10px;
  $w: 397rpx;

  .orbit-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 60rpx auto 8rpx;
    width: $w;
    height: $w;
  }

  .orbit {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: $w;
    height: $w;
    border: 3rpx solid rgb(126 211 218 / 26%);
    border-radius: 50%;

    .excircle {
      display: flex;
      justify-content: center;
      align-items: center;
      $w: 343rpx;
      width: $w;
      height: $w;
      border-radius: 50%;
      background-color: #7ed3da;

      .avatar {
        $w: 292rpx;
        width: $w;
        height: $w;
        border-radius: 50%;
      }
    }
  }

  .planet-box {
    position: absolute;
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;

    &.one {
      animation: fadenum 40s ease;
      animation-iteration-count: infinite;
    }

    &.two {
      animation: fadenum 50s infinite;
      animation-iteration-count: infinite;
    }

    &.star {
      animation: fadenum 50s infinite;
      animation-iteration-count: infinite;
    }

    .planet-one {
      position: absolute;
      bottom: 50%;
      right: -12rpx;
      border-radius: 50%;
      width: 14rpx;
      height: 14rpx;
      background: #7ed3da;
      transform: translateX(-50%);
    }

    .planet-two {
      position: absolute;
      top: 50%;
      left: -11rpx;
      border-radius: 50%;
      width: 22rpx;
      height: 22rpx;
      background: #7ed3da;
      transform: translateY(-50%);
      animation: fadenum 10s infinite;
    }

    .planet-star {
      position: absolute;
      top: -10rpx;
      left: 50%;
      border-radius: 50%;
      width: 20rpx;
      height: 20rpx;
      background-color: var(--theme-color);

      // transform: translateX(-50%);


      animation: fadenumgg 10s ease;
      animation-iteration-count: infinite;

      @keyframes fadenumgg {
        100% {
          transform: rotate(360deg);
        }
      }

      @keyframes fadenum {
        100% {
          transform: rotate(360deg);
        }
      }
    }
  }

  .name {
    margin-bottom: 40rpx;
    font-size: 54rpx;
    font-weight: 600;
    line-height: 75rpx;
  }

  .sub {
    margin-bottom: 78rpx;
    font-size: 30rpx;
    font-weight: 400;
    line-height: 42rpx;
    color: #6c6565;
  }

  .my-button {
    display: flex;
    align-items: center;
    margin: 0 auto 60rpx;
    padding: 0 62rpx;
    width: 488rpx;
    height: 90rpx;
    border-radius: 25rpx;
    background-color: #7ed3da;
    font-size: 28rpx;
    color: #fff;

    .icon {
      $w: 60rpx;
      width: $w;
      height: $w;
      margin-right: 16rpx;
    }
  }

  .my-info {
    display: flex;
    height: 340rpx;
    padding: 35rpx 30rpx;
    background-image: url("@/static/image/my/bggg-icon.png");
    background-size: cover;
    text-align: left;
    font-size: 28rpx;

    .out-ring {
      width: 160rpx;
      height: 160rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.82);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin-right: 12rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 3rpx solid rgba(255, 255, 255, 0.82);
      }
    }

    .info-name {
      margin-top: 37rpx;
      font-weight: 600;
      font-size: 36rpx;
    }

    .info-setting {
      width: 56rpx;
      height: 56rpx;
      margin-left: auto;
      margin-top: 35rpx;
    }
  }

  .my-card {
    margin: -50rpx auto 0;
    padding-top: 38rpx;
    width: 690rpx;
    height: 180rpx;
    background-image: url("@/static/image/my/qyk-ban.png");
    background-size: cover;
    border-radius: 12rpx;

    .card-btn {
      margin-left: 52rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 600;
      font-size: 30rpx;
      width: 130rpx;
      height: 53rpx;
      background: linear-gradient(180deg, #ffd689 0%, #e7ac60 100%);
      box-shadow: inset 0px 3rpx 1rpx 1rpx rgba(255, 250, 231, 0.7);
      border-radius: 27rpx;
    }
  }
}
</style>
<template>
  <!-- <y-nav-bar fontColor="#050505" solid>我的</y-nav-bar> -->

  <view class="my-page">
    <view class="my-info">
      <view class="out-ring">
        <image v-if="stase.userInfo.avatar" class="avatar" :src="imgHost + stase.userInfo.avatar" mode="aspectFill">
        </image>
        <image class="avatar" v-else src="@/static/image/default-avatar.png" mode="aspectFill"></image>
        <view class="planet-box star">
          <view class="planet-star"></view>
        </view>
      </view>
      <view>
        <view class="info-name">{{
          stase.userInfo.nickname || stase.userInfo.username || "Elisabeth"
        }}</view>
        <view v-if="stase.userInfo.phone">{{ stase.userInfo.phone }}</view>
      </view>
      <y-svg @click="goCasWeb" class="info-setting" name="setting-icon" />
    </view>
    <view class="my-card" @click="Tool.goPage.push('/pages/travelCardRights/travelCardRights')">
      <view class="card-btn">权益卡</view>
    </view>
    <view style="padding-top: 30rpx; padding-bottom: 100rpx">
      <view class="y-cell" @click="Tool.goPage.push('/pages/myCollect/myCollect')">
        <!-- <image
					class="y-cell-icon"
					src="@/static/image/my/my-collect-icon.png"
					mode="scaleToFill" /> -->
        <y-svg class="y-cell-icon" name="my-collect-icon" />
        <view class="y-cell-title">收藏</view>
        <view class="y-cell-right">
          {{ collectNum }}
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/contactsList/contactsList')">
        <y-svg class="y-cell-icon" name="link-man-icon" />
        <view class="y-cell-title">常用人</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/travelList/travelList')">
        <y-svg class="y-cell-icon" name="my-travel" />
        <view class="y-cell-title">我的行程</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/my/myCheckIn')">
        <y-svg class="y-cell-icon" name="my-check-in-icon" />
        <view class="y-cell-title">我的打卡</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/shippingAddress/shippingAddress')">
        <y-svg class="y-cell-icon" name="address-icon" />
        <view class="y-cell-title">收货地址</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/certification/certification')">
        <y-svg class="y-cell-icon" name="real-name-icon" />
        <view class="y-cell-title">实名认证</view>
        <view class="y-cell-right">
          {{ stase.isAutonym ? "已认证" : "" }}
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view class="y-cell" @click="Tool.goPage.push('/pages/advice/advice')">
        <y-svg class="y-cell-icon" name="feedback-icon" />
        <view class="y-cell-title">意见反馈</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>

      <view class="y-cell" @click="Tool.goPage.push('/pages/my/userAgreement')">
        <y-svg class="y-cell-icon" name="user-agreement-icon" />
        <view class="y-cell-title">用户协议</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
      <view v-if="showContactUs" class="y-cell" @click="Tool.goPage.push('/pages/my/contactUs')">
        <y-svg class="y-cell-icon" name="contact-us-icon" />
        <view class="y-cell-title">联系我们</view>
        <view class="y-cell-right">
          <uni-icons type="right" color="#000" size="20" />
        </view>
      </view>
    </view>

    <!-- <view class="orbit-box">
			<view class="orbit">
				<view class="excircle">
					<image
						v-if="stase.userInfo.avatar"
						class="avatar"
						:src="imgHost + stase.userInfo.avatar"
						mode="aspectFill"></image>
					<image
						class="avatar"
						v-else
						src="@/static/image/default-avatar.png"
						mode="aspectFill"></image>
				</view>
			</view>
			<view class="planet-box one">
				<view class="planet-one"></view>
			</view>
			<view class="planet-box two">
				<view class="planet-two"></view>
			</view>
			<view class="planet-box star">
				<image
					class="planet-star"
					src="@/static/image/star.png"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="name">
			{{ stase.userInfo.nickname || stase.userInfo.username || "Elisabeth" }}
		</view>
		<view
			class="my-button"
			@tap="Tool.goPage.push('/pages/personalCenter/personalCenter')">
			<image class="icon" src="@/static/image/my/my-icon.png" mode=""></image>
			个人中心
		</view>
		<view
			class="my-button"
			@tap="Tool.goPage.push('/pages/travelCardRights/travelCardRights')">
			<image
				class="icon"
				src="@/static/image/my/equities-icon.png"
				mode=""></image>
			权益卡权益
		</view> -->
  </view>
  <!-- <l-customer-service /> -->
</template>
<script setup>
import { toRefs, reactive, ref, onMounted } from "vue";
import { getRoute } from "@/utils/tool.js";
import request from "@/utils/request.js";
import { getEnv } from "@/utils/getEnv";

const collectNum = ref();
const showContactUs = ref(false);
const imgHost = ref(getEnv().VITE_IMG_HOST);
const stase = reactive({
  userInfo: {},
  isAutonym: false,
});
let userData = {};
onMounted(async () => {
  userData = await Tool.getUserInfo(true);
  stase.userInfo = userData.userInfo;
  const { storeId } = getRoute.params();

  if (userData.realNameInfo?.idNumber) {
    //已实名
    stase.isAutonym = true;
  }

  // 是否展示联系我们
  request.get(`/contractStaff/list/${storeId}`).then(({ data }) => {
    showContactUs.value = data.length > 0;
  });

  // 收藏数量
  request.get(`/my/favorites/number?storeId=${storeId}`).then(({ data }) => {
    collectNum.value = data;
  });
});
const goCasWeb = () => {
  Tool.goPage.push(
    `/pages/personalCenter/personalCenter?appId=${uni.getStorageSync("appId")}`
  )
}
</script>
