import pointIcon from '@/static/image/tour/tour_current_position.svg'
// 基于DOMOverlay实现dom点位
function LocaPoint(options) {
  TMap.DOMOverlay.call(this, options);
}
LocaPoint.prototype = new TMap.DOMOverlay();
// 初始化
LocaPoint.prototype.onInit = function(options) {
  this.position = options.position;
};
// 销毁时需解绑事件监听
LocaPoint.prototype.onDestroy = function() {
  this.removeAllListeners();
};
// 创建DOM元素，返回一个DOMElement，使用this.dom可以获取到这个元素
LocaPoint.prototype.createDOM = function() {
  let dom = document.createElement('div');
  let img = document.createElement('img');
  img.src = pointIcon
  dom.classList.add('locaPoint')
  dom.append(img)
  return dom;
};
// 更新DOM元素，在地图移动/缩放后执行
LocaPoint.prototype.updateDOM = function() {
  if (!this.map) {
    return;
  }
  // 经纬度坐标转容器像素坐标
  let pixel = this.map.projectToContainer(this.position);
  // 气泡箭头对齐经纬度坐标点
  let left = pixel.getX() - this.dom.offsetWidth * 0.5 + 'px';
  let top = pixel.getY() - this.dom.offsetHeight * 0.5 + 'px';
  this.dom.style.transform = `translate(${left}, ${top})`;
};
window.LocaPoint = LocaPoint;