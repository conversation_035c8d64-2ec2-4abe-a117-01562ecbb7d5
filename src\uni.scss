/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */
/* 行为相关颜色 */
$color-primary: #7ed3da;
$color-success: #4cd964;
$color-warning: #fab714;
$color-error: #d12e32;

/* 文字基本颜色 */
$text-color-base: #7ed3da;
$text-color-assist: #349fff;
$text-color-black: #3b3c3e;
$text-color-grey: #c5c5c5;
$text-color-white: #ffffff;

/* 背景颜色 */
$bg-color: #fcfcfc;
$bg-color-grey: #f5f5f5;
$bg-color-primary: #dbe7ea;

/* 边框颜色 */
$border-color: #e2e2e2;

/* 尺寸变量 */

/* 文字尺寸 */
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;

/* 图片尺寸 */
$img-size-sm: 40rpx;
$img-size-base: 52rpx;
$img-size-lg: 80rpx;

/* Border Radius */
$border-radius-sm: 4rpx;
$border-radius-base: 6rpx;
$border-radius-lg: 12rpx;
$border-radius-circle: 50%;

/* 水平间距 */
$spacing-row-sm: 10px;
$spacing-row-base: 20rpx;
$spacing-row-lg: 30rpx;

/* 垂直间距 */
$spacing-col-sm: 8rpx;
$spacing-col-base: 16rpx;
$spacing-col-lg: 24rpx;

/* 透明度 */
$opacity-disabled: 0.3;

/* 文章场景相关 */
$color-title: #2c405a;
$font-size-title: 40rpx;
$color-subtitle: #555555;
$font-size-subtitle: 36rpx;
$color-paragraph: #3f536e;
$font-size-paragraph: 30rpx;

$box-shadow: 0 20rpx 20rpx -20rpx
  rgba(
    $color: #333,
    $alpha: 0.1,
  );

/* 动画过度时间 */
$transition-time: 0.2s;

/*每个页面公共css */
@use "@/static/style/app.scss" as *;
