<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>门票预订</y-nav-bar>
	<view class="book">
		<!-- 预定信息 -->
		<l-order-info @onChangeDate="onChangeDate" @onChangeTimePeriodStock="onChangeTimePeriodStock"
			v-model:ticketNum="ticketNum" :ticketInfo="ticketInfo" :numRange="ticketInfo._numRange"
			:type="routerParams.orderType" />
		<!-- 套票信息 -->
		<template v-if="routerParams.orderType === 'compose'">
			<ticketPackageInfo v-for="(item, index) in state.composetTouristInfo" :key="index" :ticketInfo="ticketInfo"
				:goodsList="item" @onChangeComposeTimePeriod="onChangeComposeTimePeriod" />
		</template>
		<!-- 单票/权益卡信息 -->
		<l-tourist-info v-else-if="ticketInfo.isRealName" :ticketNum="ticketNum" v-model="touristList"
			:ticketInfo="ticketInfo" :isOwner="!!ticketInfo.buyOwner" :orderType="routerParams.orderType" />

		<!-- 取票人 -->
		<y-collect-ticket-man edit v-model="collectTicketMan" v-if="routerParams.orderType !== 'travel'" />
		<!-- 联系人弹框 -->
		<y-popup v-model="isPopUpWin" :title="linkPopType === ''
			? '常用人'
			: `${linkPopType === 'edit' ? '编辑' : '新增'}常用人`
			">
			<!-- <l-linkman
				@ensure="ensure"
				v-model:edit="linkPopType"
				v-model:iscontactList="state.iscontactList"></l-linkman> -->
		</y-popup>
		<!-- 占位 -->
		<view class="solid"></view>
		<!-- 合计 -->
		<view class="total-box">
			<view class="left">
				<view class="money">
					<text class="unit">¥</text>
					{{ (ticketInfo._storeGoodsPrice * ticketNum).toFixed(2) }}
				</view>
				<view class="number">共计：{{ ticketNum }} 张</view>
			</view>
			<view class="right" @tap="toPay" v-if="['travel', 'single'].includes(routerParams.orderType)"
				:style="{ background: ticketStock.num ? '#FF9201' : '#c5c5c5' }">
				去下单
				<view class="residue">剩余 {{ ticketStock.num || 0 }} 张</view>
			</view>
			<view class="right" @tap="toPay" v-else>去下单</view>
		</view>
	</view>
</template>
<script setup>
import { reactive, ref, watch, onMounted, computed } from "vue"
import request from "@/utils/request.js"
import { markdownToHtml } from "@/utils/tool.js"
import ticketPackageInfo from "./component/ticketPackageInfo.vue"
import { onLoad } from "@dcloudio/uni-app"

import dayjs from "dayjs"
import { goodsType, ticketType } from "@/utils/constant.js"
import { getEnv } from "@/utils/getEnv";

const { VITE_IMG_HOST, VITE_MALL_HOST } = getEnv()
const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
})
const isPopUpWin = ref(false) //常用人弹窗
const linkPopType = ref("") //联系人修改
const ticketNum = ref(0) //购买的票数
const tourist = { name: "", identity: "", isLinkMan: false }
const touristList = ref([]) // 游客实名列表
const ticketInfo = ref({
	_day: dayjs().format("YYYY-MM-DD")
}) // 请求的票数据
const ticketStock = reactive({
	// 票库存
	num: 0
})
const state = reactive({
	composetTouristInfo: [], //组合票游客信息
	composet: [], //组合商品信息
	composeGoodsInfo: {}, //组合票信息
	tavelCard: {}, //权益卡信息
	place: [], //联系人位置
	iscontactList: [] //已有的联系人列表
})
// 套票信息
const comTicketList = ref([])

// 景区---当前选中的时段信息
const timePeriodField = ref({})

// 组合票 --- 分时时段信息
let timeLaws = reactive({})

// 实名列表随票数而变化
watch(
	() => ticketNum.value,
	(newVal, oldVal) => {
		// 组合票组件
		if (routerParams.orderType !== "compose") return
		const diff = newVal - oldVal
		if (diff > 0) {
			// 新增
			for (let i = 0; i < diff; i++) {
				// 设置套票信息
				const ticketList = []

				state.composet.forEach(element => {
					for (let i = 0; i < element.num; i++) {
						ticketList.push({
							checkType: element.checkType,
							goodsId: element.goodsId,
							title: `${element.proName}-${ticketType[element.proType]}`,
							goodsType: goodsType[element.goodsType],
							isRealName: element.isRealName == 1,
							timeShare:
								element.timeShareId == 0
									? `${element.validityDay}天有效`
									: `${element.timeShareBeginTime}-${element.timeShareEndTime}`,
							realNameInfo: [],
							timeLaws: element?.timeLaws || [],
						})
					}
				})
				state.composetTouristInfo.push(ticketList)
			}
		} else if (diff < 0) {
			// 删除
			state.composetTouristInfo.splice(diff)
		}
	}
)
// 库存变化，更新可购买数量
watch(
	() => ticketStock.num,
	() => {
		setTicketNumRange()
	}
)

// 选择日期
const onChangeDate = async (e, price) => {
	console.log('onChangeDate==', e, price)
	const { orderType } = routerParams
	ticketInfo.value._day = e
	// 如果传入了价格，更新票价
	if (price !== undefined) {
		ticketInfo.value._storeGoodsPrice = price
	}
	if (orderType === "single") {
		if (!ticketInfo.value?.timeLaws?.length) { // 非分时预约走单独的查询接口，分时预约通过 onChangeTimePeriodStock 取 timeLaws 里对应的库存信息
			//单票查询库存
			await getStockInfo()
		}
	} else if (orderType === "compose") {
		console.log(e)
		//组合票查询库存
	}
}
let userData = {}
//取票人
const collectTicketMan = reactive({ name: "", identity: "", mobile: "" })
// 初始化
onMounted(async () => {
	userData = await Tool.getUserInfo()
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { orderType } = routerParams
	//景区门票
	if (orderType === "single") {
		await getTicket()
		await getStockInfo()
	}
	//组合票
	if (orderType === "compose") {
		await getCompose()
	}
	//权益卡
	if (orderType === "travel") {
		await getTavelCard()
	}
	uni.hideLoading()
})
// 检测实名状态
const checkAutonym = () => {
	let isAutonym = true

	if (!userData.realNameInfo?.idNumber) {
		isAutonym = false
		uni.showModal({
			title: "当前登录用户未实名",
			confirmText: "去实名",
			success: obj => {
				if (obj.confirm) {
					Tool.goPage.replace(`/pages/certification/certification`)
				} else if (obj.cancel) {
					Tool.goPage.back()
				}
			}
		})
	}
	return isAutonym
}

// 设置可选入园时间，不可购买过期的票
const setCanBuyDate = endTimeList => {
	const now = dayjs()
	const isTomorrow = endTimeList.some(e =>
		now.isAfter(dayjs(`${now.format("YYYY-MM-DD")} ${e}`))
	)
	const tomorrow = now.add(1, "day").format("YYYY-MM-DD")
	if (isTomorrow && ticketInfo.value._dateRange?.[0] !== tomorrow) {
		ticketInfo.value._dateRange = [tomorrow]
		ticketInfo.value._day = tomorrow
		onChangeDate(tomorrow)
	}
}
// 设置购买数量范围
const setTicketNumRange = () => {
	const { orderType } = routerParams
	let minBuyNum = 1
	let maxBuyNum = ticketStock.num

	if (orderType === "single") {
		// 单票
		if (ticketInfo.value.isPeopleNumber) {
			// 后台配置了人数限制
			minBuyNum = ticketInfo.value.minPeople
			if (ticketInfo.value.maxPeople < maxBuyNum)
				maxBuyNum = ticketInfo.value.maxPeople // 哪个小取哪个
		}
		if (ticketInfo.value.buyOwner) {
			// 仅限本人购买
			minBuyNum = 1
			maxBuyNum = 1
		}
		if (ticketInfo.value.rightsId?.length > 1) {
			// 权益票
			maxBuyNum = 1
		}
	} else if (orderType === "compose") {
		// TODO: 组合票需要查询库存
	} else if (orderType === "travel") {
		// 权益卡
		maxBuyNum = 1
	}
	ticketInfo.value._numRange = [minBuyNum, maxBuyNum]
}
//获取权益卡
const getTavelCard = async () => {
	checkAutonym()
	try {
		const params = {
			isRecommend: false,
			storeGoodsIds: [routerParams.storeGoodsId],
			storeId: routerParams.storeId
		}
		const { data } = await request.get(
			`/appScenic/appTravelGoodsPageList`,
			params
		)
		if (data.data && data.data.length > 0) {
			ticketNum.value = 1
			state.tavelCard = data.data?.[0].travelCardUnitInfo
			ticketStock.num = state.tavelCard.stockAmount //库存
			state.tavelCard.appName = state.tavelCard.goodsName // c 端删品名
			ticketInfo.value = {
				isRealName: true,
				buyOwner: state.tavelCard.buyOwner,
				_title: state.tavelCard.goodsName,
				_storeGoodsPrice: state.tavelCard.salePrice,
				note: "",
				_pic:
					VITE_IMG_HOST + data.data?.[0].goodsBaseInfo.picUrl,
				effectiveTime: state.tavelCard.effectiveTime
			}
			console.log("ticketInfo.value=====")
			console.log(ticketInfo.value)
			//获取预订须知
			const {
				data: { notices }
			} = await request.get(
				`/travelGoods/info/${state.tavelCard.travelGoodsId}`
			)
			ticketInfo.value.note = markdownToHtml(notices)

			if (ticketInfo.value.buyOwner) {
				//仅限本人购买
				touristList.value = [
					{
						name: userData.realNameInfo.idName,
						identity: userData.realNameInfo.idNumber,
						isLinkMan: false
					}
				]
			} else {
				// touristList.value = [{ ...tourist }];
			}

			setTicketNumRange()
		} else {
			throw new Error("暂无权益卡")
		}
	} catch (err) {
		console.log(err)
	}
}
//获取组合票
const getCompose = async () => {
	try {
		const { data } = await request.get(
			`/appTicket/composeGoodsDetail/${routerParams.storeGoodsId}`
		)
		const { composeGoodsInfo, goodsDetail } = data
		let isIncludeOwner = false // 是否包含仅限本人购买的票
		let touristList = []
		const endTimeList = []
		goodsDetail.map(e => {
			if (e.buyOwner) {
				checkAutonym()
				isIncludeOwner = true
			}
			if (e.timeShareEndTime) endTimeList.push(e.timeShareEndTime)
		})

		ticketInfo.value = {
			buyOwner: isIncludeOwner,
			_title: composeGoodsInfo.name,
			_storeGoodsPrice: composeGoodsInfo.totalPrice,
			isRealName: composeGoodsInfo.isRealName,
			note: "",
			_day: dayjs().format("YYYY-MM-DD")
		}
		if (endTimeList.length > 0) setCanBuyDate(endTimeList)
		state.composet = goodsDetail
		state.composeGoodsInfo = composeGoodsInfo

		ticketNum.value = 1
		ticketStock.num = 99
		setTicketNumRange()
	} catch (err) {
		console.log(err)
	}
}

//获取单票
const getTicket = async () => {
	try {
		const { data } = await request.get(
			`/appTicket/goodsInfo/${routerParams.storeGoodsId}`
		)
		ticketInfo.value = data
		ticketNum.value = data.isPeopleNumber ? data.minPeople : 1
		if (data.isRealName) {
			// 仅限本人购买
			checkAutonym()
			if (data.buyOwner) {
				touristList.value[0] = {
					name: userData.realNameInfo.idName,
					identity: userData.realNameInfo.idNumber,
					isLinkMan: false
				}
			}
		}

		// ticketInfo.value._title = `${data.name} + ${goodsType[data.goodsType]}`
		ticketInfo.value._title = data.name
		ticketInfo.value._storeGoodsPrice = data.storeGoodsPrice // 单价
		ticketInfo.value._day = dayjs().format("YYYY-MM-DD")
		ticketInfo.value.buyOwner = !!data.buyOwner
	} catch (err) {
		console.log(err)
	}
}
//单票获取检票规制
const getCheck = async () => { }
//单票获取库存
const getStockInfo = async () => {
	try {
		const { code, data } = await request.get(
			`/appWindows/simpleTicket/stockInfo`,
			{
				day: ticketInfo.value._day,
				storeGoodsId: routerParams.storeGoodsId
			}
		)
		const timeSareList = data.list || []

		if (timeSareList.length > 0) {
			// 分时预约
			const timeShare = timeSareList.find(
				e => e.timeShareId === routerParams.timeShareId
			)
			if (timeShare) {
				ticketStock.num = timeShare.stockAmount || 0
				ticketStock.timeShareBegin = timeShare.beginTime
				ticketStock.timeShareEnd = timeShare.endTime
				// 限制购买时间，不可购买过期的票
				setCanBuyDate([timeShare.endTime])
			}
		} else {
			// 不分时预约
			ticketStock.num = data.stockAmount || 0
		}
		// 设置成分时时段的库存
		if (ticketInfo.value?.timeLaws?.length) {
			ticketStock.num = timePeriodField.value?.stockAmount || 0
		}
		// 购买数量范围
		setTicketNumRange()
	} catch (err) {
		console.log(err)
	}
}
//权益卡下单
const travelCardMergeData = () => {
	const travelCard = e => {
		const _identity = ticketInfo.value.buyOwner ? e.identity : e.idCard;
		return {
			applicationCode: state.tavelCard.collectionCode, // 应用场景
			identityName: e.name,
			identity: _identity,
			productId: state.tavelCard.travelCardId, // 原始产品 ID
			productSkuId: state.tavelCard.travelGoodsId, // 原始商品 id
			buyOwner: ticketInfo.value.buyOwner ? 1 : 0, // 是否本人购买 1:是 0:否
			storeGoodsId: state.tavelCard.storeGoodsId // 产品 skuID  王丹的商品 id
		}
	}
	const returnUrl = `${VITE_MALL_HOST
		}/#/pages/orderDetail/orderDetail?payloading=1&storeId=${routerParams.storeId
		}&orderId=`
	const travelCardList = touristList.value.map(e => travelCard(e))
	return {
		agentId: state.tavelCard.agentId, // 代理商 id
		// 权益卡数据
		travelCardList,
		// 店铺 id
		storeId: routerParams.storeId,
		// 结算回调
		returnUrl
	}
}
//组合票下单数据
const composeMergeData = () => {
	const { composet, composetTouristInfo, composeGoodsInfo } = state
	console.log(composetTouristInfo)
	const { storeId } = routerParams
	// 添加组合票 - 单票信息
	const productTicket = composet.map((n, i) => {
		const aaa = []
		composetTouristInfo.map(item => {
			aaa.push(...item)
		})
		const userList = []
		aaa
			.filter(e => e.goodsId == n.goodsId)
			.forEach(item => {
				if (n.isRealName) {
					item.realNameInfo.forEach(e => {
						userList.push({
							name: e.name,
							identity: e.idCard,
							checkType: n.checkType,
							faceImageUrl: e.faceImageUrl
						})
					})
				}
			})

		// 分时时段
		const timeShareId = (timeLaws?.[n.goodsId]?.[ticketInfo.value._day] || []).filter((_tp) => _tp.active)?.[0]?.timeShareId || '';
		return {
			isComposeSimpleGood: 1,
			// applicationCode: n.collectionCode, // 收款场景编码
			day: dayjs(ticketInfo.value._day).format("YYYY-MM-DD"), // 游玩时间
			num: composetTouristInfo.length * n.num, // 购买数量
			parentProductId: routerParams.storeGoodsId, // 组合票产品 id
			productId: n.goodsId, // 产品 ID
			timeShare: n.timeShareId
				? `${n.timeShareBeginTime}~${n.timeShareEndTime}`
				: "", //分时预约名称
			realNameInfo: userList, // 实名身份证
			productSkuId: n.goodsId, // 产品 skuId
			isCompose: 0, // 是否组合票 1 为组合票，0 为单票
			timeShareId,
		}
	})
	// 添加组合票信息
	productTicket.push({
		isComposeSimpleGood: 0,
		day: dayjs(ticketInfo.value._day).format("YYYY-MM-DD"),
		num: composetTouristInfo.length,
		productId: routerParams.storeGoodsId,
		isCompose: 1 // 是否组合票 1 为组合票，0 为单票
	})
	// 结算回调地址
	const returnUrl = `${VITE_MALL_HOST
		}/#/pages/orderDetail/orderDetail?payloading=1&storeId=${storeId}&orderId=`

	// 获取系统来源
	const sourceType = Tool.getSystemSource()

	return {
		orderType: "JQMP", // 订单类型
		agentId: composeGoodsInfo.distributorId, //代理商 id 供应商
		sourceType, // 渠道
		scenicTicket: {
			pilotName: collectTicketMan.name, // 领票人姓名
			pilotPhone: collectTicketMan.mobile.split(" ").join(""), // 领票人电话
			pilotIdentity: collectTicketMan.identity // 领票人身份证
		},
		productTicket, //产品列表
		storeId, // 店铺 id
		returnUrl
	}
}
//单票下单数据
const mergeData = () => {
	const {
		collectionCode,
		storeGoodsId,
		goodsId,
		rightsId,
		agentId,
		checkType
	} = ticketInfo.value
	const { storeId } = routerParams
	let userList = []
	if (touristList.value.length > 0) {
		userList = touristList.value.map(({ name, idCard, faceImageUrl }) => {
			return { name, identity: idCard, checkType, faceImageUrl }
		})
	}
	let ticketList = {
		isComposeSimpleGood: 0,
		// applicationCode: collectionCode, // 收款场景编码
		day: ticketInfo.value._day, // 游玩时间
		num: ticketNum.value, // 购买数量
		productId: storeGoodsId, // 产品 ID   productId  传 storeGoodsId
		productSkuId: goodsId, // 产品 skuID  productSkuId 传 proId
		isCompose: 0, // 是否组合票 1 为组合票，0 为单票
		timeShare: "", // 分时预约名称
		realNameInfo: userList, // 实名身份证
		rightsGoodsId: routerParams.rightsGoodsId || "", // 权益商品 ID
		timeShareId: timePeriodField.value?.timeShareId || "", // 选中的分时时段 id
	}
	if (ticketStock.timeShareBegin && ticketStock.timeShareEnd) {
		ticketList.timeShare = `${ticketStock.timeShareBegin}~${ticketStock.timeShareEnd}` // 分时预约名称
	}
	// 权益票
	if (rightsId) {
		ticketList.rightsId = rightsId
		ticketList.identity = userData.realNameInfo.idNumber
	}
	// 获取系统来源
	const sourceType = Tool.getSystemSource()
	const params = {
		// orderType: "JQMP", // 订单类型
		agentId, //代理商 id 供应商
		sourceType,
		scenicTicket: {
			pilotName: collectTicketMan.name, // 领票人姓名
			pilotPhone: collectTicketMan.mobile.split(" ").join(""), // 领票人电话
			pilotIdentity: collectTicketMan.identity // 领票人身份证
		},
		productTicket: [ticketList], // //产品列表
		storeId, // 店铺 id
		returnUrl: `${VITE_MALL_HOST
			}/#/pages/orderDetail/orderDetail?payloading=1&storeId=${storeId}&orderId=` // 结算回调
	}
	return params
}
//信息检验
const examine = params => {
	if (!ticketStock.num && !routerParams.storeGoodsId) {
		uni.showModal({
			content: "库存不足",
			showCancel: false
		})
		return false
	}
	let errorInfo = "" //错误信息
	let identityList = []
	let showFaceTip = false //是否展示人脸提示弹窗
	if (routerParams.orderType === "single") {
		//单票
		params.productTicket.map(n => {
			if (n.realNameInfo && n.realNameInfo.length > 0) {
				n.realNameInfo.forEach(e => identityList.push(e.identity))
				n.realNameInfo.forEach(e => {
					const onlyFace = e.checkType.includes(1) && e.checkType.length === 1
					showFaceTip = e.checkType.includes(1) && e.checkType.length > 1
					if (onlyFace && e.faceImageUrl === "") {
						errorInfo = "请上传人脸识别照片"
					}
				})
			}
		})
		if (ticketInfo.value.isRealName) {
			if (identityList.length < ticketNum.value) {
				errorInfo = "请填写完整的身份信息"
			}
		}
	} else if (routerParams.orderType === "compose") {
		//组合票
		params.productTicket.map(n => {
			if (n.realNameInfo && n.realNameInfo.length > 0) {
				n.realNameInfo.map(e => {
					identityList.push(e.identity)
				})
			}
		})
		state.composetTouristInfo.forEach(e => {
			e.forEach(item => {
				if (item.isRealName) {
					if (item.realNameInfo.length === 0) errorInfo = "请填写完整的身份信息"
				}
				const onlyFace =
					item.checkType.includes(1) && item.checkType.length === 1

				if (onlyFace) {
					const needFace = item.realNameInfo.some(t => t.faceImageUrl === "")
					if (needFace) {
						errorInfo = "请上传人脸识别照片"
					}
				}
				if (!showFaceTip) {
					showFaceTip = item.checkType.includes(1) && item.checkType.length > 1
				}
			})
		})
	} else if (routerParams.orderType === "travel") {
		if (params.travelCardList.length === 0) {
			errorInfo = "请选择使用人"
		}
	}

	if (new Set(identityList).size != identityList.length) {
		errorInfo = "身份证重复 当天同一张票一人只能购买一次"
	}

	if (errorInfo) {
		uni.showModal({
			content: errorInfo,
			showCancel: false
		})
	}

	const isPass = errorInfo === ""
	return { isPass, showFaceTip }
}
//去下单
const toPay = async () => {
	if (ticketStock.num == 0) return
	let params
	if (routerParams.orderType === "single") {
		params = mergeData()
	} else if (routerParams.orderType === "compose") {
		params = composeMergeData()
	} else if (routerParams.orderType === "travel") {
		params = travelCardMergeData()
	}

	const { isPass, showFaceTip } = examine(params)
	if (!isPass) return
	if (showFaceTip) {
		await showFaceTipModal(
			"门票支持多种入园方式，若选择人脸入闸，请及时录入人脸信息"
		)
	}
	uni.showLoading({
		mask: true,
		title: "易旅宝"
	})
	try {
		// 权益卡下单 / 普通票
		let url = ""
		if (routerParams.orderType === "travel") {
			url = "/travel/card/create"
		} else {
			url = "/order/create"
		}
		const { code, data } = await request.post(url, params)
		Tool.goPage.push(`/pages/orderDetail/orderDetail?orderId=${data.orderId}`)
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}

function showFaceTipModal(content) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			content: content,
			showCancel: false,
			success: function (res) {
				if (res.confirm) {
					resolve()
				}
			}
		})
	})
}

// 景区，设置分时时段信息，用于修改剩余库存
const onChangeTimePeriodStock = (_timePeriodField) => {
	const { orderType } = routerParams;
	if (orderType === "single") {
		timePeriodField.value = _timePeriodField;
		if (ticketInfo.value?.timeLaws?.length) {
			ticketStock.num = timePeriodField.value?.stockAmount || 0
		}
	}
}

// 组合票 设置分时时段信息，用于创建订单
const onChangeComposeTimePeriod = (_timeLaws) => {
	timeLaws = _timeLaws;
}
</script>

<style lang="scss" scoped>
.book {
	padding: 36rpx 40rpx 0;
	margin-bottom: 180rpx;
	background: linear-gradient(180deg, #bbd9ff 0%, rgba(217, 233, 255, 0) 100%);

	.total-box {
		position: fixed;
		bottom: 0;
		left: 0%;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 140rpx;
		z-index: 3;
		padding-left: 57rpx;
		padding-right: 40rpx;
		background-color: #fff;

		.left {
			.money {
				font-size: 54rpx;
				font-weight: 600;
				color: #f43636;

				.unit {
					font-size: 42rpx;
				}
			}

			.number {
				font-size: 24rpx;
				font-weight: 400;
				color: #050505;
				line-height: 33rpx;
			}
		}

		.right {
			padding: 10rpx 54rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			font-size: 32rpx;
			font-weight: 600;
			color: #ffffff;
			line-height: 45rpx;
			letter-spacing: 1rpx;
			background: #ff9201;
			border-radius: 44rpx;

			.residue {
				font-size: 22rpx;
				font-weight: 400;
				color: #ffffff;
				line-height: 30rpx;
			}
		}
	}
}

.solid {
	height: 140rpx;
}
</style>
