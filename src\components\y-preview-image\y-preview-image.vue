<template>
  <view class="y-preview-image" v-if="visible" @click.stop="close">
    <view class="preview-container" @click.stop>
      <view class="header flex-row justify-between">
        <view class="close-btn" @click="close">
          <svg class="close-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="#ffffff"/>
          </svg>
        </view>
        <text class="counter">{{ currentIndex + 1 }}/{{ mediaItems.length }}</text>
      </view>
      
      <swiper class="image-swiper" :current="currentIndex" @change="handleSwiperChange">
        <swiper-item v-for="(item, index) in mediaItems" :key="index">
          <!-- 图片预览 -->
          <image
            v-if="!item.isVideo"
            class="preview-image"
            :src="item.url"
            mode="aspectFit"
          />
          
          <!-- 视频预览 -->
          <view v-else class="video-container">
            <video
              class="preview-video"
              :src="item.url"
              :autoplay="item.autoplay"
              :controls="item.showControls"
              :muted="item.muted"
              :loop="true"
              object-fit="contain"
              @play="handleVideoPlay(index)"
              @pause="handleVideoPause(index)"
              @ended="handleVideoEnded(index)"
              @fullscreenchange="handleFullscreenChange"
            ></video>
            
            <!-- 自定义视频控制条 -->
            <view class="video-controls" v-if="item.isVideo && !item.showControls">
              <!-- <view class="play-btn" @click.stop="toggleVideoPlay(index)">
                <uni-icons 
                  :type="item.playing ? 'pause-filled' : 'play-filled'" 
                  size="60" 
                  color="#fff"
                ></uni-icons>
              </view> -->
            </view>
          </view>
        </swiper-item>
      </swiper>
      
      <view class="footer flex-col" v-if="userInfo">
        <text class="username">{{ userInfo.userName }}</text>
        <view class="score-row flex-row">
          <view class="score-badge flex-col">
            <text class="score-value">{{ userInfo.score }}</text>
          </view>
          <text class="score-level">{{ userInfo.scoreLevel }}</text>
          <text class="score-item">景色 {{ userInfo.environmentScore }}</text>
          <text class="score-item">趣味 {{ userInfo.funScore }}</text>
          <text class="score-item">性价比 {{ userInfo.valueScore }}</text>
        </view>
        <text class="comment-content">{{ userInfo.content }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'y-preview-image',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => []
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    userInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentIndex: 0,
      mediaItems: [] // 包含媒体类型信息的数组
    };
  },
  watch: {
    initialIndex(val) {
      this.currentIndex = val;
      this.resetVideosState();
    },
    visible(val) {
      if (val) {
        this.currentIndex = this.initialIndex;
        this.prepareMediaItems();
      } else {
        this.pauseAllVideos();
      }
    },
    images: {
      immediate: true,
      handler(newVal) {
        this.prepareMediaItems();
      }
    }
  },
  methods: {
    // 判断是否为视频
    isVideo(url) {
      if (!url) return false;
      const videoExtensions = ["mp4", "mov", "avi", "wmv", "flv"];
      const ext = url.split('.').pop().toLowerCase();
      return videoExtensions.includes(ext);
    },
    
    // 准备媒体数据
    prepareMediaItems() {
      this.mediaItems = this.images.map(url => ({
        url,
        isVideo: this.isVideo(url),
        autoplay: false,
        playing: false,
        muted: true,
        showControls: false
      }));
    },
    
    // 重置视频状态
    resetVideosState() {
      this.mediaItems.forEach(item => {
        if (item.isVideo) {
          item.autoplay = false;
          item.playing = false;
          item.showControls = false;
        }
      });
    },
    
    // 暂停所有视频
    pauseAllVideos() {
      this.mediaItems.forEach((item, index) => {
        if (item.isVideo && item.playing) {
          this.pauseVideo(index);
        }
      });
    },
    
    // 切换视频播放状态
    toggleVideoPlay(index) {
      const item = this.mediaItems[index];
      if (item.playing) {
        this.pauseVideo(index);
      } else {
        this.playVideo(index);
      }
    },
    
    // 播放视频
    playVideo(index) {
      const item = this.mediaItems[index];
      if (!item.isVideo) return;
      
      // 暂停其他视频
      this.mediaItems.forEach((otherItem, otherIndex) => {
        if (otherIndex !== index && otherItem.isVideo && otherItem.playing) {
          this.pauseVideo(otherIndex);
        }
      });
      
      // 播放当前视频
      item.autoplay = true;
      item.playing = true;
      item.showControls = false;
      
      // 更新数据
      this.$set(this.mediaItems, index, {...item});
    },
    
    // 暂停视频
    pauseVideo(index) {
      const item = this.mediaItems[index];
      if (!item.isVideo) return;
      
      item.playing = false;
      this.$set(this.mediaItems, index, {...item});
    },
    
    // 视频播放事件
    handleVideoPlay(index) {
      const item = this.mediaItems[index];
      if (item.isVideo) {
        item.playing = true;
        this.$set(this.mediaItems, index, {...item});
      }
    },
    
    // 视频暂停事件
    handleVideoPause(index) {
      const item = this.mediaItems[index];
      if (item.isVideo) {
        item.playing = false;
        this.$set(this.mediaItems, index, {...item});
      }
    },
    
    // 视频结束事件
    handleVideoEnded(index) {
      const item = this.mediaItems[index];
      if (item.isVideo) {
        item.playing = false;
        this.$set(this.mediaItems, index, {...item});
      }
    },
    
    // 全屏变化事件
    handleFullscreenChange(e) {
      // 当退出全屏时恢复控制条
      if (e.detail.fullScreen === false) {
        const index = this.currentIndex;
        const item = this.mediaItems[index];
        if (item.isVideo) {
          item.showControls = false;
          this.$set(this.mediaItems, index, {...item});
        }
      }
    },
    
    close() {
      this.pauseAllVideos();
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    
    handleSwiperChange(e) {
      const oldIndex = this.currentIndex;
      const newIndex = e.detail.current;
      
      // 暂停离开的视频
      const oldItem = this.mediaItems[oldIndex];
      if (oldItem && oldItem.isVideo && oldItem.playing) {
        this.pauseVideo(oldIndex);
      }
      
      this.currentIndex = newIndex;
      
      // 尝试自动播放进入的视频（如果是视频且用户已播放过）
      const newItem = this.mediaItems[newIndex];
      if (newItem && newItem.isVideo && newItem.playing) {
        this.playVideo(newIndex);
      }
      
      this.$emit('change', this.currentIndex);
    }
  }
};
</script>

<style lang="scss" scoped>
.y-preview-image {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .header {
      padding: 60rpx 40rpx 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .close-btn {
        width: 50rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .close-icon {
        width: 40rpx;
        height: 40rpx;
      }
      
      .counter {
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: 600;
      }
    }
    
    .image-swiper {
      flex: 1;
      width: 750rpx;
      
      .preview-image {
        width: 750rpx;
        height: 100%;
      }
      
      .video-container {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .preview-video {
          width: 100%;
          height: 100%;
        }
        
        .video-controls {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.3);
          
          .play-btn {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
    
    .footer {
      padding: 30rpx;
      
      .username {
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: 500;
        margin-bottom: 10rpx;
      }
      
      .score-row {
        margin: 10rpx 0;
        display: flex;
        align-items: center;
        
        .score-badge {
          background-color: rgba(52, 159, 255, 1);
          border-radius: 10rpx 10rpx 0rpx 10rpx;
          padding: 4rpx 11rpx;
          
          .score-value {
            color: #FFFFFF;
            font-size: 24rpx;
            font-weight: 600;
            line-height: 24rpx;
          }
        }
        
        .score-level {
          color: #FFFFFF;
          font-size: 24rpx;
          margin-left: 6rpx;
        }
        
        .score-item {
          color: rgba(187, 187, 187, 1);
          font-size: 24rpx;
          margin-left: 20rpx;
        }
      }
      
      .comment-content {
        color: #FFFFFF;
        font-size: 24rpx;
        line-height: 36rpx;
        margin-top: 15rpx;
        max-height: 180rpx;
        overflow: auto;
      }
    }
  }
}

// 工具类
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}
</style>