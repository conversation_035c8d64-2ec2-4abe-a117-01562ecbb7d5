<template>
  <view v-if="isReady">
    <view class="travel-note">
      <view class="title">攻略游记</view>
      <custom-waterfalls-flow :value="articleList" :listStyle="{
        width: '335rpx',
      }">
      </custom-waterfalls-flow>
      <view v-if="articleList.length === 0">暂无数据 </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import Waterfall from '@/components/custom-waterfalls-flow/custom-waterfalls-flow.vue';
import request from '@/utils/request.js';
import { getRoute } from '@/utils/tool.js';
import dayjs from 'dayjs';
import { defineComponent, onMounted, ref } from 'vue';
import { getEnv } from "@/utils/getEnv";

defineComponent({
  'custom-waterfalls-flow': Waterfall,
});

interface ArticleListItem {
  id: string;
  articleName: string;
  articleAuthor: string;
  publishTime: string;
  readCount: number;
  publicizeImgUrl: string;
  createTime: string;
  articleType: number;
  articleSource: number;
  externalUrl: string;
}
const host = getEnv().VITE_IMG_HOST;
const articleList = ref<ArticleListItem[]>([]);
const isReady = ref(false);

const fetchArticleData = async () => {
  try {
    const params = {
      articleType: '1,4',
      quoteType: 2,
      sortType: 2,
      storeId: getRoute.params().storeId,
    };

    uni.showLoading({
      title: '加载中',
      mask: true,
    });
    const { data = [] } = await request.get(`/article/h5/list`, params);
    articleList.value = data.map((i) => ({
      ...i,
      image: host + (i.publicizeImgUrl || '').split(',')[0],
      createTime: dayjs(i.createTime).format('YYYY.MM.DD'),
    }));
    isReady.value = true;
    uni.hideLoading();
  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  fetchArticleData();
});
</script>
<style lang="scss" scoped>
.travel-note {
  margin: 40rpx 30rpx;

  .title {
    height: 50rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: #14131f;
    line-height: 50rpx;
    margin: 20rpx 0 14rpx 0;
  }
}
</style>
