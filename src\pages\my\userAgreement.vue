<style lang="scss" scoped>
.content {
	padding: 20rpx;
	font-size: 28rpx;
	line-height: 1.5;
}
</style>
<template>
	<view class="content" v-html="markdownToHtml(content)"></view>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue"
import { markdownToHtml } from "@/utils/tool.js"

const content = ref(`用户协议

欢迎您使用我们的产品和服务。在开始使用之前，请仔细阅读以下用户协议。使用我们的产品和服务，即表示您同意遵守以下协议。

### 1. 用户责任
- 您必须对您的账户和密码保密，不得将其透露给他人。
- 您必须对您在使用产品和服务过程中的行为负责，包括但不限于言论、行为和上传内容。
- 您不得利用产品和服务从事违法、违规或侵犯他人权益的行为。

### 2. 服务内容
- 我们将尽最大努力保证产品和服务的稳定性和安全性，但不对由于不可抗力、网络故障等原因造成的服务中断或数据丢失承担责任。
- 我们保留在必要时对产品和服务进行维护和升级的权利。

### 3. 知识产权
- 我们拥有产品和服务中的所有知识产权，包括但不限于软件、源代码、图像、文字和其他内容。
- 未经我们许可，您不得复制、修改、传播或利用这些内容。

### 4. 隐私保护
- 我们将严格保护您的个人信息，不会将其透露给任何第三方。
- 但在法律允许的范围内，我们有权根据相关法律法规披露您的个人信息。

### 5. 其他
- 本协议适用于您使用我们的所有产品和服务。
- 我们有权根据实际情况对本协议进行调整和修改，调整后的协议将在网站上公布并生效。
- 如果您不同意调整后的协议，应立即停止使用我们的产品和服务。

感谢您阅读以上用户协议。如果您有任何疑问或意见，请随时联系我们。祝您使用愉快！`)
</script>
