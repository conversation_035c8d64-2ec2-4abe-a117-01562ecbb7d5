<style lang="scss" scoped>
/* Basic Speech Bubble */
/* 尾巴位于右下角的对话框气泡 */
.bubble {
  position: relative;
  border-radius: 10rpx;
  word-break: break-word;
  overflow: hidden;
  max-width: 600rpx;
  margin-bottom: 20rpx;
  width: fit-content;
  word-wrap: break-word;
  &.me {
    margin-left: auto;
    color: white;
    background: linear-gradient(309deg, #6470FF 0%, #428EFF 100%);
    border-radius: 22rpx 22rpx 0rpx 22rpx;

    &::after {
      position: absolute;
      bottom: -8rpx;
      left: 0;
      display: block;
      width: 100%;
      height: 10rpx;
      margin-bottom: 0rpx;
      background-color: #6470ff;
      content: "";
      clip-path: polygon(94% 0%, 100% 0%, 100% 100%);
    }
  }
  &.tip {
		margin-left: auto;
		background: linear-gradient(309deg, #6470ff 0%, #428eff 100%);
		color: white;
    padding: 16rpx 24rpx;
    font-size: 22rpx;
    overflow: visible;
		&::after {
			content: "";
			display: block; // 使伪元素成为块级元素
			position: absolute;
			bottom: -8rpx;
			width: 100%;
			margin-bottom: 0rpx;
			height: 10rpx;
		}
	}
  &.tip__left {
    border-bottom-right-radius: 0;
    &::after {
			left: 0;
      clip-path: polygon(94% 0%, 100% 0%, 100% 100%);
      background-color: #6470ff;
    }
  }
  &.tip__right {
    border-bottom-left-radius: 0;
    &::after {
			right: 0;
      clip-path: polygon(0% 0%, 0% 100%, 6% 0%);
      background-color: #428eff;
		}
  }

  &.robot {
    color: #14131f;
    background-color: white;
    border-bottom-left-radius: 0;
    padding-top: 0;
    padding-bottom: 0;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0rpx 22rpx 22rpx 22rpx;
    backdrop-filter: blur(12rpx);
    &::after {
      position: absolute;
      bottom: -8rpx;
      left: 0;
      display: block;
      width: 100%;
      height: 10rpx;
      background-color: #fff;
      content: "";
      clip-path: polygon(0% 0%, 6% 0%, 0% 100%);
    }
  }
}

.robot__tip {
  display: flex;
  align-items: center;
  margin-top: 16rpx;

  .robot__tip__content {
    flex: 1;
    color: #8e8e8e;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 33rpx;
  }

  .robot__tip__feedback {
    display: flex;
    flex: none;

    .feedback {
      width: 35rpx;
      height: 35rpx;
      margin-left: 20rpx;
    }
  }
}

.lineee {
  margin-top: 22rpx;
  padding-top: 18rpx;
  color: #349fff;
  font-weight: 500;
  font-size: 28rpx;
  border-top: 1rpx solid #cacaca;
}
</style>

<template>
  <div>
    <!-- 行程规划模块 -->
    <template v-if="type === 'planTip'">
      <tripPlanning :config="params" :type="type" @on-submit="onSubmit" />
    </template>
    <!-- 行程偏好 -->
    <template v-else-if="type === 'tripPreference'">
      <tripPlanning :config="params" :type="type" @on-submit="onSubmit" />
    </template>
    <!-- 商品文章 -->
    <template v-else-if="type === 'articles'">
      <articles :data="params" />
    </template>

    <!-- 用户 -->
    <div v-else-if="isMe" class="bubble me">
      <div style="margin: 18rpx 30rpx">
        <!-- 录音 -->
        <div v-if="type === 'recording'">
          <y-loading-text text="倾听中" v-show="!content" />
          <text v-show="content">{{ content }}</text>
        </div>
        <div v-else>
          
          <span v-html="answer" />
          <image
            v-if="editable"
            style="width: 32rpx"
            src="@/static/image/ai/edit-icon.png"
            mode="widthFix"
          />
        </div>
      </div>
    </div>

    <!-- 气泡提示 -->
    <div v-else-if="isTip">
			<div :class="['bubble', 'tip', isTipPos]">
				<span v-html="answer" />
				<image
					v-if="editable"
					style="width: 32rpx"
					src="@/static/image/ai/edit-icon.png"
					mode="widthFix" />
			</div>
		</div>

    <!-- 机器人 -->
    <div v-else class="bubble robot">
      <div style="margin: 18rpx 30rpx">
        <template v-if="isThinking">
          <y-loading-text text="容我三思" />
        </template>

        <template v-else>
          <div class="markdown_html">
            <div ref="answerRef" v-html="answer" />
          </div>

          <div class="robot__tip" v-if="showTip">
            <div class="robot__tip__content">
              以上内容由 AI 大模型生成，请注意甄别
            </div>
            <!-- <div class="robot__tip__feedback">
						<image
							@click="onFeedback('good')"
							class="feedback"
							:src="feedback === 'good' ? goodActiveIcon : GoodIcon"
							mode="widthFix" />
						<image
							@click="onFeedback('bad')"
							class="feedback"
							:src="feedback === 'bad' ? BadActiveIcon : BadIcon"
							mode="widthFix" />
					</div> -->
					</div>
					<div v-if="goLineBtn.show" class="lineee" @click="goLine(item)">{{ goLineBtn.text }}</div>
				</template>
			</div>

		</div>
	</div>
</template>

<script setup>
import yLoadingText from "@/components/y-loading-text/y-loading-text.vue";
import { markdownToHtml } from "@/utils/tool.js";
import { inject, nextTick, onBeforeUnmount, ref, watch } from "vue";

const getPointList = inject("getPointList");
const props = defineProps({
  isMe: {
    type: Boolean,
    default: false,
  },
  content: {
    type: String,
    default: "",
  },
  // 是否可编辑
  editable: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  // tutu 思考中
  isThinking: {
    type: Boolean,
    default: false,
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true,
  },
  flagList: {
    type: Array,
    default: () => [],
  },
  isGoLine: {
    type: Boolean,
    default: false,
  },
  isRoute: {
    type: Boolean,
    default: true,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  isTip: {
    type: Boolean,
    default: false,
  },
  // 位置
  posX: {
    type: Number,
  },
  posY: {
    type: Number,
  },
});
const emits = defineEmits(["closePop", "onSubmit"]);
const answerRef = ref(null);
const isTipPos = ref('tip__left')
const handleJourneyClick = (event) => {
  const scenicName = event.target.getAttribute("data-name");
  emits("showScenic", scenicName);
};

// 路线按钮
const goLineBtn = computed(() => {
  const { isRoute, params, flagList } = props;
  const btn = {
    show: false,
    text: "",
    line_id: "",
    type: "",
  };



  if (isRoute && flagList.length > 0) {
    const firstFlag = flagList[0];
    btn.type = firstFlag.type;
    const isJourney = firstFlag.type === "journey";
    if (isJourney) {
      btn.show = true;
      // 行程
      btn.text = `查看行程路线`;
    } else {
      // 导览
      btn.show = params.is_route && !!firstFlag.line_id;
      btn.line_id = firstFlag.line_id;
      btn.text = `查看路线详情`;
    }
    console.log('goLineBtngoLineBtngoLineBtn', firstFlag, flagList, btn);
  }

  return btn;
});

const handlePointClick = (event) => {
  const pointKey = event.target.getAttribute("data-point");

  const pointObj = getApp().globalData.pointObj;
  const curPoint = Object.values(pointObj).find(
    (item) => item.point_id === pointKey
  );

  getPointList(curPoint.point_type || 1, curPoint.point_id);
  console.log("curPoint", curPoint);

  Tool.goPage.push(
    `/pages/pointDetail/pointDetail?point_id=${curPoint.point_id}`
  );
  nextTick().then(() => {
    emits("closePop");
  });
};

const goLine = () => {
  const { topic_id, answer_round } = props.params;
  console.log('goLineBtngoLineBtngoLineBtn ', goLineBtn.value);

  if (goLineBtn.value.type === "journey") {
    Tool.goPage.push(
      `/pages/travelDetails/travelDetails?topic_id=${topic_id}&answer_round=${answer_round}`
    );
  } else {
    const { guideId } = Tool.getRoute.params();
    if (guideId)
      Tool.goPage.push(
        `/pages/tourLineDetail/tourLineDetail?guideId=${guideId}&id=${goLineBtn.value.line_id}`
      );
  }
};

// 获取唯一标识字符串
const getUuid = () => {
  return `${Math.random().toString(36).substring(2)}`;
};

const onSubmit = (item) => {
  emits("onSubmit", item);
};

const answer = ref("");
watch(
  () => props.content,
  (val) => {

    val = val.replace(/\[|\]/g, "");
    // 将换行符\n替换为 HTML 的<br/>标签
    val = val.replace(/\n/g, "<br/>");
    let html = val

    const { flagList } = props;
    // console.log("flagList 前序", flagList);
    const flogObj = {};
    if (flagList && flagList.length > 0) {
      // 按长度对关键词进行排序，确保较长的关键词优先匹配
      flagList.sort((a, b) => b.name.length - a.name.length);
      // console.log("flagList 排序", flagList);


      // 将关键词替换为唯一标识字符串，防止重复渲染
      flagList.forEach((item) => {
        const replacement = getUuid();
        const regex = new RegExp(item.name); // 创建一个全局搜索的正则表达式
        html = html.replace(regex, replacement);
        flogObj[replacement] = item;
      });
      // console.log("flagList 替换字典", flogObj);
      html = markdownToHtml(html);
      // console.log("flagList 待替换的", html);
      Object.keys(flogObj).forEach((key) => {
        const item = flogObj[key];
        if (item.type === "journey") {
          html = html.replace(
            new RegExp(key),
            `<span class="tutujourney" data-name="${item.name}">${item.name}</span>`
          );
        } else if (item.type === "tour") {
          html = html.replace(
            new RegExp(key),
            `<span class="tutupoint" data-point="${item.point_id}">${item.name}</span>`
          );
        }
      });

      // const replacements = {}
      // flagList.forEach(item => {
      // 	if (item.type === 'journey') {
      // 		replacements[item.name] = `<span class="tutujourney" data-name="${item.name}">${item.name}</span>`
      // 	} else if (item.type === 'tour') {
      // 		replacements[item.name] = `<span class="tutupoint" data-point="${item.point_id}">${item.name}</span>`
      // 	}
      // })
      // const regex = new RegExp(Object.keys(replacements).join("|"));
      // html = html.replace(regex, (matched) => replacements[matched]);

      // console.log("flagList 替换前", val);
      // console.log("flagList 替换后", html);

      answer.value = html;
      nextTick().then(() => {
        if (answerRef.value) {
          const journeyDoms = answerRef.value.querySelectorAll(".tutujourney");
          if (journeyDoms) {
            journeyDoms.forEach((button) => {
              button.addEventListener("click", handleJourneyClick);
            });
          }
          const pointDoms = answerRef.value.querySelectorAll(".tutupoint");
          if (pointDoms) {
            pointDoms.forEach((button) => {
              button.addEventListener("click", handlePointClick);
            });
          }
        }
      });
    } else {
      answer.value = markdownToHtml(val);
    }
  },
  { immediate: true }
);
watch(
	() => props.posX,
	val => {
    const isLeft = window.innerWidth / val <= 2;
    if (isLeft) {
      isTipPos.value = 'tip__left'
    } else {
      isTipPos.value = 'tip__right'
    }
	},
	{ immediate: true }
);
onBeforeUnmount(() => {
  if (answerRef.value) {
    const pointDoms = answerRef.value.querySelector(".tutupoint");
    const tutujourneyDoms = answerRef.value.querySelector(".tutujourney");
    if (pointDoms) {
      pointDoms.removeEventListener("click", handlePointClick);
    }
    if (tutujourneyDoms) {
      pointDoms.removeEventListener("click", handleJourneyClick);
    }
  }
});

const feedback = ref("");
// AI 生成效果反馈
const onFeedback = (type) => {
  if (feedback.value) return;
  feedback.value = type;
  if (type === "good") {
    console.log("AI 生成效果反馈：好评");
  } else if (type === "bad") {
    console.log("AI 生成效果反馈：差评");
  }
};
</script>
