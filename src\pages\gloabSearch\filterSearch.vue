<template>
  <div class="filterSearch">
    <view class="search_box">
      <view class="input_box">
        <input v-model="searchVal" :maxlength="10" placeholder="" placeholder-class="input-placeholder"
          @input="input" />
        <image src="@/static/image/tour/tour_search.svg" mode="aspectFit" />
        <uni-icons class="del_icon" type="closeempty" size="20" @tap="searchVal = ''" v-if="searchVal"></uni-icons>
      </view>
      <!-- <text @click="Tool.goPage.back()">取消</text> -->
      <view style="margin-left: 16rpx" @click="getList">搜索</view>
    </view>
    <!-- 筛选组件 -->
    <FilterMenu :tabs-list="tabList" v-model:active-tab="currentTab" :sortOptions="['低价优先', '高价优先']"
      :noPrice="[2, 3, 4]" v-model:active-sort="currentSort" />

    <template v-if="cardList.length > 0">
      <view class="container">
        <view class="card" v-for="(item, index) in cardList" :key="index" @click.stop="toUrl(item)">
          <view class="card-img">
            <view class="tag">{{ item.tag }}</view>
            <image :src="item.imageUrl" v-if="item.imageUrl"></image>
            <image src="@/static/image/no_img.png" v-else></image>
          </view>
          <view class="card-content">
            <view class="title-row">
              <text class="title">{{ item.title }}</text>
              <view v-if="item.scenicGradle" class="badge">{{ item.scenicGradle }}A</view>
              <view v-if="item.isRealName == 1" class="realN">实名制</view>
            </view>
            <text v-if="item.goodsName" class="goodsName">{{ item.goodsName }}</text>
            <text class="desc fontHide" v-if="item.desc">{{ item.desc }}</text>
            <template v-if="item.searchType === 'TRAVELOGUE'">
              <view class="publishTime">{{ item.publishTime }}</view>
              <view class="trave">
                <image class="trave_img" src="@/static/image/message/user.svg"></image>
                <text class="articleAuthor">{{ item.articleAuthor }}</text>
              </view>
            </template>
            <view class="rating">
              <text class="scoreAverage" v-if="item.scoreAverage">{{ item.scoreAverage }}</text>
              <text class="commentNumber" v-if="item.commentNumber">{{ item.commentNumber }}条点评</text>
            </view>
            <view class="distance" v-if="item.searchType === 'NAVIGATION'">距您{{ distance || 0 }}公里</view>
            <view class="price-row" v-if="item.price">
              <text style="fong-size: 24rpx">￥</text>
              <text class="price">{{ item.price }} </text>
              <text style="color: #9e9ea3; font-size: 26rpx">起</text>
              <!-- <text class="views" v-if="item.views">{{ item.views }}</text> -->
            </view>
            <view class="price-row" v-if="item.searchType === 'TRAVELOGUE'">
              <view class="lpgut">
                <image class="eye" src="@/static/image/message/eye.svg"></image>
                <view style="">{{ item.readCount }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="noData">
        <image src="@/static/image/icon-noData.png" mode="aspectFit" />
        <text>暂无数据</text>
      </view>
    </template>
  </div>
</template>
<script setup>
import FilterMenu from "@/components/filterMenu.vue/index.vue";
import { ref } from "vue";
import { pointDistance } from "@/utils/tool.js";
import { getEnv } from "@/utils/getEnv";
const imgHost = getEnv().VITE_IMG_HOST;
// 响应式数据
const currentTab = ref(0); // 当前激活的 Tab 索引
const currentSort = ref(0); // 当前激活的排序索引
const searchVal = ref("");
const tabList = ref([
  { label: "综合", value: "ALL" },
  { label: "景区", value: "SCENIC" },
  { label: "门票", value: "TICKET" },
  { label: "组合票", value: "COMPOSE" },
  { label: "权益卡", value: "TRAVEL_CARD" },
  { label: "导览", value: "NAVIGATION" },
  { label: "攻略", value: "TRAVEL_GUIDE" },
  { label: "资讯", value: "INFORMATION" },
  { label: "活动", value: "ACTIVITIES" },
  { label: "游记", value: "TRAVELOGUE" },
]);
const cardList = ref({});
const toUrl = (item) => {
  if (item.searchType === "SCENIC") {
    Tool.goPage.push(`/pages/scenic/scenic?scenicId=${item.scenicId}&storeId=${getRoute.params().storeId}`);
  } else if (item.searchType === "TICKET") {
    Tool.goPage.push(`/pages/book/book?storeGoodsId=${item.goodsId}&orderType=single&timeShareId=0&storeId=${getRoute.params().storeId}`);
  } else if (item.searchType === "COMPOSE") {
    Tool.goPage.push(`/pages/book/book?storeGoodsId=${item.storeGoodsId}&orderType=compose&storeId=${getRoute.params().storeId}`);
  } else if (item.searchType === "TRAVEL_CARD") {
    Tool.goPage.push(`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}&storeId=${getRoute.params().storeId}`);
  } else if (item.searchType === "NAVIGATION") {
    window.open(item.navigationUrl);
  } else if (item.searchType === "TRAVEL_GUIDE" || item.searchType === "INFORMATION" || item.searchType === "ACTIVITIES" || item.searchType === "TRAVELOGUE") {
    Tool.goPage.push(`/pages/articleDetail/articleDetail?id=${item.articleId}&storeId=${getRoute.params().storeId}`);
  }
};
const getList = async () => {
  const res = await request.get(`/appScenic/search/${getRoute.params().storeId}`, {
    keyWord: searchVal.value,
    type: tabList.value[currentTab.value].value,
    sort: currentSort.value == 0 ? -1 : 1,
  });
  if (res.code === 20000) {
    cardList.value = res.data.map((item, index) => {
      return {
        searchType: item.searchType,
        title: filterTitle(item),
        imageUrl: item.picUrl ? imgHost + item.picUrl.split(",")[0] : null,
        tag: tagFilter(item.searchType),
        scenicGradle: item.searchType === "SCENIC" ? item.scenicGradle : null,
        isRealName: item.isRealName || null,
        goodsName: toString(item.goodsType) ? goodsType[item.goodsType] : null,
        desc: item.address,
        scoreAverage: item.scoreAverage >= 0 ? item.scoreAverage : null,
        commentNumber: item.commentNumber >= 0 ? item.commentNumber : null,
        price: item.price >= 0 ? item.price : null,
        articleAuthor: item.articleAuthor || null,
        publishTime: item.publishTime || null,
        readCount: item.readCount,
        distance: item.latitude && item.longitude ? pointDistance([item.latitude, item.longitude]) : null,
        scenicId: item.scenicId,
        goodsId: item.goodsId,
        storeGoodsId: item.storeGoodsId,
        navigationUrl: item.navigationUrl,
        articleId: item.articleId,
        rightsId: item.rightsId,
      };
    });
  }
};
const filterTitle = (item) => {
  const titleObj = {
    SCENIC: "scenicName",
    TICKET: "goodsName",
    COMPOSE: "goodsName",
    TRAVEL_CARD: "goodsName",
    NAVIGATION: "scenicName",
    TRAVEL_GUIDE: "articleName",
    INFORMATION: "articleName",
    ACTIVITIES: "articleName",
    TRAVELOGUE: "articleName",
  };
  return item[titleObj[item.searchType]];
};
const tagFilter = (searchType) => {
  let tabItem = tabList.value.find((item) => {
    return searchType === item.value;
  });
  return tabItem.label;
};
watch(
  () => currentTab.value,
  (i) => {
    getList();
  }
);
watch(
  () => currentSort.value,
  (v) => {
    getList();
  }
);
onMounted(() => {
  if (getRoute.params().searchValue) {
    searchVal.value = getRoute.params().searchValue;
  }
  getList();
});
</script>
<style scoped lang="scss">
.filterSearch {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;

  .search_box {
    width: 100%;
    height: 108rpx;
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid rgba(151, 151, 151, 0.23);
    padding: 0 30rpx;

    .input_box {
      flex: 1;
      height: 60rpx;
      background: #f0f0f0;
      border-radius: 30rpx;
      position: relative;

      >input {
        width: 100%;
        height: 100%;
        padding: 0 20rpx 0 77rpx;
        font-size: 28rpx;

        &.input-placeholder {
          color: #999;
        }
      }

      >image {
        position: absolute;
        top: 50%;
        left: 20rpx;
        transform: translateY(-50%);
        width: 50rpx;
        height: 50rpx;
      }

      .del_icon {
        position: absolute;
        right: 18rpx;
        top: 10rpx;
      }
    }

    // > text {
    //   margin-left: 35rpx;
    //   font-size: 28rpx;
    //   color: #000;
    //   line-height: 40rpx;
    //   white-space: nowrap;
    // }
  }

  .container {
    padding: 30rpx;
    background-color: #f5f5f5;
    overflow-y: scroll;
  }

  .noData {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 50rpx;

    image {
      width: 320rpx;
    }

    text {
      color: #999;
    }
  }

  .card {
    display: flex;
    padding: 20rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .card-img {
      position: relative;
      width: 201rpx;
      height: 182rpx;
      margin-right: 20rpx;
      border-radius: 12rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 6rpx 12rpx;
        font-size: 24rpx;
        color: #fff;
        border-radius: 8rpx;
        background: #000;
        z-index: 9;
      }
    }

    .card-content {
      flex: 1;
      // display: flex;
      flex-direction: column;
      position: relative;
      // justify-content: space-between;

      .title-row {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-bottom: 8rpx;

        .title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          /* 这里是超出几行省略 */
          overflow: hidden;
        }

        .badge {
          padding: 3rpx 5rpx;
          background: #ffe7ca;
          border-radius: 3rpx;
          margin-left: 12rpx;
          font-size: 22rpx;
          color: #ff772f;
        }

        .realN {
          padding: 3rpx 5rpx;
          background: linear-gradient(270deg, #f1b896 0%, #f9ddc7 100%);
          border-radius: 3rpx;
          color: #65300f;
          font-size: 22rpx;
          margin-left: 12rpx;
          white-space: nowrap;
        }
      }

      .desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .publishTime {
        color: #8c8b91;
        font-size: 26rpx;
        margin: 12rpx 0;
      }

      .trave {
        display: flex;
        align-items: center;
        font-size: 24rpx;

        image {
          margin-right: 9rpx;
          width: 36rpx;
          height: 36rpx;
        }
      }

      .goodsName {
        padding: 3rpx 5rpx;
        border-radius: 6rpx;
        border: 2rpx solid #ff9201;
        font-size: 22rpx;
        white-space: nowrap;
        color: #ff9201;
      }

      .rating {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;

        text {
          margin-right: 10rpx;
        }

        .scoreAverage {
          width: 58rpx;
          height: 32rpx;
          line-height: 32rpx;
          text-align: center;
          background: #349fff;
          border-radius: 10px 10px 0px 10px;
          color: #fff;
          font-size: 24rpx;
        }

        .commentNumber {}
      }

      .distance {
        font-size: 26rpx;
        margin-top: 8rpx;
        color: #908f95;
      }

      .price-row {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #ff4d4f;

        .price {
          font-size: 38rpx;
          font-weight: bold;
          margin: 0 3rpx;
        }

        .views {
          font-size: 24rpx;
          color: #999;
        }

        .lpgut {
          display: flex;
          align-items: center;
          color: #89898f;
          font-size: 24rpx;

          .eye {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }
}

.fontHide {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  /* 这里是超出几行省略 */
  overflow: hidden;
}
</style>
