<template>
	<slot>
		<template v-if="selectedContacts.length && !isOne">
			<view class="fast-link">
				<view v-for="item in state.linkmans.slice(0, 3)" :key="item.name" class="fast-box" @click="onSelected(item)"
					:class="item.selected ? 'active' : ''">{{ item.name }}</view>
				<view @click="() => isPopUpWin = true" class="fast-box more">
					<view>新增/选择</view>
					<uni-icons type="forward" color="var(--theme-color)"></uni-icons>
				</view>
			</view>
		</template>
		<template v-if="selectedContacts.length === 0 || isOne">
			<view class="add-btn" @click="isPopUpWin = true">
				<image class="icon" src="@/static/image/order/add-icon.png" mode="scaleToFill" />
				新增游客
			</view>
		</template>
	</slot>

	<y-popup v-model="isPopUpWin">
		<template #header>
			<view class="link-pop__header">
				<uni-icons @click="onBack" class="left" type="left" size="20" color="#000"></uni-icons>
				<view class="title">{{ state.edit.idCard ? "修改" : state.isEdit ? "新增" : "" }}常用人</view>
				<view class="right" @click="newLink">{{ !state.isEdit ? "新增" : "" }}</view>
			</view>
		</template>
		<view style="padding: 0 30rpx; margin-bottom: 140rpx">
			<view v-if="state.isEdit">
				<view class="edit-input-box">
					<view class="name"> 姓&emsp;名 </view>
					<input v-model="state.edit.name" type="text" placeholder="请输入姓名" placeholder-class="placeholder-style" />
				</view>
				<view class="edit-input-box">
					<view class="name"> 身份证 </view>
					<input v-model="state.edit.idCard" maxlength="18" type="text" placeholder="请输入身份证"
						placeholder-class="placeholder-style" />
				</view>
				<view class="edit-input-box">
					<view class="name"> 身份证 </view>
					<input v-model="state.edit.idCard" maxlength="18" type="text" placeholder="请输入身份证"
						placeholder-class="placeholder-style" />
				</view>
				<y-button style="margin-top: 50rpx" @tap="editLinkMan"
					:disable="!state.edit.name || !state.edit.idCard">保存</y-button>
			</view>
			<view v-else style="
					display: flex;
					flex-direction: column;
					height: 100%;
					padding-bottom: 150rpx;
				">
				<view v-if="state.linkmans.length" style="flex: 1; overflow: scroll">
					<view v-for="(n, i) in state.linkmans" :key="n.faceImageUrl" class="radio-group">
						<view :class="n.selected ? 'radio action' : 'radio'" @tap="onSelected(n)">
							<view class="dot"></view>
						</view>
						<image v-if="n.faceImageUrl" class="avatar" :src="imgHost + n.faceImageUrl" mode="aspectFill" />
						<view class="list" @tap="onSelected(n)">
							<view class="name">{{ n.name }}</view>
							<view class="idCard">身份证&nbsp;&nbsp;&nbsp;&nbsp;{{
								`${n.idCard.slice(0, 3)}***********${n.idCard.slice(-4)}`
							}}</view>
						</view>

						<view class="edit" @click="toEdit(i)">
							<y-svg name="edit-icon" style="width: 50rpx" v-if="editable" />
						</view>
					</view>
				</view>
				<y-empty v-else>暂无联系人</y-empty>
				<!-- <div class="option-btn">
					<y-button class="btn" @tap="ensure" :disable="disable">确定</y-button>
				</div> -->
			</view>
		</view>
	</y-popup>
</template>

<script setup>
import {
	toRefs,
	reactive,
	ref,
	watch,
	onMounted,
	onBeforeMount,
	onUpdated,
	onBeforeUnmount,
	onUnmounted
} from "vue"
import request from "@/utils/request.js"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
	// 已选择的联系人
	selectedContacts: {
		type: Array,
		default: () => []
	},
	// 最多可选联系人
	max: {
		type: Number,
		default: 1
	},
	isOne: {
		type: Boolean,
		default: false
	}
})
const imgHost = getEnv().VITE_IMG_HOST
const isPopUpWin = ref(false) //常用人弹窗
const radio = ref(-1) //联系人索引
const disable = ref(true) //按钮禁用点击
const state = reactive({
	linkmans: [], //联系人
	edit: {},
	isEdit: false
})
//获取常用联系人
const getAllContactInfo = async () => {
	try {
		const { data } = await request.get(`/appContract/list`)
		state.linkmans = data.map(e => {
			const curMan = state.linkmans.find(i => i.id === e.id)
			if (curMan) {
				e.selected = curMan.selected
			} else {
				e.selected = false
			}
			return e
		})
		emits(
			"update:selectedContacts",
			state.linkmans.filter(e => e.selected)
		)
	} catch (err) {
		console.info(err)
	}
}
const init = async () => {
	await getAllContactInfo()
}
onMounted(init)
onShow(init)

const emits = defineEmits(["update:linkManList", "onUpdate"])
watch(
	() => props.selectedContacts,
	(newVal, oldVal) => {
		state.linkmans.forEach(e => {
			if (newVal.some(i => i.id === e.id)) {
				e.selected = true
			} else {
				e.selected = false
			}
		})
	},
	{
		deep: true
	}
)

//监听票数变化
watch(
	() => props.max,
	(newVal, oldVal) => {
		let diff = oldVal - newVal
		console.log(diff)
		if (diff > 0) {
			// 票数减少，取消多余的选中状态
			const sList = state.linkmans.filter(e => e.selected)
			if (sList.length > newVal) {
				state.linkmans.forEach(e => {
					if (e.selected && diff-- > 0) {
						onSelected(e)
					}
				})
			}
		}
	},
	{
		deep: true
	}
)

//选中联系人
const onSelected = item => {
	item.selected = !item.selected
	if (item.selected) {
		const sList = state.linkmans.filter(e => e.selected)
		// 只有一个可选人，自动取消其他人的选中状态
		if (props.max === 1) {
			state.linkmans.forEach(e => {
				if (e.id !== item.id) {
					e.selected = false
				}
			})
		} else if (sList.length > props.max) {
			// 限制最多可选
			item.selected = false
			uni.showToast({
				icon: "none",
				title: `最多可选 ${props.max} 个联系人`
			})
			return
		}
	}
	disable.value = false

	emits(
		"update:selectedContacts",
		state.linkmans.filter(e => e.selected)
	)
	ensure()
	console.log(state.linkmans.filter(e => e.selected))
}

//选中联系人
const ensure = async () => {
	if (disable.value) return
	isPopUpWin.value = false
	disable.value = true
}
//修改联系人
const editLinkMan = async () => {
	if (!state.edit.name || !state.edit.idCard) return
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	try {
		let message = ""
		if (state.edit.id) {
			// 修改
			await request.put(`/appContract/info`, state.edit)
			message = "修改成功"
		} else {
			// 新增
			await request.post(`/appContract/info`, state.edit)
			message = "新增成功"
		}
		uni.hideLoading()
		uni.showToast({
			title: message
		})
		state.isEdit = false
		console.log("onUpdateLinkManonUpdateLinkManonUpdateLinkMan")
		emits("onUpdate")
	} catch (err) {
		console.info(err)
	}
}

//删除联系人
const del = async () => {
	try {
		const { code, data } = await request.delete(
			`/appContract/info/${state.edit.id}`
		)
		uni.showToast({
			title: "删除成功"
		})
	} catch (err) {
		console.info(err)
	}
}
//编辑联系人
const toEdit = i => {
	state.isEdit = true
	state.edit = state.linkmans[i]
}

const newLink = async () => {
	// 打开
	// state.isEdit = true
	// state.edit = {
	// 	phone: "",
	// 	name: "",
	// 	idCard: ""
	// }
	Tool.goPage.push("/pages/contactsList/contactsList?page=editPage")
}

// 点击返回
const onBack = () => {
	if (state.isEdit) {
		state.isEdit = false
		return
	}
	isPopUpWin.value = false
}

// 暴露给父组件的方法
defineExpose({
	init
})
</script>

<style lang="scss" scoped>
.radio-group {
	display: flex;
	padding: 30rpx 0;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);

	.radio {
		width: 34rpx;
		height: 34rpx;
		border: 2rpx solid var(--theme-color);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.action {
		.dot {
			width: 18rpx;
			height: 18rpx;
			border-radius: 50%;
			background-color: var(--theme-color);
		}
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-left: 20rpx;
	}

	.list {
		flex: 1;
		padding-left: 34rpx;
		justify-content: flex-start;

		.name {
			font-size: 32rpx;
			font-weight: 700;
			color: #050505;
		}

		.idCard {
			font-size: 28rpx;
			color: #040404;
			margin-top: 5rpx;
			opacity: 0.5;
		}
	}

	.edit {
		color: var(--theme-color);
		font-size: 26rpx;
	}
}

.edit-input-box {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	height: 100rpx;
	font-weight: 400;
	color: #040404;
	border-bottom: 1rpx solid rgba(157, 157, 157, 0.39);

	.name {
		margin-right: 40rpx;
	}
}

.option-btn {
	background-color: #fff;
	display: flex;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;

	.btn {
		flex: 1;
		margin: 20px;
	}
}

.fast-link {
	display: flex;
	justify-content: space-between;

	.fast-box {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 146rpx;
		height: 80rpx;

		background: #fbfbfb;
		border-radius: 8rpx;

		border: 1rpx solid #e1e1e2;
		font-size: 28rpx;
		color: #14131f;

		&.active {
			background: #edf7ff;
			border: 1rpx solid var(--theme-color);
		}

		&.more {
			padding-left: 5rpx;
			font-size: 24rpx;
			color: var(--theme-color);
		}
	}
}

.link-pop__header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid rgb(228, 228, 228, 0.39);

	.left {}

	.title {
		font-size: 36rpx;
		font-weight: 500;
		color: #000;
	}

	.right {
		font-size: 33rpx;
		font-weight: 500;

		color: var(--theme-color);
	}
}

.add-btn {
	color: var(--theme-color);
	font-weight: 400;
	font-size: 28rpx;
	height: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;

	.icon {
		width: 34rpx;
		height: 34rpx;
		margin-right: 18rpx;
	}
}
</style>
