<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>权益卡预定</y-nav-bar>
	<view class="travelCardDetail">
		<view class="travel-card-ticket">
			<view class="img">
				<image v-if="travelCardInfo.picUrl" class="image" mode="aspectFill" :src="travelCardInfo.picUrl">
				</image>
				<view class="image" :style="{ backgroundColor: getRandomColor() }"></view>
				<view v-if="travelCardInfo.travelGoodsStatus === 1" class="boughten">
					已购买
				</view>
				<view v-if="travelCardInfo.travelGoodsStatus != 0" class="valid-date">
					<view>
						有效日期：{{
							travelCardInfo.validityBeginTime &&
							dayjs(travelCardInfo.validityBeginTime).format("YYYY.MM.DD")
						}}
						-
						{{
							travelCardInfo.validityEndTime &&
							dayjs(travelCardInfo.validityEndTime).format("YYYY.MM.DD")
						}}
					</view>
					<view>
						剩余次数：{{
							(travelCardInfo.useFrequencyType &&
								(travelCardInfo.useFrequencyType == 1
									? "不限"
									: travelCardInfo.canUseFrequency)) ||
							"-"
						}}
					</view>
				</view>
			</view>
			<view class="content">
				<view class="left">
					<view class="title">
						{{ travelCardInfo.goodsName }}
					</view>
					<view class="sub">
						<template v-if="state.isCheck">
							<text v-if="travelCardInfo.stateType === '0'">审核进度：<text
									style="color: var(--theme-color)">审核中</text></text>
							<text v-else-if="travelCardInfo.stateType === '1'">审核进度：<text style="color: #ff9201">已拒绝</text></text>
							<text v-else-if="travelCardInfo.stateType === '2'">审核进度：<text
									style="color: var(--theme-color)">已通过</text></text>
							<text v-else>需先提交信息，审核通过后方可购买</text>
						</template>
						<template v-else>
							<text v-if="travelCardInfo.stateType === '2'">审核进度：<text
									style="color: var(--theme-color)">已通过</text></text>
						</template>
					</view>
				</view>
				<view class="right">
					<view v-if="travelCardInfo.travelGoodsStatus === 1">
						<text class="unit">￥</text>
						<text class="num">{{ travelCardInfo.salePrice }}</text>
					</view>
					<!-- 需要审批且没通过的情况 -->
					<view v-else-if="state.isCheck && travelCardInfo.stateType !== '2'">
						<view class="gm-btn" @click="goApproval" v-if="
							travelCardInfo.stateType === '0' ||
							travelCardInfo.stateType === '1'
						">
							重新提交
						</view>
						<view v-else class="gm-btn" @click="goApproval"> 提交信息 </view>
					</view>
					<view v-else-if="[0, 3].includes(travelCardInfo.travelGoodsStatus)" class=" gm-btn" @click="onBuyTravelCard">
						购买
					</view>
					<view v-else-if="travelCardInfo.travelGoodsStatus === 2" class="gm-btn" @click="onBuyTravelCard">
						续费
					</view>
				</view>
			</view>
			<image class="expired-icon" src="@/static/image/ticket-expired.png" mode=""></image>
			<y-popup v-model="isPopUpWin" title="权益说明">
				<view v-if="notice" class="rich-content">
					<rich-text :nodes="notice"></rich-text>
				</view>
				<y-empty v-else>暂无内容</y-empty>
			</y-popup>
		</view>
		<!-- <view class="qy-title" v-if="travelCardList.length > 0"> 权益票 </view>
		<view class="item" v-for="(item, index) in travelCardList" :key="index">
			<view class="title">
				<view class="left">{{ item.goodsName }}</view>
				<view class="right">
					<text class="unit">¥</text>{{ item.storeGoodsPrice
					}}<text class="up">起</text>
				</view>
			</view>
			<view class="merit" v-if="item.labels && item.labels.length > 0">
				{{ item.labels.map(n => n.name).join(" / ") }}
			</view>
			<view style="margin-bottom: 16rpx">
				<text v-if="item.timeShareId != 0" style="margin-right: 20rpx">{{
					`${item.timeShareBeginTime} - ${item.timeShareEndTime}`
					}}</text>
				<text>{{ `${item.validityDay} 天有效` }}</text>
			</view>

			<view class="reserve">
				<view class="left" @tap="booking(item.ticketRemark)">
					<text>预订须知</text>
					<uni-icons type="forward" size="12" color="#1C78E9"></uni-icons>
				</view>
			</view>
			<view class="go-yd" v-if="travelCardInfo.travelGoodsStatus === 1" @click="onBuyTicket(item)">立即预订</view>
			<view class="go-yd no-yd" v-else>立即预订</view>
		</view> -->
		<!-- <y-empty v-if="state.cardList.length==0" class>暂无权益卡</y-empty>
		<view v-if="state.cardList.length!==0 && state.cardList[0].notice" class="product-explain">
			<view class="title">说明信息</view>
			<view class="note" v-html="state.cardList[0].notice" />
		</view> -->
	</view>
	<!-- <y-popup v-model="showPopUp">
		<view v-if="state.notice" v-html="state.notice" class="rich-content"></view>
		<y-empty v-else>暂无内容</y-empty>
	</y-popup> -->
	<y-popup v-model="showPopUp" type="reserve" title="预订须知" @close="handlePopupClose">
		<view class="rich-content">
			<div v-if="state.notice">
				<rich-text :nodes="state.notice"></rich-text>
			</div>
			<y-empty v-else>暂无内容</y-empty>
		</view>
	</y-popup>

	<!-- Tab 切换功能 -->
	<view style="height: 1rpx;background: #f1f1f1;"></view>
	<view style="background: #f1f1f1;overflow: hidden;">
		<view class="tab-container" id="tab-container" :class="{ 'fixed': isFixed }"
			:style="{ top: isFixed ? '0px' : 'auto' }">
			<view class="tab-list">
				<view class="tab-item">
					<text :class="['tab-text', activeTab === 'ticket' ? 'tab-active' : 'tab-inactive']"
						@click="switchTab('ticket')">门票预订</text>
					<view v-if="activeTab === 'ticket'" class="tab-indicator"></view>
				</view>
				<view class="tab-item" style="margin-left: 40rpx;">
					<text :class="['tab-text', activeTab === 'scenic' ? 'tab-active' : 'tab-inactive']"
						@click="switchTab('scenic')">权益景区</text>
					<view v-if="activeTab === 'scenic'" class="tab-indicator"></view>
				</view>
				<view class="tab-item" style="margin-left: 40rpx;">
					<text :class="['tab-text', activeTab === 'description' ? 'tab-active' : 'tab-inactive']"
						@click="switchTab('description')">权益说明</text>
					<view v-if="activeTab === 'description'" class="tab-indicator"></view>
				</view>
			</view>
		</view>
		<view v-if="isFixed" :style="{ height: tabHeight + 'px' }"></view>
		<!-- 门票预订内容区域 -->
		<view id="ticket-content">
			<view class="tab-content-title">
				<text class="title-icon"></text>
				<text class="title-text">权益票</text>
			</view>
			<l-ticket-list v-if="adaptedTicketList && adaptedTicketList.length > 0" :ticketList="adaptedTicketList"
				:isTravelCardPurchased="travelCardInfo.travelGoodsStatus === 1" @submit="handleTicketSubmit"
				@booking="handleTicketBooking">
			</l-ticket-list>
			<view v-else style="background-color: #fff;" class="empty-state">
				<y-empty>暂无权益票</y-empty>
			</view>
		</view>
		<!-- 权益景区内容区域 -->
		<view id="scenic-content">
			<view class="tab-content-title">
				<text class="title-icon"></text>
				<text class="title-text">权益景区</text>
			</view>
			<view v-if="adaptedScenicList && adaptedScenicList.length > 0" class="scenic-list">
				<y-ticket v-for="(scenic, index) in adaptedScenicList" :key="scenic.scenicId || index" :ticket="scenic"
					:mold="0" @click="handleScenicClick(scenic)">
				</y-ticket>
			</view>
			<view v-else class="empty-state">
				<y-empty>暂无权益景区</y-empty>
			</view>
		</view>
		<!-- 权益说明内容区域 -->
		<view id="description-content" class="description-content">
			<view class="tab-content-title">
				<text class="title-icon"></text>
				<text class="title-text">权益说明</text>
			</view>
			<view class="description-wrapper">
				<view class="description-text">
					<rich-text :nodes="descriptionContent"></rich-text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { goodsType } from "@/utils/constant.js"
import request from "@/utils/request.js"
import {
	getRandomColor,
	getRoute,
	objToUrlPath,
	markdownToHtml
} from "@/utils/tool.js"
import { Tool } from "@/utils/tools.ts"
import { onLoad, onShow, onReady, onPageScroll, onHide, onUnload } from "@dcloudio/uni-app"
import dayjs from "dayjs"
import lTicketList from "./component/l-ticket-list.vue"
import yTicket from "@/components/y-ticket/y-ticket.vue"

import { reactive, ref, nextTick, computed, watch } from "vue"
import { getEnv } from "@/utils/getEnv";

// 定义 props
defineProps({
	// 如果需要 props，请在这里添加
})

const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}

	// 禁用浏览器的滚动恢复功能
	if (typeof window !== 'undefined' && 'scrollRestoration' in window.history) {
		window.history.scrollRestoration = 'manual'
		console.log("禁用浏览器滚动恢复功能")
	}

	// 页面加载时立即重置滚动位置，防止浏览器缓存的滚动位置
	// 这是第一层防护，确保页面从顶部开始
	setTimeout(() => {
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0
		})
		console.log("页面加载时重置滚动位置")
	}, 0)
})

onShow(async () => {
	// 页面显示时重置滚动位置到顶部，避免浏览器返回时的滚动位置记忆
	// 使用 nextTick 确保页面渲染完成后再执行滚动
	nextTick(() => {
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0 // 立即滚动，无动画
		})

		// 重置相关状态变量
		scrollTop = 0
		isFixed.value = false
		activeTab.value = 'ticket'

		// 重置位置计算相关变量
		tabOffsetTop.value = 0
		tabHeight.value = 0
		ticketContentTop.value = 0
		scenicContentTop.value = 0
		descriptionContentTop.value = 0

		console.log("页面显示时重置滚动位置和相关状态")
	})

	await init()

	// 页面显示时重新计算位置，处理从其他页面返回的情况
	setTimeout(() => {
		if (travelCardList.value && travelCardList.value.length > 0) {
			setTabPosition()
		}
	}, 500) // 增加延迟，确保滚动重置完成后再计算位置
})

const showPopUp = ref(false)
const state = reactive({
	isCheck: true, // 是否需要审批
	cardList: [], //权益卡
	issueInfo: {}, //出票规则
	Autonym: {}, //实名
	notice: "", //预定须知
	traveInfo: {}
})
const booking = async ticketRemark => {
	uni.hideLoading()
	if (ticketRemark) state.notice = markdownToHtml(ticketRemark)
	showPopUp.value = !showPopUp.value
}

const handlePopupClose = () => {
	showPopUp.value = false
}
// onBeforeMount(async () => {

// });
// 购买权益卡
const onBuyTravelCard = async () => {
	const { storeGoodsId } = travelCardInfo.value
	const urlQuery = {
		storeGoodsId,
		orderType: "travel"
	}
	Tool.goPage.push(`/pages/book/book?${objToUrlPath(urlQuery)}`)
}
// 购买票
const onBuyTicket = item => {
	const urlQuery = {
		storeGoodsId: item.storeGoodsId,
		orderType: "single",
		rightsGoodsId: travelCardInfo.value.travelGoodsId
	}
	if (item.timeShareId) urlQuery.timeShareId = item.timeShareId
	const bookPath = `/pages/book/book?${objToUrlPath(urlQuery)}`
	Tool.goPage.push(bookPath)
}

//初始化
const travelCardInfo = ref({})
const travelCardList = ref([])
const issueInfo = ref({})
const scenicList = ref([])
let userData = {}
const init = async () => {
	userData = await Tool.getUserInfo()
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { data } = await request.get(`/appScenic/rightList`, {
			rightId: routerParams.rightId,
			storeGoodsId: routerParams.storeGoodsId,
			storeId: routerParams.storeId,
			isTravelCardRights: true
		})
		const picUrl = data.travelGoods.goodsBaseInfo.picUrl

		travelCardInfo.value = {
			...data.travelGoods.travelCardUnitInfo,
			picUrl: picUrl ? getEnv().VITE_IMG_HOST + picUrl : ""
		}
		scenicList.value = data.scenicList
		descriptionContent.value = markdownToHtml(data?.travelGoods?.travelCardUnitInfo?.notice || '')

		travelCardList.value = data.list || []
		// 添加标签
		travelCardList.value.forEach(item => {
			item.labels = []
			item.labels.push({
				name: goodsType[item.goodsType]
			})
			if (item.isRealName === "1") {
				item.labels.push({
					name: "实名制"
				})
			}
		})
		console.log(travelCardList.value)

		// 是否需要审批
		const res = await request.get(`/ticketIssue/info`, {
			id: travelCardInfo.value.issueId
		})
		issueInfo.value = res.data
		if (issueInfo.value.isRealName === "1") {
			// 账号需要实名
			if (!userData.realNameInfo?.idNumber) {
				Tool.goPage.replace(`/pages/certification/certification`)
				return
			}
		}
		if (travelCardInfo.value.approveState == 1) {
			// 需要审批
			const params = {
				productId: travelCardInfo.value.travelCardId, // 权益卡 id / 门票产品 id
				idCard: userData.realNameInfo.idNumber, // 身份证 id
				typeProduct: 2, //	1:门票 / 2：权益卡
				unitId: travelCardInfo.value.travelGoodsId //当前购买的 门票商品 id / 权益卡商品 id
			}
			const { data } = await request.put(`/ticketIssue/issueCheck`, params)
			state.isCheck = data.flag !== 5
		} else {
			state.isCheck = false
		}
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
// 去审批页面
const goApproval = async type => {
	try {
		const { travelCardId, issueId, travelGoodsId, storeGoodsId } =
			travelCardInfo.value
		const urlQuery = {
			productId: travelCardId, // 权益卡 id / 门票产品 id
			idCard: userData.realNameInfo.idNumber, // 身份证 id
			typeProduct: 2, //	1:门票 / 2：权益卡
			unitId: travelGoodsId, //当前购买的 门票商品 id / 权益卡商品 id
			storeGoodsId
		}
		Tool.goPage.push(`/pages/approval/approval?${objToUrlPath(urlQuery)}`)
	} catch (err) {
		console.log(err)
	}
}

//收藏
const isCollect = ref(false)
const onCheckCollect = async () => {
	const params = {
		actionType: !isCollect.value ? 1 : 2, //类型：1 - 收藏，2 - 取消收藏
		info: {
			relationId: routerParams.productId, //relationType: 对应的 id- 景区 id、组合商品 id、权益卡 id
			relationType: 3, //类型 1 - 景点，2 - 组合商品 3 - 权益卡
			storeId: getRoute.params().storeId,
			userId: userData.userInfo.userId
		}
	}
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { code, data } = await request.post(`/my/favorites`, params)
	uni.hideLoading()
	uni.showToast({
		icon: "none",
		title: `${!isCollect.value ? "收藏成功" : "取消收藏"}`
	})
	isCollect.value = !isCollect.value
}

// Tab 切换相关变量和方法
const activeTab = ref('ticket')
const isFixed = ref(false)
const tabHeight = ref(0)
const tabOffsetTop = ref(0)
const ticketContentTop = ref(0)
const scenicContentTop = ref(0)
const descriptionContentTop = ref(0)
let scrollTop = 0

// 权益说明模拟数据
const descriptionContent = ref('')



// 为 l-ticket-list 组件适配数据结构
const adaptedTicketList = computed(() => {
	if (!travelCardList.value || travelCardList.value.length === 0) {
		return []
	}

	// 将 travelCardList 转换为 l-ticket-list 期望的格式
	return [{
		pcName: '权益票',
		ticketId: 'travel-card-tickets',
		list: travelCardList.value.map(item => ({
			...item,
			// 确保必要的字段存在
			goodsName: item.goodsName || '',
			storeGoodsPrice: item.storeGoodsPrice || 0,
			validityDay: item.validityDay || 0,
			labels: item.labels || [],
			isDigit: item.isDigit || '0',
			ticketRemark: item.ticketRemark || '',
			storeGoodsId: item.storeGoodsId || '',
			timeShareId: item.timeShareId || 0
		}))
	}]
})

// l-ticket-list 组件的事件处理
const handleTicketBooking = (ticketData) => {
	console.log('Ticket booking:', ticketData)
	booking(ticketData.ticketRemark)
}

const handleTicketSubmit = (ticketData) => {
	if (travelCardInfo.value.travelGoodsStatus !== 1) {
		uni.showToast({
			title: '请先购买旅游卡',
			icon: 'none'
		});
		return;
	}
	console.log('Ticket submit:', ticketData)
	onBuyTicket(ticketData)
}

// 适配景区数据，处理可能为 null 的字段
const adaptedScenicList = computed(() => {
	if (!scenicList.value || scenicList.value.length === 0) {
		return []
	}

	return scenicList.value.map(scenic => ({
		...scenic,
		// 处理价格字段，如果为 null 则显示默认值
		price: scenic.price || 0,
		// 处理评分和点评数量
		scoreAverage: scenic.scoreAverage || null,
		commentNumber: scenic.commentNumber || 0
	}))
})

// 景区点击事件处理
const handleScenicClick = (scenic) => {
	console.log('handleScenicClick', scenic)
	const travelGoodsId = scenic.ticketRightGoodsId.join(',')
	if (travelCardInfo.value.travelGoodsStatus === 1) {
		Tool.goPage.push(`/pages/scenic/scenic?scenicId=${scenic.scenicId}&travelGoodsId=${travelGoodsId}`)
	} else {
		Tool.goPage.push(`/pages/scenic/scenic?scenicId=${scenic.scenicId}&travelGoodsId=${travelGoodsId}&disableTravelCard=1`)
	}
}

const setTabPosition = () => {
	// 防抖处理，避免频繁调用
	if (setTabPosition.timer) {
		clearTimeout(setTabPosition.timer)
	}

	setTabPosition.timer = setTimeout(() => {
		const query = uni.createSelectorQuery()

		// 获取当前滚动位置
		const currentScrollTop = scrollTop || 0

		query.select('#tab-container').boundingClientRect(data => {
			console.log("tab-container position", data)
			if (data && data.top !== undefined) {
				tabHeight.value = data.height || 0
				// 修复：使用绝对位置计算，避免 scrollTop 为 0 的问题
				tabOffsetTop.value = Math.max(0, data.top + currentScrollTop)
				console.log("tabOffsetTop", tabOffsetTop.value, "currentScrollTop", currentScrollTop)

				// 验证计算结果的合理性
				if (tabOffsetTop.value < 0) {
					console.warn("tabOffsetTop计算异常，重置为0")
					tabOffsetTop.value = 0
				}
			}
		}).exec()

		// 获取内容区域位置
		query.select('#ticket-content').boundingClientRect(data => {
			if (data && data.top !== undefined) {
				ticketContentTop.value = Math.max(0, data.top + currentScrollTop)
				console.log("ticketContentTop", ticketContentTop.value)
			}
		}).exec()

		query.select('#scenic-content').boundingClientRect(data => {
			if (data && data.top !== undefined) {
				scenicContentTop.value = Math.max(0, data.top + currentScrollTop)
				console.log("scenicContentTop", scenicContentTop.value)
			}
		}).exec()

		query.select('#description-content').boundingClientRect(data => {
			if (data && data.top !== undefined) {
				descriptionContentTop.value = Math.max(0, data.top + currentScrollTop)
				console.log("descriptionContentTop", descriptionContentTop.value)
			}
		}).exec()
	}, 300) // 减少延迟时间
}

const switchTab = (tab) => {
	activeTab.value = tab;

	// 安全检查：确保位置数据有效
	const safeTabOffsetTop = tabOffsetTop.value || 0
	const safeTabHeight = tabHeight.value || 0

	if (safeTabOffsetTop <= 0) {
		console.warn("tab位置数据无效，重新计算")
		setTabPosition()
		return
	}

	// 先确保 tab 吸顶
	if (!isFixed.value) {
		uni.pageScrollTo({
			scrollTop: safeTabOffsetTop,
			duration: 100,
		});
	}

	// 滚动到对应内容区域
	setTimeout(() => {
		let targetPosition = 0;
		if (tab === 'ticket') {
			targetPosition = Math.max(0, (ticketContentTop.value || 0) - safeTabHeight);
		} else if (tab === 'scenic') {
			targetPosition = Math.max(0, (scenicContentTop.value || 0) - safeTabHeight);
		} else if (tab === 'description') {
			targetPosition = Math.max(0, (descriptionContentTop.value || 0) - safeTabHeight);
		}

		// 确保目标位置有效
		if (targetPosition >= 0) {
			uni.pageScrollTo({
				scrollTop: targetPosition,
				duration: 300,
			});
		}
	}, isFixed.value ? 0 : 150);
}

// 页面滚动监听
onPageScroll(e => {
	scrollTop = e.scrollTop

	// 修复：增加容错处理，避免异常吸顶
	const safeTabOffsetTop = tabOffsetTop.value || 0
	const safeTabHeight = tabHeight.value || 0

	// 只有当 tabOffsetTop 有效值时才进行吸顶判断
	if (safeTabOffsetTop > 0) {
		// 增加一个小的缓冲区，避免边界情况的抖动
		const buffer = 5
		isFixed.value = e.scrollTop >= (safeTabOffsetTop - buffer)
	} else {
		// 如果 tabOffsetTop 无效，则不启用吸顶
		isFixed.value = false
		console.warn("tabOffsetTop 无效，禁用吸顶功能")
	}

	// 根据滚动位置动态更新当前 tab（增加有效性检查）
	const safeDescriptionTop = descriptionContentTop.value || 0
	const safeScenicTop = scenicContentTop.value || 0
	const safeTicketTop = ticketContentTop.value || 0

	if (safeDescriptionTop > 0 && e.scrollTop >= safeDescriptionTop - safeTabHeight - 10) {
		activeTab.value = 'description'
	} else if (safeScenicTop > 0 && e.scrollTop >= safeScenicTop - safeTabHeight - 10) {
		activeTab.value = 'scenic'
	} else if (safeTicketTop > 0 && e.scrollTop >= safeTicketTop - safeTabHeight - 10) {
		activeTab.value = 'ticket'
	}
})

// 在页面准备完成后获取各元素位置
onReady(() => {
	console.log("onReady - setting tab position")

	// 再次确保滚动位置在顶部（最后一层防护）
	uni.pageScrollTo({
		scrollTop: 0,
		duration: 0
	})

	// 重置状态变量
	scrollTop = 0
	isFixed.value = false

	// 延迟执行，确保 DOM 完全渲染和滚动重置完成
	setTimeout(() => {
		setTabPosition()
	}, 500)
})

// 监听 travelCardList 数据变化，重新设置 tab 位置
watch(
	() => travelCardList.value,
	(newVal, oldVal) => {
		// 只有在数据真正变化且有内容时才重新计算位置
		if (newVal && newVal.length > 0 && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
			nextTick(() => {
				console.log("travelCardList changed - updating tab position")
				// 延迟执行，确保 DOM 更新完成
				setTimeout(() => {
					setTabPosition()
				}, 200)
			})
		}
	},
	{
		deep: true,
		immediate: false // 修复：不立即执行，避免初始化时的问题
	}
)

// 页面隐藏时清理定时器，避免内存泄漏
onHide(() => {
	console.log("页面隐藏，清理定时器")
	if (setTabPosition.timer) {
		clearTimeout(setTabPosition.timer)
		setTabPosition.timer = null
	}
})

// 页面卸载时清理所有定时器和状态
onUnload(() => {
	console.log("页面卸载，清理所有状态")
	if (setTabPosition.timer) {
		clearTimeout(setTabPosition.timer)
		setTabPosition.timer = null
	}

	// 恢复浏览器的默认滚动恢复行为
	if (typeof window !== 'undefined' && 'scrollRestoration' in window.history) {
		window.history.scrollRestoration = 'auto'
		console.log("恢复浏览器默认滚动恢复功能")
	}

	// 重置所有状态变量
	scrollTop = 0
	isFixed.value = false
	activeTab.value = 'ticket'
	tabOffsetTop.value = 0
	tabHeight.value = 0
	ticketContentTop.value = 0
	scenicContentTop.value = 0
	descriptionContentTop.value = 0
})

</script>

<style lang="scss" scoped>
.travelCardDetail {
	--radius-size: 28rpx;
	width: 100%;
	padding: 40rpx 40rpx 0 40rpx;
	overflow: auto;
	// height: 100%;
	background-color: #f1f1f1;
	display: flex;
	flex-direction: column;

	.trave-card-info {
		border-radius: var(--radius-size);
		background-color: #fff;

		>.trave-card-info-img {
			width: 100%;
			height: 340rpx;
			border-top-left-radius: var(--radius-size);
			border-top-right-radius: var(--radius-size);
			overflow: hidden;

			.image {
				width: 100%;
				height: 100%;
			}
		}

		>.info {
			padding: 28rpx 30rpx;
			border-bottom-left-radius: var(--radius-size);
			border-bottom-right-radius: var(--radius-size);

			.title {
				display: flex;
				justify-content: space-between;
				font-size: 34rpx;
				font-weight: 500;
				color: #050505;
			}

			.sub-title {
				margin-top: 1rpx;
				font-size: 25rpx;
				font-weight: 400;
				color: #c5c5c5;
			}
		}
	}

	.item {
		position: relative;
		margin-top: 20rpx;
		padding: 30rpx;
		background-color: #fff;
		border-radius: 24rpx;

		.title {
			display: flex;
			justify-content: space-between;

			.left {
				font-size: 32rpx;
				font-weight: 400;
				color: #000000;
			}

			.right {
				display: flex;
				font-size: 42rpx;
				font-weight: 400;
				color: #f43636;
				line-height: 42rpx;

				.unit {
					font-size: 28rpx;
					letter-spacing: 4rpx;
				}

				.up {
					font-size: 28rpx;
					color: #6b6b6b;
				}
			}
		}

		.time {
			font-size: 26rpx;
			font-weight: 400;
			color: #050505;
			line-height: 37rpx;
		}

		.merit {
			display: inline-block;
			padding: 2rpx 8rpx;
			margin: 16rpx 0 16rpx;
			font-size: 22rpx;
			font-weight: 400;
			color: #ff9201;
			line-height: 30rpx;
			border: 1rpx solid #ff9201;
			border-radius: 6rpx;
		}

		.reserve {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;

			.left {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-weight: 400;
				color: #1c78e9;
				line-height: 33rpx;

				.icon {
					width: 14rpx;
					margin-left: 8rpx;
				}
			}
		}

		.go-yd {
			position: absolute;
			bottom: 30rpx;
			right: 30rpx;
			display: inline-block;
			padding: 12rpx 24rpx;
			font-size: 26rpx;
			font-weight: 500;
			color: #ffffff;
			line-height: 37rpx;
			letter-spacing: 1px;
			background: #ff9201;
			border-radius: 34rpx;

			&.no-yd {
				background-color: #dfdfdf;
			}
		}
	}

	.product-explain {
		margin-bottom: 30rpx;
		margin-top: 42rpx;
		border-radius: 24rpx;

		>.title {
			margin-bottom: 20rpx;
			color: #050505;
			font-size: 32rpx;
			font-weight: 500;
		}

		.note {
			margin-bottom: 30rpx;
			padding: 34rpx 30rpx;
			background-color: #fff;
			border-radius: 24rpx;
		}
	}
}

.qy-title {
	font-weight: 600;
	color: #050505;
	font-size: 32rpx;
	font-weight: 500;
}

.travel-card-ticket {
	position: relative;
	--radius-size: 28rpx;
	margin-bottom: 30rpx;
	width: 100%;
	// overflow: hidden;
	height: 466rpx;
	border-radius: var(--radius-size);
	background-color: #fff;
	display: flex;
	flex-direction: column;
	box-shadow: 0rpx 2rpx 9rpx 3rpx rgba(195, 206, 218, 0.31);

	>.img {
		// padding: 12rpx;
		// background: url('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2F1114%2F0QR0103525%2F200QQ03525-1-1200.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1655609440&t=d1c0084aab0e4e66f1cc3864af2fb8a6') no-repeat;
		// background-size: cover;
		position: relative;
		width: 100%;
		height: 340rpx;
		border-radius: var(--radius-size) var(--radius-size) 0 0;
		overflow: hidden;

		.image {
			width: 100%;
			height: 100%;
		}

		.boughten {
			width: 124rpx;
			height: 50rpx;
			position: absolute;
			top: 0;
			right: 0;
			background: linear-gradient(90deg, #f9dab0 0%, #eb9c51 100%);
			border-radius: 0rpx 30rpx 0rpx 30rpx;
			font-size: 22rpx;
			color: #541f0d;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 500;
		}

		.valid-date {
			display: flex;
			justify-content: space-between;
			color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			padding-left: 30rpx;
			padding-top: 60rpx;
			padding-bottom: 10rpx;
			padding-right: 30rpx;
			font-size: 26rpx;
			background: linear-gradient(to top,
					rgba(0, 0, 0, 0.15),
					rgba(255, 0, 0, 0));
		}
	}

	.content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		height: 125rpx;

		.left {
			.title {
				margin-bottom: 10rpx;
				font-size: 34rpx;
				font-weight: 500;
				color: #050505;
				line-height: 34rpx;
			}

			.sub {
				display: flex;
				align-items: center;
				font-size: 20rpx;
				font-weight: 400;
			}
		}

		.right {
			font-size: 26rpx;
			font-weight: 400;
			color: #6b6b6b;

			.unit {
				font-size: 28rpx;
				color: #f43636;
			}

			.num {
				font-size: 42rpx;
				color: #f43636;
			}
		}
	}

	.expired-icon {
		display: none;
		width: 185rpx;
		height: 185rpx;
		position: absolute;
		bottom: 53rpx;
		right: 30rpx;
	}

	.gm-btn {
		position: absolute;
		bottom: 30rpx;
		right: 30rpx;
		display: inline-block;
		padding: 12rpx 24rpx;
		font-size: 26rpx;
		font-weight: 500;
		color: #ffffff;
		line-height: 37rpx;
		letter-spacing: 1px;
		background: #ff9201;
		border-radius: 34rpx;
	}
}

// Tab 切换相关样式
.tab-container {
	background-color: #ffffff;
	padding: 16rpx 30rpx 0 44rpx;
}

.tab-list {
	width: 100%;
	display: flex;
	justify-content: flex-start;
}

.tab-item {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.tab-text {
	font-size: 32rpx;
	letter-spacing: 0.28rpx;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 45rpx;
}

.tab-active {
	color: rgba(23, 24, 26, 1);
}

.tab-inactive {
	color: rgba(153, 153, 153, 1);
}

.tab-indicator {
	background-color: rgba(23, 24, 26, 1);
	border-radius: 2rpx;
	width: 56rpx;
	height: 4rpx;
	margin-top: 15rpx;
}

.tab-container.fixed {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

// Tab 内容标题统一样式
.tab-content-title {
	display: flex;
	align-items: center;
	padding: 30rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #E9E9E9;

	.title-icon {
		width: 8rpx;
		height: 32rpx;
		background: #4787FB;
		border-radius: 4rpx;
		margin-right: 16rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #17181a;
		line-height: 45rpx;
	}
}

// 占位内容样式
.tab-content-placeholder {
	background-color: #ffffff;
	min-height: 100vh;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;

	.placeholder-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 80vh;
		text-align: center;

		.placeholder-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #17181A;
			margin-bottom: 30rpx;
		}

		.placeholder-text {
			font-size: 28rpx;
			color: #666666;
			line-height: 1.6;
			margin-bottom: 20rpx;
			max-width: 600rpx;
		}
	}
}

// 权益景区内容样式
#scenic-content {
	background-color: #fff;
	margin-bottom: 20rpx;

	// min-height: 80vh;
	.scenic-list {
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
}

// 权益说明内容样式
.description-content {
	background-color: #ffffff;

	.description-wrapper {
		padding: 30rpx;

		.description-text {
			font-size: 28rpx;
			color: #333333;
			line-height: 1.8;
		}
	}
}
</style>
